import 'package:stickyqrbusiness/@core/core.dart';

/// App Config Data
class AppConfig {
  /// Get Environment Data
  static AppEnv getEnvironment(AppEnvironment env) {
    ///MAP android: AIzaSyADtb-VnuZY5_aaYJ1XN9TXEnie_82SyOc
    ///MAP ios: AIzaSyAdrpDObD1Z7DPzg9E7VratUgBQBWHFifg
    switch (env) {
      case AppEnvironment.stagging:
        return AppEnv(
          id: AppEnvironment.development,
          name: 'STAG',
          // baseURL: 'https://dev-server.stickyqr.com',
          baseURL: 'https://sandbox.stickyqr.com',
          mapApiKey: 'AIzaSyB-7BPDx1DDKK17u6Krfxu_aO0ZG0oQ05M',
          ablyKey: '6jBXwg.ULa92Q:Nx5ZoKXPTk7H7eLzKNDI8YUdEpp8d1a023wdw1w56-Y',
          getStartedURL: 'https://dev.stickyqr.com',
          managerURL: 'https://bo-dev-v2.stickyqr.com',
          pointClaimsURL: 'https://dev.stickyqr.com',
          email: '<EMAIL>',
          cdnUrl: 'https://cdnd.stickyqr.com/',
          sseURL: 'https://sse-dev.stickyqr.com/events/stream-live-chat',
          sentryDNSUrl: '',
          // sentryDNSUrl: 'https://<EMAIL>/4506787959865344',
        );
      case AppEnvironment.production:
        return AppEnv(
          id: AppEnvironment.production,
          name: 'PROD',
          baseURL: 'https://app.stickyqr.com',
          mapApiKey: 'AIzaSyB-7BPDx1DDKK17u6Krfxu_aO0ZG0oQ05M',
          ablyKey: 'sppmRQ.p5IQZg:tipDH7JO5tU_ZJ2wGy0ymYUr2h2Dj30z3eQpDC1anrs',
          getStartedURL: 'https://stickyqr.com',
          managerURL: 'https://manager.stickyqr.com',
          pointClaimsURL: 'https://stickyqr.com',
          email: '<EMAIL>',
          cdnUrl: 'https://cdn.stickyqr.com/',
          sseURL: 'https://sse.stickyqr.com/events/stream-live-chat',
          sentryDNSUrl: 'https://<EMAIL>/4506787813720064',
        );
      case AppEnvironment.development:
        return AppEnv(
          id: AppEnvironment.development,
          name: 'DEV',
          baseURL: 'https://sandbox.stickyqr.com',
          mapApiKey: 'AIzaSyB-7BPDx1DDKK17u6Krfxu_aO0ZG0oQ05M',
          ablyKey: '6jBXwg.ULa92Q:Nx5ZoKXPTk7H7eLzKNDI8YUdEpp8d1a023wdw1w56-Y',
          getStartedURL: 'https://dev.stickyqr.com',
          managerURL: 'https://bo-dev-v2.stickyqr.com',
          pointClaimsURL: 'https://dev.stickyqr.com',
          email: '<EMAIL>',
          cdnUrl: 'https://cdnd.stickyqr.com/',
          sseURL: 'https://sse-dev.stickyqr.com/events/stream-live-chat',
          sentryDNSUrl: '',
          // sentryDNSUrl: 'https://<EMAIL>/4506787959865344',
        );
      default:
        return AppEnv(
          id: AppEnvironment.local,
          name: 'LOCAL',
          baseURL: 'https://sandbox.stickyqr.com',
          mapApiKey: 'AIzaSyB-7BPDx1DDKK17u6Krfxu_aO0ZG0oQ05M',
          ablyKey: '6jBXwg.ULa92Q:Nx5ZoKXPTk7H7eLzKNDI8YUdEpp8d1a023wdw1w56-Y',
          getStartedURL: 'https://dev.stickyqr.com',
          managerURL: 'https://bo-dev-v2.stickyqr.com',
          pointClaimsURL: 'https://dev.stickyqr.com',
          email: '<EMAIL>',
          cdnUrl: 'https://cdnd.stickyqr.com/',
          sseURL: 'https://sse-dev.stickyqr.com/events/stream-live-chat',
          sentryDNSUrl: '',
          // sentryDNSUrl: 'https://<EMAIL>/4506787959865344',
        );
    }
  }

  static final StorageKeys localAccessToken = StorageKeys(key: 'STICKYQR_ACT');
  static final StorageKeys localRefreshToken = StorageKeys(key: 'STICKYQR_RFT');
  static final StorageKeys localExpiresToken = StorageKeys(key: 'STICKYQR_EPT');
  static final StorageKeys localUserId = StorageKeys(key: 'STICKYQR_USDID');
  static final StorageKeys localFCMTopicBID = StorageKeys(key: 'STICKYQR_FCM_BID');
  static final StorageKeys localFCMTopicBIDUID = StorageKeys(key: 'STICKYQR_FCM_BID_UID');
  static final StorageKeys localUserData = StorageKeys(key: 'STICKYQR_USR', isJsonObject: true);
  static final StorageKeys localUserPermission = StorageKeys(key: 'STICKYQR_PERMISSION', isJsonObject: true);
  static final StorageKeys localBusinessData = StorageKeys(key: 'STICKYQR_BSR', isJsonObject: true);
  static final StorageKeys localBusinessMultipleHomeData = StorageKeys(key: 'STICKYQR_MULTIPLE_HOME', isJsonObject: true);
  static final StorageKeys localPrintersData = StorageKeys(key: 'STICKYQR_BUS_PRINTERS', isJsonObject: true);
  static final StorageKeys localLabelsData = StorageKeys(key: 'STICKYQR_BUS_LABELS', isJsonObject: true);
  static final StorageKeys localLabelsOfferData = StorageKeys(key: 'STICKYQR_BUS_LABELS_OFFER', isJsonObject: true);
  static final StorageKeys localLanguageData = StorageKeys(key: 'STICKYQR_BUS_LANGUAGE', isJsonObject: true);
  static final StorageKeys localRoleIdBusiness = StorageKeys(key: 'STICKYQR_BUS_ROLE_ID');
  static final StorageKeys localStartedGuide = StorageKeys(key: 'STICKYQR_BUS_STARTED_GUIDE');
  static final StorageKeys localFirstScreen = StorageKeys(key: 'STICKYQR_BUS_FIRST_SCREEN');
  static final StorageKeys localFirstMultiHomeScreen = StorageKeys(key: 'STICKYQR_BUS_FIRST_MULTIPE_HOME');
  static final StorageKeys localFirstNewHomeScreen = StorageKeys(key: 'STICKYQR_BUS_FIRST_NEW_HOME');
  static final StorageKeys localHideProPlan = StorageKeys(key: 'STICKYQR_BUS_HIDE_PRO_PLAN');
  static final StorageKeys localFeatures = StorageKeys(key: 'STICKYQR_FEATURES', isJsonObject: true);
  static final StorageKeys localAISupport = StorageKeys(key: 'STICKYQR_AI_SUPPORT', isJsonObject: true);
  static final StorageKeys localHasShowBubble = StorageKeys(key: 'STICKYQR_AI_HAS_SHOW_BUBBLE'); //has_shown_bubble
  static final StorageKeys localCustomerReceipt = StorageKeys(key: 'STICKYQR_BUS_CUSTOMER_RECEIPT', isJsonObject: true);
  static final StorageKeys localKitchenReceipt = StorageKeys(key: 'STICKYQR_BUS_KITCHEN_RECEIPT', isJsonObject: true);

  // static final StorageKeys localTest = StorageKeys(key: 'STICKYQR_TEST');

  static const String iosAppID = '';
  static const String androidAppID = '';

  /// FCM KEYS
  static const String fcmAndroidNewVersion = 'ANDROID_NEW_VERSION';
  static const String fcmIOSNewVersion = 'IOS_NEW_VERSION';

  static const Set<String> fcmKeys = {
    fcmAndroidNewVersion,
    fcmIOSNewVersion,
  };

  static Set<String> fcmKeysAllowedTap = {
    fcmAndroidNewVersion,
    fcmIOSNewVersion,
  };
}
