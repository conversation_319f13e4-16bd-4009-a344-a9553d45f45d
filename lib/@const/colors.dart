// ignore_for_file: use_full_hex_values_for_flutter_colors

import 'package:flutter/material.dart';

///
/// <PERSON><PERSON><PERSON> sắc được định nghĩa ở đây
/// Nếu màu chung thì đặt tên chung
/// Nếu màu áp dụng cho từng page thì đặt: `pageName + "ColorName"`
///
class AppColors {
  AppColors._();

  /// GENERAL
  static const Color appColor = Color(0xFFFF4F0F);
  static const Color cursorColor = Color(0xFFFB7A7C);
  static const Color darkPrimaryBackgroundColor = Color(0xFF2C2C2C);
  static const Color lightPrimaryBackgroundColor = Colors.white;
  static const Color primaryBackColor = Color(0xFF757575);
  static const Color iconColor = Color(0xFF999CA0);
  static const Color appImageBackgroundColor = Color(0xFFE9E9E9);
  static const Color appImageIconColor = Color(0xFFA1A1A1);
  static const Color appTransparentColor = Colors.transparent;
  static const Color appBottomSheetTopLineColor = Color(0xFFC4C4C4);
  static const Color appBottomTextLineColor = Color(0xFFD5D9DC);
  static const Color appTextColor = Color(0xFF333333);
  static const Color appBlueColor = Color(0xFF4E46B4);
  static const Color appErrorColor = Colors.red;
  static const Color appSuccess = Colors.green;
  static const Color appBGAvatarColor = Color(0xFFF5F5F5);
  static const Color appBGGreyColor = Color(0xFFE2E2E2);
  static const Color appLabelTextColor = Color(0xFF999CA0);
  static const Color appBorderColor = Color(0xFFE0E0E0);
  static const Color appBGInputCodeScanColor = Color(0x8E000000);
  static const Color appResendColor = Color(0xFF3448F0);
  static Color appResendColorDisable = const Color(0xFF3448F0).withValues(alpha: .5);
  static const Color appBlackColor = Colors.black;
  static const Color appPrintingBG = Color(0xFF444444);
  static const Color homePrintBGColor = Color(0xFF525252);
  static const Color sliderColor = Color(0xFF949494);
  static const Color cbDisableTitleColor = Color(0xFFB2B2B2);
  static const Color signageBorderTemplateColor = Color(0xFFFFC100);
  static const Color signageTemplateBG5Color = Color(0xFF8E0000);
  static const Color signageTemplateBG6Color = Color(0xFF35BDB3);
  static const Color homeShowQRBGColor = Color(0xFF595D62);
  static const Color cupertinoSwitchColor = Color(0xFF23A037);

  /// HOMEPAGE
  static const Color darkBoxBackgroundColor = Color(0xFF363636);
  static const Color lightBoxBackgroundColor = Color(0xFFF9F9F9);
  static const Color homeLeftBackgroundColor = Color(0xFFF4F4F4);
  static const Color homeBGColor = Color(0xFFE0E0E0);
  static const Color homeBorderColor = Color(0xFFEBEBEB);
  static const Color homeBGUpgradeColor = Color(0xFFFFF4DD);
  static const Color homeBGUpgradeButtonColor = Color(0xFFD2BD91);
  static const Color homeBGSnackBarErrorColor = Color(0xFFDF1C41);

  static const MaterialColor appPrimarySwatch = MaterialColor(
    _appredPrimaryValue,
    {
      50: Color(0xFFFFFFFFF),
      100: Color(0xFFFFFFFFF),
      200: Color(0xFFFFFFFFF),
      300: Color(0xFFFFFFFFF),
      400: Color(0xFFFFFFFFF),
      500: Color(0xFFFFFFFFF),
      600: Color(0xFFFFFFFFF),
      700: Color(0xFFFFFFFFF),
      800: Color(0xFFFFFFFFF),
      900: Color(0xFFFFFFFFF),

      // 50: Color(0xFFFFFFFFF),
      // 100: Color(0xFFFDC6C7),
      // 200: Color(0xFFFCA0A2),
      // 300: Color(0xFFFB7A7C),
      // 400: Color(0xFFFA5E60),
      // 500: Color(0xFFFF4F0F),
      // 600: Color(0xFFF83B3E),
      // 700: Color(0xFFF73235),
      // 800: Color(0xFFF62A2D),
      // 900: Color(0xFFF51C1F),
    },
  );
  static const MaterialColor appPrimary = MaterialColor(
    0xFFFF4F0F,
    {
      50: Color(0xFFFFFFFFF),
      100: Color(0xFFFFFFFFF),
      200: Color(0xFFFFFFFFF),
      300: Color(0xFFFFFFFFF),
      400: Color(0xFFFFFFFFF),
      500: Color(0xFFFFFFFFF),
      600: Color(0xFFFFFFFFF),
      700: Color(0xFFFFFFFFF),
      800: Color(0xFFFFFFFFF),
      900: Color(0xFFFFFFFFF),

      // 50: Color(0xFFFFFFFFF),
      // 100: Color(0xFFFDC6C7),
      // 200: Color(0xFFFCA0A2),
      // 300: Color(0xFFFB7A7C),
      // 400: Color(0xFFFA5E60),
      // 500: Color(0xFFFF4F0F),
      // 600: Color(0xFFF83B3E),
      // 700: Color(0xFFF73235),
      // 800: Color(0xFFF62A2D),
      // 900: Color(0xFFF51C1F),
    },
  );
  // static const int _appredPrimaryValue = 0xFFFF4F0F;
  static const int _appredPrimaryValue = 0xFFFFFFFF;

  static const MaterialColor appredAccent = MaterialColor(
    _appredAccentValue,
    {
      100: Color(0xFFFFFFFF),
      200: Color(0xFFFFFFFF),
      400: Color(0xFFFFFFFF),
      700: Color(0xFFFFFFFF),
      // 200: Color(_appredAccentValue),
      // 400: Color(0xFFFFC3C3),
      // 700: Color(0xFFFFA9AA),
    },
  );
  static const int _appredAccentValue = 0xFFFFFFFF;

  /// LOGIN PAGE
  static const Color loginBtnBackgroundColor = Color(0xFF4E46B4);
  static const Color loginOPTTextColor = Color(0xFF1573FE);
  static Color loginRememberMeTextColor = const Color(0xFF333333).withValues(alpha: .5);

  /// REGISTER PAGE
  static const Color registerBtnBackgroundColor = Color(0xFFFD4E2F);
  static Color registerRememberMeTextColor = const Color(0xFF333333).withValues(alpha: .5);

  /// FORGOT PAGE
  static const Color forgotBtnBackgroundColor = Color(0xFFFD4E2F);
  static Color forgotRememberMeTextColor = const Color(0xFF333333).withValues(alpha: .5);
  static const Color forgotTitleTextColor = Color(0xFF333333);
  static const Color forgotResendCodeTextColor = Color(0xFF999CA0);

  /// NOTIFICATIONS PAGE
  static const Color notificationsTextColor = Color(0xFF333333);
  static const Color notificationsTextDescriptionColor = Color(0xFF555555);
  static const Color notificationsDotIconColor = Color(0xFFFF2600);

  /// CODES SCANED
  static const Color codeScanNameColor = Color(0xFF707070);
  static const Color codeScanPointsMonthColor = Color(0xFF3FAD50);
  static const Color codeScanBGColor = Color(0xFF525252);
  static const Color codeScanHeaderColor = Color(0xFFF5F5F5);

  /// ACTIVITIES
  static const Color activitiesReWardRedeemedColor = Color(0xFF38C793);
  static const Color activitiesCodesScanedColor = Color(0xFFF2AE40);
  static const Color activitiesEarnedPointsColor = Color(0xFF23A037);
  static const Color activitiesVoidsColor = Color(0xFFDF1C41);
  static const Color activitiesPendingColor = Color(0xFFDF1C41);
  static const Color activitiesGevenColor = Color(0xFF38C793);
  static const Color activitiesPendingPointsColor = Color(0xFF7D7D7D);
  static const Color activitiesPaymentColor = Color(0xFF375DFB);
  static const Color activitiesOfferClaimedColor = Color(0xFFF2AE40);

  /// ANNOUNCEMENTS
  static const Color anouncementBGColor = Color(0xFFf5f5f5);
  static const Color anouncementBorderColor = Color(0xFFf5f5f5);
  static const Color accountLabelColor = Color(0xFF7D7D7D);
  static const Color anouncementEndGradientColor = Color(0xFFFFDCC3);
  static const Color anouncementStartGradientColor = Color(0xFFFFFFFF);
  static const Color anouncementBorder = Color(0xFFD5D5D5);

  /// SIGNAGE REWARDS
  static const Color signageBorderColor = Color(0xFFF5F5F5);
  static const Color pointDancingColor = Color(0xFF989039);
  static const Color titleDancingColor = Color(0xFF828282);
  static const Color temp7DowloadColor = Color(0xFF638B4E);
  static const Color temp7TitleColor = Color(0xFFDA8286);
  static const Color temp7ItemNameColor = Color(0xFF476637);
  static const Color temp9BGColor = Color(0xFF74A3AB);

  /// PROFILE
  static const Color circularIndicatorPercent = Color(0xFF40A69F);
  static const Color circularIndicatorPercentBG = Color(0xFFD9D9D9);

  /// CHANGE PLAN
  static const Color changePlanBasic = Color(0xFF000000);
  static const Color changePlanBasicBG = Color(0xFF4E46B4);
  static const Color changePlanGold = Color(0xFFB3804A);
  static const Color changePlanGoldBG = Color(0xFFF4E2BD);
  static const Color changePlanPlatinum = Color(0xFF334D74);
  static const Color changePlanNeedToUpdateColor = Color(0xFFFF9900);
  static const Color changePlanPlatinumCurrentCustomerColor = Color(0xFF23A037);
  static const Color changePlanChartColor = Color(0xFF22D1EE);
  static const Color changePlanPlatinumGradientColor = Color(0xFF7A7873);
  static const Color changePlanBGColor = Color(0xFFFAFAFA);
  static const Color changePlanCurrentBtnBGColor = Color(0xFFEBEBEB);

  static const Color chartCodeScannedColor = Color(0xFF5C33CF);
  static const Color chartNewCustomersColor = Color(0xFF64DC77);
  static const Color chartRewardsRedeemColor = Color(0xFF999CA0);
  static const Color chartReturningColor = Color(0xFFFF4E64);

  /// PENDING APPROVALS
  static const Color rejectBtnText = Color(0xFFF06767);
  static const Color approveBtnText = Color(0xFF3448F0);
  static const Color pendingPointText = Color(0xFF23A037);
  static const Color pendingNameText = Color(0xFF707070);
  static const Color pendingReWardRedeemedColor = Color(0xFFFF4F0F);
  static const Color pendingEarnedPointsColor = Color(0xFF23A037);

  /// CUSTOMERS
  static const Color customersPhoneColor = Color(0xFF999CA0);

  /// ONBOARDING
  static const Color onboardingPrimary = Color(0xFF9DC183);
  static const Color onboardingSetupRwAIBackground = Color(0xFFFFF9E4);
  static const Color onboardingSetupRwAIBtnBg = Color(0xFF444444);
  static const Color onboardingSetupRwAddBtn = Color(0xFF3448F0);

  /// CHANGE PLAN
  static const Color changePlanColorMostPopular = Color(0xFFFFAF10);
  static const Color changePlanTextColor = Color(0xFFB3B2B5);

  static const Color offerActiveColor = Color(0xFF2C668F);
  static const Color offerCompletedColor = Color(0xFF23A037);
  static const Color offerRunningColor = Color(0xFF3448F0);
  static const Color offerPausedColor = Color(0xFFFF4E64);

  static const Color defaultLabelBGColor = Color(0xFFEAE9F6);

  static const Color editorBGColor = Color(0xFFEFF3F8);

  static const Color pointClaimsShrinkBG = Color(0xFF565656);
  static const Color pointClaimsCustomsMoreBG = Color(0xFFEAE9F6);
  static const Color pointClaimsCustomsRejectTitle = Color(0xFFFF4E64);
  static const Color pointClaimsCustomsRejectBG = Color(0xFFFF96A3);
  static const Color pointClaimsTagBG = Color(0xFFd8d8d8);

  static const Color callButtonEnterServiceNameHintText = Color(0xFFC3C3C3);

  static const Color deleteRoleColor = Color(0xFFFEF3EB);
  static const Color startDateTimeBGColor = Color(0xFFFFB319);

  static const Color cbItemColor = Color(0xFFF6F8FA);

  ///MULTIPLE BACKGROUND COLOR
  static const Color appBackground1Color = Color(0xFFF5F5F5);
  static const Color appBackground2Color = Color(0xFFE2E2E2);
  static const Color appBackground3Color = Color(0xFF588157);
  static const Color appBackground4Color = Color(0xFF414E92);
  static const Color appBackground5Color = Color(0xFF5E4678);
  static const Color appBackground6Color = Color(0xFF242424);
  static const Color appBackground7Color = Color(0xFFD4D9BC);
  static const Color appBackground8Color = Color(0xFF3BABAE);
  static const Color appBackground9_1Color = Color(0xFFFFDBA0);
  static const Color appBackground9_2Color = Color(0xFFF8968B);
  static const Color appBackground10_1Color = Color(0xFF77CDCE);
  static const Color appBackground10_2Color = Color(0xFFD5E4C3);
  static const Color appBackground10_3Color = Color(0xFFF8968B);
  static const Color appBackground11_1Color = Color(0xFF85FFFF);
  static const Color appBackground11_2Color = Color(0xFF6194D0);
  static const Color appBackground11_3Color = Color(0xFF273A80);
  static const Color appBackground12_1Color = Color(0xFFFB41D9);
  static const Color appBackground12_2Color = Color(0xFFEA4968);
  static const Color appBackground12_3Color = Color(0xFFFFCD54);
  static const Color thanksAnnouncement1BG = Color(0xFFFF5133);
  static const Color thanksAnnouncement2BG = Color(0xFFDF2676);
  static const Color multipleHomeTooltipColor = Color(0xFF525866);
  static const Color multipleHomeMainTooltipColor = Color(0xFF929292);
  static const Color multipleHomeMainTooltipSkipColor = Color(0xFF40A69F);
  static const Color tabsBGColor = Color.fromARGB(255, 55, 55, 55);
  static const Color tabsBadgeColor = Color(0xFF868C98);

  ///ORDERING
  static const Color buttonOrderItemBGColor = Color(0xFFDEDEDE);
  static const Color specialRequirementsColor = Color(0xFFC25D19);
  static const Color drawerBGColor = Color(0xFF1D1D1D);
  static const Color orderNewBGColor = Color(0xFFFF4F0F);
  static const Color orderInProgressBGColor = Color(0xFF375DFB);
  static const Color orderReadyBGColor = Color(0xFF23A037);
  static const Color orderCompletedBGColor = Color(0xFF23A037);
  static const Color orderScheduledBGColor = Color(0xFF6E3FF3);
  static const Color orderCancelledBGColor = Color(0xFFDF1C41);
  static const Color orderRefundedBGColor = Color(0xFFF17B2C);
  static const Color orderMarkBG = Color(0xFF444444);
  static const Color orderingProductTypeColor = Color(0xFFFF4E64);
  static const Color orderingProductThumbnailGBColor = Color(0xFFEBF1FF);
  static const Color orderingProductAvailableColor = Color(0xFFC2540A);
  static const Color orderingProductAvailableSwitchColor = Color(0xFF38C793);
  static const Color orderingIntegrationColor = Color(0xFFF6F8FA);
  static const Color orderingIntegrationTextColor = Color(0xFF0A0D14);
  static const Color comingSoon1Color = Color(0xFF10CDE7);
  static const Color comingSoon2Color = Color(0xFF8B80F0);
  static const Color comingSoon3Color = Color(0xFFFF5010);
  static const Color orderOnlineDisable = Color(0xFFD33030);
  static const Color orderLine = Color(0xFFCDD0D5);
  static const Color orderSettingAutoCompleted = Color(0xFFD33030);
  static const Color orderPayAtStoreColor = Color(0xFFE9ECFF);
  static const Color orderUnpaidColor = Color(0xFFD33030);
  static const Color orderUnpaidBGColor = Color(0xFFFFD5DA);
  static const Color orderPaidColor = Color(0xFF23A037);
  static const Color orderPaidBGColor = Color(0xFFC7FFCF);

  static const Color appTOImpressionsColor = Color(0xFF7B5ECC);
  static const Color appTOClaimsColor = Color(0xFFFF723F);

  static const Color appTOSegmentChampionCustomersColor = Color(0xFF006D77);
  static const Color appTOSegmentLoyalCustomersColor = Color(0xFF00B4D8);
  static const Color appTOSegmentNewCustomersColor = Color(0xFF90E0EF);
  static const Color appTOSegmentReactivatedCustomersColor = Color(0xFFFFD166);
  static const Color appTOSegmentPotentialLoyalistsColor = Color(0xFFF4A261);
  static const Color appTOSegmentAtRiskCustomersColor = Color(0xFFE76F51);
  static const Color appTOSegmentHibernatingCustomersColor = Color(0xFFD62828);
  static const Color appTOSegmentLostCustomersColor = Color(0xFF6D6875);
  static const Color appTOStatusRunningColor = Color(0xFF3448F0);
  static const Color appTOStatusPauseColor = Color(0xFFDF1C41);
  static const Color appTOStatusScheduleColor = Color(0xFFC2EFFF);
  static const Color appTOStatusScheduleTitleColor = Color(0xFF164564);
  static const Color appDeliveryIntegrations = Color(0xFFFFF6E3);
  static const Color appDeliveryIntegrationsBoxBG = Color(0xFFE2E4E9);
  static const Color appDeliveryIntegrationsEnabled = Color(0xFF2E7D32);
  static const Color appDeliveryIntegrationsDisabled = Color(0xFFD33030);
  static const Color appDeliveryAnalyticChartColor = Color(0xFF335CFF);
  static const Color appDeliveryTransactionChartColor = Color(0xFF006D77);
  static const Color appDeliveryAnalyticOverviewTextColor = Color(0xFF00195F);
  static const Color labelNewHomeColor = Color(0xFF6B717E);
  static const Color labelNewHomeBGColor = Color(0xFFF6F8FF);
  static const Color swichToFxibleHomeColor = Color(0xFF3669BB);
}
