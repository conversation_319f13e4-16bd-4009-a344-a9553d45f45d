class AppAPI {
  AppAPI._();

  /// AUTH
  static const String userLogin = 'auth/user_login';
  static const String refreshToken = 'auth/token';
  static const String forgotSendPhone = 'auth/forgot-password';
  static const String forgotSendCode = 'auth/forgot-password/is-valid-code';
  static const String forgotSendPassword = 'auth/forgot-password/verify';
  static const String forgotResend = 'auth/forgot-password/resend';
  static const String checkBusiness = 'auth/business-check';
  static const String loginBusiness = 'auth/business-login';
  static const String businessGetCode = 'auth/business-set-userpass';
  static const String businessVerifiedCode = 'auth/business-set-userpass/is-valid-code';
  static const String businessConfirmPassword = 'auth/business-set-userpass/verify';
  static const String businessResendCodePassword = 'auth/business-set-userpass/resend';
  static const String businessSignUpGetCode = 'auth/business-signup';
  static const String businessSignUpResend = 'auth/business-signup/resend';
  static const String businessSignUpVerify = 'auth/business-signup/verify';
  static const String businessSearchCustomerByPhone = 'business/customers/code/';
  static const String businessSearchCustomerByID = 'business/customers/id/';
  static const String businessQRStatistics = 'business/qr/statistics';
  static const String businessListStatistics = 'business/qr/list-statistics';
  static const String myBusinessDetail = 'business/account';
  static const String activities = 'business/activities';
  static const String addCustomer = 'business/customers/add';
  static const String activitiesDetail = 'business/activities';
  static const String activitiesVoid = 'business/void';
  static const String checkUser = 'auth/check';
  static const String platformUpgradeStatus = 'business/public/system/platform-upgrade-status';

  /// INSTANT QR GENERATE
  static const String AddSticky = 'qr';
  static const String redeem = 'redeem';

  /// PROFILE
  static const String profile = '/business/account';

  /// NOTIFICATIONS
  static const String notificationsList = 'v1/notifications';
  static const String notificationsSetAllRead = 'v1/notifications/setall-read';
  static const String notifications = 'v1/notifications';

  /// SEARCH CUSTOMER
  static const String searchCustomer = 'search-customer';

  /// ADD PRINTER
  static const String addPrinter = 'add-printer';
  static const String printers = 'business/printers';

  /// REWARDS
  static const String getRewards = 'business/rewards';
  static const String addReward = 'business/rewards';
  static const String updateReward = 'business/rewards';
  static const String deleteReward = 'business/rewards';
  static const String rewardProductSelection = 'orders/products/selection';
  static const String rewardCateroriesSelection = 'orders/categories/selection';

  /// CUSTOMER
  static const String customerRedeem = 'business/customers/redeem';
  static const String customerSetPoints = 'business/customers/add-points';

  /// MEDIA
  static const String uploadMedia = 'media/upload';

  /// USERS
  static const String userStaffs = 'business/users';

  /// LABELS
  static const String labelTemplates = 'business/label-templates';

  /// ANNOUNCEMENT
  static const String announcements = 'business/announcements';
  static const String announcementsAiContent = 'business/announcements/ai-content';

  /// REFERRAL
  static const String referral = 'business/account/settings/referral-program';

  /// ANNOUNCEMENT
  static const String myUserDetail = 'users/profile';
  static const String myUserSetPass = 'users/profile/set-password';

  /// CLOSE ACCOUNT
  static const String closeSendCode = 'auth/close-business-account/send';
  static const String closeResendCode = 'auth/close-business-account/resend';
  static const String closeCheckIsvalid = 'auth/close-business-account/is-valid';
  static const String closeVerify = 'auth/close-business-account/verify';

  /// AUDIT LOGGING
  static const String auditLogging = 'audit/ui';

  /// AUDIT LOGGING
  static const String statisticsDashboard = 'business/dashboard/stats';
  static const String statisticsDashboardChart = 'business/dashboard/stats/charts';
  static const String statisticsDashboardReferrals = 'business/dashboard/stats/referrals';
  static const String statisticsDashboardCustomer = 'business/dashboard/stats/new-returning-customers';

  /// PENDING APPROVALS
  static const String pendingApprovals = 'business/activities/pending-approvals';
  static const String pendingApprove = 'business/activities/approve-transactions';
  static const String pendingReject = 'business/activities/refuse-transactions';

  /// CUSTOMERS
  static const String getCustomers = 'business/customers';
  static const String searchCustomers = 'business/customers/search';
  static const String activitiesCustomer = 'business/users';
  static const String customerActivities = 'business/activities';

  /// ONBOARDING
  static const String onboarding = 'business/account/onboarding';

  /// AI REWARDS
  static const String aiRewards = 'business/rewards/ai-rewards';
  static const String onboardingAddRewards = 'business/rewards/batch';

  /// PAYMENT METHOD
  static const String getPaymentMethod = 'business/payments/payment-methods';

  /// PAYMENT METHOD
  static const String getChangePlan = 'business/account/plan-pricing';
  static const String doChangePlanPayment = 'business/payments/change-plan';
  static const String getPaymentInvoicesUpcoming = 'business/payments/invoices';
  static const String setPaymentDefaultCard = 'business/payments/payment-methods';
  static const String doUpdateCard = 'business/payments/payment-methods';
  static const String getClientSecret = 'business/payments/payment-methods/card';

  /// SEARCH ADDRESS
  static const String searchAddress = 'business/account/search';
  static const String searchAddressPublic = 'business/public/search-places';

  /// OFFER
  static const String offers = 'business/vouchers';
  // static const String offers = 'business/offers';
  // static const String offersCampaigns = 'business/offers/campaigns';
  static const String redeemOffer = 'business/customers/redeem-offer';
  static const String offersAvailble = 'business/vouchers/availables';
  // static const String offersAvailble = 'business/offers/availables';
  static const String offerClaim = 'business/customers/claims-customer-offer';

  static const String getOfferCustomer = 'business/customers/id';

  /// QR TAGS
  static const String getTags = 'business/tags';
  static const String addTag = 'business/tags';
  static const String updateTag = 'business/tags';
  static const String deteteTag = 'business/tags';

  /// BUY CREDITS
  static const String getAddOns = 'business/payments/addons?addon=TEXT_CREDIT';
  static const String addOn = '/business/payments/addons/buy';

  /// CUSTOMER PAYMENT
  static const String customerPayment = 'business/customer-payments';
  static const String checkEnableCustomerPayment = 'business/account/is-enabled-customer-pay';

  /// POINT CLAIMS
  static const String getPointClaims = 'business/account/point-claims';
  static const String rejectPointClaims = 'business/account/point-claims';
  static const String givePointClaims = 'business/customers/set-point-claims';
  static const String rejectAllPointClaims = 'business/account/point-claims/status-all';

  /// CALL BUTTON
  static const String callButtonGetSessions = 'business/call-button/sessions';
  static const String callButtonGetSessionTableDetail = 'business/call-button/sessions';
  static const String callButtonSetRequestDone = 'business/call-button/sessions';
  static const String callButtonCloseOut = 'business/call-button/sessions';
  static const String callButtonCloseOutInactive = 'business/call-button/sessions/close-inactive';
  static const String cbTable = 'business/call-button/tables';
  static const String cbService = 'business/call-button/services';
  static const String cbServiceSection = 'business/call-button/services/section-services';
  static const String cbTableSection = 'business/call-button/tables/section-tables';
  static const String cbTableAddNewTable = 'business/call-button/tables/table';
  static const String cbReoderTable = 'business/call-button/tables/section-tables/index-category';
  static const String cbReoderService = 'business/call-button/services/section-services/index-category';
  static const String callButtonSetPoints = 'business/customers/call-button-set-points';
  static const String callButtonCheckLastDateRequest = 'business/call-button/sessions/last-request-not-ack';
  static const String callButtonProcessRawData = 'business/call-button/hmc/process-raw-data';

  /// STAFF
  static const String staffTags = 'business/users';

  /// BUSINESS ROLES
  static const String businessRoles = 'business/permissions/roles';
  static const String rolePermission = 'business/permissions/roles/current';
  static const String getRolesPermissions = 'business/permissions/roles';
  static const String getPermissions = 'business/permissions';

  /// VOUCHER CALENDAR
  static const String getVouchersCalendar = 'business/vouchers/calendar';
  // static const String getVouchersCalendar = 'business/offers/calendar';

  /// MULTIPLE HOME
  static const String getMultipeHome = 'business/multiple-dashboard';
  static const String getMultipeHomeWidgets = 'business/multiple-dashboard/def/widgets';

  /// FCM
  static const String fcmToken = 'business/fcmtokens';

  /// Features info
  static const String busAccountFeatures = 'business/account/features';
  static const String featuresInfo = 'business/public/features';

  /// Features Order
  static const String ordersActive = 'orders/orders/active';
  static const String orderDetail = 'orders/orders';

  /// Features Ordering
  static const String getCategories = 'orders/categories';
  static const String getProduct = 'orders/products';
  static const String setProductOutOfStock = 'orders/products';
  static const String setProductModifiersOutOfStock = 'orders/modifiers';
  static const String setProductModifiersOptionAvailableUntil = 'orders/modifiers';
  static const String getModifiers = 'orders/modifiers';
  static const String orderHistory = 'orders/orders/history';
  static const String orderSettings = 'business/account/orders/settings';
  static const String orderWithIssue = 'orders/orders';
  static const String orderProductUpdate = 'orders/products';
  static const String regularStoreHours = 'orders/availability/open-hours';
  static const String specialStoreHours = 'orders/availability/special-and-closures';
  static const String addSpecialStoreHours = 'orders/availability/many-special-hours';
  static const String updateSpecialStoreHours = 'orders/availability/special-hours';
  static const String addClosuresDay = 'orders/availability/many-closures-day';
  static const String updateClosuresDay = 'orders/availability/closures-day';
  static const String orderSettingsPoints = 'orders/order-points/general-settings';
  static const String orderSettingsVouchers = 'orders/order-vouchers/general-settings';
  static const String customersSegment = 'business/customers/customers-segment';
  static const String searchPublicAddress = 'business/public/search-address';
  static const String newDelivery = 'orders/orders';

  /// Targeted Offers
  static const String targetedOffers = 'orders/target-offers/offers';
  static const String segments = 'orders/target-offers/segments';
  static const String customerSegmentsHistory = 'orders/target-offers/segment-customer-log';
  static const String targetOffersChannelPerformance = 'orders/target-offers/channel-performance';

  /// Receipt Template
  static const String businessReceiptTemplates = 'business/receipt-templates';
  static const String ordersTOSegmentCustomersAnalytics = 'orders/target-offers/segment-customers-analytics';
  static const String ordersTODashboardOverview = 'orders/target-offers/dashboard-overview';
  static const String ordersTOdashboardtransactions = 'orders/target-offers/dashboard-transactions';

  static const String businessPaymentGateways = 'business/payment-gateways';

  static const String multipleAccount = 'users/profile/businesses';
  static const String doSwitchAccount = 'auth/business-switch';
}
