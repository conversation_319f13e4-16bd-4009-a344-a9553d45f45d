import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:stickyqrbusiness/@const/config.dart';
import 'package:stickyqrbusiness/@core/enums.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/app/app.dart';
import 'package:stickyqrbusiness/bootstrap.dart';
import 'package:stickyqrbusiness/firebase_options_dev.dart';
import 'package:timezone/data/latest.dart' as tz;

void main() {
  tz.initializeTimeZones();
  LicenseRegistry.addLicense(() async* {
    final license = await rootBundle.loadString('google_fonts/OFL.txt');
    yield LicenseEntryWithLineBreaks(['google_fonts'], license);
  });

  bootstrap(
    () => const RestartWidget(child: App()),
    AppConfig.getEnvironment(AppEnvironment.development),
    DefaultFirebaseOptions.currentPlatform,
  );
}
