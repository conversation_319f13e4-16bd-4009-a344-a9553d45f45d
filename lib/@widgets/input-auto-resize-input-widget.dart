// ignore_for_file: empty_catches

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';

class AutoResizeInputWidget extends StatefulWidget {
  final ValueChanged<String> onChanged;
  final ValueChanged<String>? onFieldSubmitted;
  final String? hintText;
  final String? errorText;
  final bool isBorder;
  final Color? hintTextColor;
  final double? hintTextFontSize;
  final bool isValidate;
  final TextInputType? keyboardType;
  final Color errorColor;
  final Color? errorTextColor;
  final Icon? errorIcon;
  final double? errorFontSize;
  final int minLines;
  final int? maxLength;
  final int maxLines;
  final EdgeInsetsGeometry? contentPadding;
  final String? initialValue;
  final BorderRadius? borderRadius;
  final Color? borderColor;
  final bool isEnabledInput;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? suffixIcon;
  final TextStyle? styleTextForm;
  final Color? borderColorFocus;
  final double widthBorder;
  final TextEditingController? controller;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final EdgeInsets? scrollPadding;
  final TextDirection? textDirection;
  final TextDirection? hintTextDirection;
  final TextAlign? textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool readOnly;
  final double? minHeight;
  final double? maxHeight;

  const AutoResizeInputWidget({
    Key? key,
    required this.onChanged,
    this.textAlignVertical,
    this.maxLength,
    this.hintText,
    this.isValidate = false,
    this.errorText,
    this.isBorder = true,
    this.hintTextColor,
    this.hintTextFontSize,
    this.keyboardType = TextInputType.text,
    required this.errorColor,
    this.errorTextColor,
    this.errorIcon,
    this.errorFontSize,
    this.minLines = 1,
    this.maxLines = 1,
    this.contentPadding,
    this.initialValue,
    this.borderRadius,
    this.borderColor,
    this.isEnabledInput = true,
    this.inputFormatters,
    this.suffixIcon,
    this.styleTextForm,
    this.borderColorFocus,
    this.widthBorder = 1,
    this.controller,
    this.textInputAction,
    this.onFieldSubmitted,
    this.focusNode,
    this.scrollPadding,
    this.textDirection,
    this.hintTextDirection,
    this.textAlign,
    this.autofocus = false,
    this.readOnly = false,
    this.minHeight,
    this.maxHeight,
  }) : super(key: key);

  @override
  State<AutoResizeInputWidget> createState() => _AutoResizeInputWidgetState();
}

class _AutoResizeInputWidgetState extends State<AutoResizeInputWidget> with WidgetsBindingObserver {
  final FocusNode _focusInput = FocusNode();
  Color borderInputColor = AppColors.appBorderColor;
  final _textInputController = TextEditingController();
  double _currentHeight = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());

    // Tính toán chiều cao ban đầu
    _calculateHeight();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  void _calculateHeight() {
    final textStyle = widget.styleTextForm ?? const TextStyle(fontSize: 16);
    final lineHeight = textStyle.fontSize! * 1.2; // Ước tính line height
    final contentPadding = widget.contentPadding ?? const EdgeInsets.all(16);

    // Tính chiều cao dựa trên minLines
    double baseHeight = (lineHeight * widget.minLines) + contentPadding.vertical;

    // Áp dụng minHeight nếu có
    if (widget.minHeight != null) {
      baseHeight = baseHeight < widget.minHeight! ? widget.minHeight! : baseHeight;
    }

    setState(() {
      _currentHeight = baseHeight;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          constraints: BoxConstraints(
            minHeight: widget.minHeight ?? 58,
            maxHeight: widget.maxHeight ?? double.infinity,
          ),
          decoration: BoxDecoration(
            border: widget.isBorder
                ? Border.all(
                    width: widget.widthBorder,
                    color: widget.isValidate ? widget.errorColor : borderInputColor,
                  )
                : null,
            borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
          ),
          child: TextFormField(
            readOnly: widget.readOnly,
            cursorColor: AppColors.cursorColor,
            maxLength: widget.maxLength,
            autofillHints: const [AutofillHints.oneTimeCode],
            autofocus: widget.autofocus,
            textAlign: widget.textAlign ?? TextAlign.start,
            autocorrect: false,
            scrollPadding: widget.scrollPadding ?? const EdgeInsets.only(bottom: 32),
            textAlignVertical: widget.textAlignVertical ?? TextAlignVertical.top,
            style: widget.styleTextForm,
            enabled: widget.isEnabledInput,
            focusNode: widget.focusNode ?? _focusInput,
            controller: widget.controller ?? _textInputController,
            keyboardType: widget.maxLines > 1 ? TextInputType.multiline : (widget.keyboardType ?? TextInputType.text),
            inputFormatters: widget.inputFormatters,
            textInputAction: widget.maxLines > 1 ? TextInputAction.newline : (widget.textInputAction ?? TextInputAction.done),
            minLines: widget.minLines,
            maxLines: widget.maxLines,
            textDirection: widget.textDirection,
            decoration: InputDecoration(
              counterText: '',
              hintTextDirection: widget.hintTextDirection,
              contentPadding: widget.contentPadding ?? const EdgeInsets.all(16),
              border: InputBorder.none,
              isDense: false,
              isCollapsed: false,
              hintText: widget.hintText,
              hintStyle: TextStyle(
                color: widget.hintTextColor ?? AppColors.appBlackColor.withValues(alpha: .56),
                fontSize: widget.hintTextFontSize,
                fontWeight: FontWeight.w400,
              ),
              suffixIcon: widget.suffixIcon,
            ),
            onChanged: (value) {
              // Tính toán lại chiều cao khi text thay đổi
              _updateHeight(value);
              widget.onChanged(value);
            },
            onFieldSubmitted: widget.onFieldSubmitted,
          ),
        ),
        if (widget.isValidate)
          LabelControlWidget(
            title: widget.errorText ?? '',
            icon: widget.errorIcon,
            titleStyle: TextStyle(
              fontSize: widget.errorFontSize ?? 12,
              color: widget.errorTextColor ?? AppColors.appErrorColor,
            ),
          ),
      ],
    );
  }

  void _updateHeight(String text) {
    if (text.isEmpty) {
      _calculateHeight();
      return;
    }

    final textStyle = widget.styleTextForm ?? const TextStyle(fontSize: 16);
    final lineHeight = textStyle.fontSize! * 1.2;
    final contentPadding = widget.contentPadding ?? const EdgeInsets.all(16);

    // Tính số dòng cần thiết
    final textSpan = TextSpan(text: text, style: textStyle);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: widget.maxLines,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 64); // Ước tính max width

    final lineCount = textPainter.computeLineMetrics().length;
    final actualLines = lineCount < widget.minLines
        ? widget.minLines
        : lineCount > widget.maxLines
            ? widget.maxLines
            : lineCount;

    final newHeight = (lineHeight * actualLines) + contentPadding.vertical;

    // Áp dụng minHeight và maxHeight
    final finalHeight = widget.minHeight != null && newHeight < widget.minHeight!
        ? widget.minHeight!
        : widget.maxHeight != null && newHeight > widget.maxHeight!
            ? widget.maxHeight!
            : newHeight;

    if ((_currentHeight - finalHeight).abs() > 5) {
      // Chỉ update khi thay đổi đáng kể
      setState(() {
        _currentHeight = finalHeight;
      });
    }
  }

  void _listenFocus() {
    _focusInput.addListener(
      () {
        setState(
          () {
            borderInputColor = _focusInput.hasFocus ? widget.borderColorFocus ?? AppColors.darkPrimaryBackgroundColor : widget.borderColor ?? AppColors.appBorderColor;
          },
        );
      },
    );
  }

  void _buildCompleted() {
    _textInputController.text = widget.initialValue ?? '';
    if (widget.borderColor != null) {
      try {
        setState(() {
          borderInputColor = widget.borderColor!;
        });
      } catch (e) {}
    }
    _listenFocus();
  }
}
