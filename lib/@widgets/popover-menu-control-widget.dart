import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

// class PopoverMenuWidget extends StatelessWidget {
//   final ValueChanged<PopoverModel> onChanged;
//   final List<PopoverModel> menuItems;
//   final Widget? child;
//   final Widget? icon;
//   final Color? textColor;
//   final Color? iconColor;
//   final double? textSize;
//   final double? iconSize;
//   final Offset? offset;
//   final String? tooltip;
//   final EdgeInsetsGeometry? padding;
//   final FontWeight? fontWeight;
//   final bool showDivider; // Thêm option để hiển thị divider
//   final Color? dividerColor; // Màu của divider (optional)
//   final double? dividerHeight;
//   const PopoverMenuWidget({
//     Key? key,
//     required this.onChanged,
//     required this.menuItems,
//     this.fontWeight = FontWeight.w500,
//     this.child,
//     this.icon,
//     this.textColor,
//     this.iconColor,
//     this.textSize,
//     this.iconSize,
//     this.offset,
//     this.tooltip,
//     this.padding,
//     this.showDivider = false, // Mặc định là false (không hiển thị divider)
//     this.dividerColor,
//     this.dividerHeight,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: padding ?? const EdgeInsets.only(right: 8),
//       child: Theme(
//         data: Theme.of(context).copyWith(
//           highlightColor: Colors.transparent,
//           splashColor: Colors.transparent,
//           hoverColor: Colors.transparent,
//         ),
//         child: PopupMenuButton(
//           icon: icon,
//           tooltip: tooltip ?? '',
//           onSelected: (PopoverModel value) {
//             onChanged(value);
//           },
//           offset: offset ?? Offset(-8.0, AppBar().preferredSize.height - 10),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(12),
//           ),
//           // itemBuilder: (context) => menuItems
//           //     .map(
//           //       (item) => PopupMenuItem<PopoverModel>(
//           //         value: item,
//           //         child: Row(
//           //           children: [
//           //             if (item.icon != null || item.svgIcon != null)
//           //               Padding(
//           //                 padding: const EdgeInsets.only(right: 8.0),
//           //                 child: item.icon != null
//           //                     ? Icon(
//           //                         item.icon,
//           //                         size: iconSize ?? 16,
//           //                         color: iconColor ?? Colors.black.withValues(alpha: .7),
//           //                       )
//           //                     : SvgPicture.asset(
//           //                         'assets/svgs/${item.svgIcon}.svg',
//           //                         width: iconSize,
//           //                         height: iconSize,
//           //                       ),
//           //               ),
//           //             Text(
//           //               item.title,
//           //               style: TextStyle(
//           //                 color: textColor ?? Colors.black.withValues(alpha: .7),
//           //                 fontSize: textSize ?? 16,
//           //                 fontWeight: fontWeight ?? FontWeight.w500,
//           //               ),
//           //             ),
//           //           ],
//           //         ),
//           //       ),
//           //     )
//           //     .toList(),
//           itemBuilder: (context) {
//             final List<PopupMenuEntry<PopoverModel>> items = [];
            
//             for (int i = 0; i < menuItems.length; i++) {
//               final item = menuItems[i];
//               items.add(
//                 PopupMenuItem<PopoverModel>(
//                   value: item,
//                   child: Row(
//                     children: [
//                       if (item.icon != null || item.svgIcon != null)
//                         Padding(
//                           padding: const EdgeInsets.only(right: 8.0),
//                           child: item.icon != null
//                               ? Icon(
//                                   item.icon,
//                                   size: iconSize ?? 16,
//                                   color: iconColor ?? Colors.black.withValues(alpha: .7),
//                                 )
//                               : SvgPicture.asset(
//                                   'assets/svgs/${item.svgIcon}.svg',
//                                   width: iconSize,
//                                   height: iconSize,
//                                 ),
//                         ),
//                       Text(
//                         item.title,
//                         style: TextStyle(
//                           color: textColor ?? Colors.black.withValues(alpha: .7),
//                           fontSize: textSize ?? 16,
//                           fontWeight: fontWeight ?? FontWeight.w500,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               );
              
//               if (showDivider && i < menuItems.length - 1) {
//                 items.add(
//                   PopupMenuItem<PopoverModel>(
//                     enabled: false,
//                     height: dividerHeight ?? 1.0,
//                     child: Container(
//                       height: dividerHeight ?? 1.0,
//                       color: dividerColor ?? Colors.grey.withValues(alpha: 0.2),
//                     ),
//                   ),
//                 );
//               }
//             }
            
//             return items;
//           },
//           child: child,
//         ),
//       ),
//     );
//   }
// }
class PopoverMenuWidget extends StatelessWidget {
  final ValueChanged<PopoverModel> onChanged;
  final List<PopoverModel> menuItems;
  final Widget? child;
  final Widget? icon;
  final Color? textColor;
  final Color? iconColor;
  final double? textSize;
  final double? iconSize;
  final Offset? offset;
  final String? tooltip;
  final EdgeInsetsGeometry? padding;
  final FontWeight? fontWeight;
  final bool showDivider;
  final Color? dividerColor;
  final double? dividerHeight;
  final EdgeInsetsGeometry? dividerPadding;
  
  const PopoverMenuWidget({
    Key? key,
    required this.onChanged,
    required this.menuItems,
    this.fontWeight = FontWeight.w500,
    this.child,
    this.icon,
    this.textColor,
    this.iconColor,
    this.textSize,
    this.iconSize,
    this.offset,
    this.tooltip,
    this.padding,
    this.showDivider = false,
    this.dividerColor,
    this.dividerHeight,
    this.dividerPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(right: 8),
      child: GestureDetector(
        onTap: () => _showPopoverMenu(context),
        child: child ?? Container(
          padding: const EdgeInsets.all(8.0),
          child: Icon(
            Icons.more_vert,
            size: iconSize ?? 24,
          ),
        ),
      ),
    );
  }

  void _showPopoverMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
    
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(
          offset?.translate(0, button.size.height) ?? Offset(0, button.size.height),
          ancestor: overlay,
        ),
        button.localToGlobal(
          button.size.bottomRight(Offset.zero),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    // Build menu items with dividers
    List<PopupMenuEntry<PopoverModel>> popupItems = [];
    
    for (int i = 0; i < menuItems.length; i++) {
      final item = menuItems[i];
      
      // Add menu item
      popupItems.add(
        PopupMenuItem<PopoverModel>(
          value: item,
          child: Row(
            children: [
              if (item.icon != null || item.svgIcon != null)
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: item.icon != null
                      ? Icon(
                          item.icon,
                          size: iconSize ?? 16,
                          color: iconColor ?? Colors.black.withValues(alpha: .7),
                        )
                      : SvgPicture.asset(
                          'assets/svgs/${item.svgIcon}.svg',
                          width: iconSize ?? 16,
                          height: iconSize ?? 16,
                        ),
                ),
              Text(
                item.title,
                style: TextStyle(
                  color: textColor ?? Colors.black.withValues(alpha: .7),
                  fontSize: textSize ?? 16,
                  fontWeight: fontWeight ?? FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
      
      // Add divider if needed (not for last item)
      if (showDivider && i < menuItems.length - 1) {
        popupItems.add(
          PopupMenuItem<PopoverModel>(
            enabled: false,
            height: dividerHeight ?? 1.0,
            child: Padding(
              padding: dividerPadding ?? const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                height: dividerHeight ?? 1.0,
                color: dividerColor ?? Colors.grey.withValues(alpha: 0.3),
              ),
            ),
          ),
        );
      }
    }

    final PopoverModel? result = await showMenu<PopoverModel>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      items: popupItems,
      elevation: 8,
    );

    if (result != null) {
      onChanged(result);
    }
  }
}

class PopoverModel {
  final String id;
  final String title;
  final IconData? icon;
  final String? svgIcon;

  PopoverModel({
    required this.id,
    required this.title,
    this.icon,
    this.svgIcon,
  });
}

// import 'package:flutter/material.dart';

// class PopoverMenuWidget extends StatelessWidget {
//   final ValueChanged<String> onChanged;
//   final List<String> menuItems;
//   final Widget? child;
//   final IconData? icon;
//   final Color? textColor;
//   final Color? iconColor;
//   final double? textSize;
//   final double? iconSize;
//   final Offset? offset;
//   final String? tooltip;
//   const PopoverMenuWidget({
//     Key? key,
//     required this.onChanged,
//     required this.menuItems,
//     this.child,
//     this.icon,
//     this.textColor,
//     this.iconColor,
//     this.textSize,
//     this.iconSize,
//     this.offset,
//     this.tooltip,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.only(right: 8),
//       child: Theme(
//         data: Theme.of(context).copyWith(
//           highlightColor: Colors.transparent,
//           splashColor: Colors.transparent,
//           hoverColor: Colors.transparent,
//         ),
//         child: PopupMenuButton(
//           tooltip: tooltip ?? '',
//           onSelected: (value) {
//             // ignore: noop_primitive_operations
//             onChanged(value);
//           },
//           offset: offset ?? Offset(-8.0, AppBar().preferredSize.height - 10),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(12),
//           ),
//           itemBuilder: (context) => menuItems
//               .map(
//                 (item) => PopupMenuItem<String>(
//                   value: item,
//                   child: Row(
//                     children: [
//                       if (icon != null)
//                         Padding(
//                           padding: const EdgeInsets.only(right: 8.0),
//                           child: Icon(
//                             icon,
//                             size: iconSize ?? 16,
//                             color: iconColor ?? Colors.black.withValues(alpha: .7),
//                           ),
//                         ),
//                       Text(
//                         item,
//                         style: TextStyle(
//                           color: textColor ?? Colors.black.withValues(alpha: .7),
//                           fontSize: textSize ?? 16,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               )
//               .toList(),
//           child: child,
//         ),
//       ),
//     );
//   }
// }

// class PopoverModel {
//   final String id;
//   final String title;
//   final IconData? icon;
//   final String? svgIcon;

//   PopoverModel({
//     required this.id,
//     required this.title,
//     this.icon,
//     this.svgIcon,
//   });
// }
