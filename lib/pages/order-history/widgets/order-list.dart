// ignore_for_file: inference_failure_on_function_return_type, unawaited_futures

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail-time-line/order-history-detail-time-line.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';

class OrdersList extends StatelessWidget {
  final List<Order> orders;
  final Order? selectedOrder;
  final Function(Order)? onOrderSelected;

  const OrdersList({
    Key? key,
    this.onOrderSelected,
    required this.orders,
    this.selectedOrder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return BlocSelector<OrderHistoryCubit, OrderHistoryState, bool>(
      selector: (state) {
        return state.isHasMore;
      },
      builder: (context, state) {
        return NotificationListener<ScrollNotification>(
          onNotification: (notification) {
            if (notification is ScrollEndNotification) {
              if (notification.metrics.pixels == notification.metrics.maxScrollExtent && state) {
                context.read<OrderHistoryCubit>().getOrders(withLoading: false);
                return true;
              }
            }
            return true;
          },
          child: _buildContent(context, l10n),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, AppLocalizations l10n) {
    return BlocSelector<OrderHistoryCubit, OrderHistoryState, OrderHistoryStatus>(
      selector: (state) {
        return state.status;
      },
      builder: (context, state) {
        switch (state) {
          case OrderHistoryStatus.Loading:
            return _buildLoading();
          default:
        }
        return _buildListOrders(context, l10n);
      },
    );
  }

  Widget _buildListOrders(BuildContext context, AppLocalizations l10n) {
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    return Container(
      color: AppColors.appBGAvatarColor,
      child: BlocListener<OrderHistoryDetailCubit, OrderHistoryDetailState>(
        listener: (context, state) {
          if (state.status == OrderHistoryDetailStatus.Success) {
            if (state.order != null && state.order != selectedOrder) {
              context.read<OrderHistoryCubit>()
                ..updateOrderInList(state.order ?? Order())
                ..selectOrder(state.order ?? Order());
            }
          }
        },
        child: BlocBuilder<OrderHistoryCubit, OrderHistoryState>(
          builder: (context, state) {
            return RefreshIndicator(
              onRefresh: () => _onRefresh(context),
              color: AppColors.appColor,
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(
                  parent: ClampingScrollPhysics(),
                ),
                slivers: [
                  _buildContentList(l10n, state, isTablet),
                  if (state.isHasMore && (state.searchText == '' || state.searchText == null))
                    SliverFillRemaining(
                      hasScrollBody: false,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Center(
                            child: Container(
                              width: 18,
                              height: 18,
                              margin: const EdgeInsets.only(top: 24, bottom: 24),
                              child: const CircularProgressIndicator(
                                strokeWidth: 3,
                                color: AppColors.appColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  SliverFillRemaining(hasScrollBody: false, child: Container(height: 24)),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContentList(AppLocalizations l10n, OrderHistoryState state, bool isTablet) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          final order = orders[index];
          final timeZone = context.read<AuthBloc>().state.businessTimeZone;
          final isBorderBottom = index != orders.length - 1;
          return InkWell(
            onTap: () {
              if (onOrderSelected != null) {
                onOrderSelected!(order);
              } else {
                context.read<OrderHistoryCubit>().selectOrder(order);
              }
              context.read<OrderHistoryDetailCubit>().getOrderHistoryDetail(id: order.id ?? '');
              context.read<OrderHistoryDetailTimeLineCubit>().getOrderHistoryDetailTimeLine(id: order.id ?? '');
            },
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                border: Border(
                  bottom: BorderSide(
                    color: isBorderBottom == true ? AppColors.appBorderColor : Colors.transparent,
                    width: 1,
                  ),
                ),
              ),
              child: isTablet ? _buildIsTablet(context, l10n, order, timeZone) : _buildIsMobile(context, l10n, order, timeZone),
            ),
          );
        },
        childCount: orders.length,
      ),
    );
  }

  Widget _buildIsMobile(
    BuildContext context,
    AppLocalizations l10n,
    Order order,
    String? timeZone,
  ) {
    // final customerName = (order.customer?.displayName != '' && order.customer?.displayName != null)
    //     ? order.customer?.displayName
    //     : (order.customerName != '' && order.customerName != null)
    //         ? order.customerName
    //         : l10n.guest;

    final customerName = (order.customerName != '' && order.customerName != null)
        ? order.customerName
        : (order.customer?.displayName != '' && order.customer?.displayName != null)
            ? order.customer?.displayName
            : l10n.guest;
    final customerPhone = order.customer?.phone ?? order.phone ?? '';
    final isActive = order.customer?.isActive ?? true;
    final isFromPOS = order.isFromPOS ?? false;
    final isNewDelivery = order.isDeliveryExpress ?? false;
    final customerNewDeliveryPhone = order.phone ?? order.deliveryAddress?.phone ?? '';

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    if (order.orderNumber != null && order.orderNumber != '')
                      Padding(
                        padding: const EdgeInsets.only(right: 2),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              '#${order.orderNumber}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                color: Colors.black,
                              ),
                            ),
                            if (order.type?.toUpperCase() == OrderType.DELIVERY.name)
                              Padding(
                                padding: const EdgeInsets.only(left: 2.0),
                                child: SvgPicture.asset(
                                  'assets/svgs/delivery-black.svg',
                                ),
                              )
                          ],
                        ),
                      ),
                    OrderHistoryStatusWidget(order: order),
                  ],
                ),
              ),
              if (_detectTimeValue(order) != null) ...[
                Text(
                  DateTimeHelper.dateTimeLongFormat(
                    _detectTimeValue(order),
                    timeZone: timeZone,
                    isWeekDay: false,
                    langLocale: Localizations.localeOf(context).languageCode,
                  ),
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500, overflow: TextOverflow.ellipsis),
                  textAlign: TextAlign.end,
                ),
                const SizedBox(height: 4),
              ],
            ],
          ),
          const SizedBox(width: 8),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(
                    isFromPOS
                        ? (customerName ?? l10n.guest)
                        : isActive
                            ? (customerName ?? l10n.guest)
                            : l10n.guestDelete,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                ),
                if (isNewDelivery && customerNewDeliveryPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerNewDeliveryPhone) != '') ...[
                  Text(
                    AppValidations.formatPhoneNumber(customerNewDeliveryPhone),
                    style: const TextStyle(
                      color: Color(0xFF595D62),
                      fontSize: 14,
                    ),
                  ),
                ] else if (customerPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerPhone) != '')
                  Text(
                    AppValidations.formatPhoneNumber(customerPhone),
                    style: const TextStyle(
                      color: Color(0xFF595D62),
                      fontSize: 14,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIsTablet(
    BuildContext context,
    AppLocalizations l10n,
    Order order,
    String? timeZone,
  ) {
    final customerName = (order.customerName != '' && order.customerName != null)
        ? order.customerName
        : (order.customer?.displayName != '' && order.customer?.displayName != null)
            ? order.customer?.displayName
            : l10n.guest;
    final customerPhone = order.customer?.phone ?? order.phone ?? '';
    final isActive = order.customer?.isActive ?? true;
    final isFromPOS = order.isFromPOS ?? false;
    final isNewDelivery = order.isDeliveryExpress ?? false;
    final customerNewDeliveryPhone = order.phone ?? order.deliveryAddress?.phone ?? '';

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (order.orderNumber != null && order.orderNumber != '')
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Row(
                  children: [
                    Text(
                      '#${order.orderNumber}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.black,
                      ),
                    ),
                    if (order.type?.toUpperCase() == OrderType.DELIVERY.name)
                      Padding(
                        padding: const EdgeInsets.only(left: 2.0),
                        child: SvgPicture.asset(
                          'assets/svgs/delivery-black.svg',
                        ),
                      )
                  ],
                ),
              ),
            ),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(
                    isFromPOS
                        ? (customerName ?? l10n.guest)
                        : isActive
                            ? (customerName ?? l10n.guest)
                            : l10n.guestDelete,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                ),
                if (isNewDelivery && customerNewDeliveryPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerNewDeliveryPhone) != '') ...[
                  Text(
                    AppValidations.formatPhoneNumber(customerNewDeliveryPhone),
                    style: const TextStyle(
                      color: Color(0xFF595D62),
                      fontSize: 14,
                    ),
                  ),
                ] else  if (customerPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerPhone) != '')
                Text(
                  AppValidations.formatPhoneNumber(customerPhone),
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                    color: Color(0xFF595D62),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: OrderHistoryStatusWidget(order: order),
                ),
                if (_detectTimeValue(order) != null) ...[
                  Text(
                    DateTimeHelper.dateTimeLongFormat(
                      _detectTimeValue(order),
                      timeZone: timeZone,
                      isWeekDay: false,
                      langLocale: Localizations.localeOf(context).languageCode,
                    ),
                    style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500, overflow: TextOverflow.ellipsis),
                    textAlign: TextAlign.end,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  DateTime? _detectTimeValue(Order? order) {
    return order?.createdAt;
    // switch (order?.status?.toUpperCase()) {
    //   case 'CONFIRMED':
    //     if (order?.scheduledAt != null) {
    //       return order?.scheduledAt;
    //     }
    //     return order?.prepEndTime;
    //   case 'PREPARING':
    //     return order?.prepEndTime;
    //   case 'READY':
    //     return order?.readyAt;
    //   case 'SCHEDULED':
    //     return order?.scheduledAt;
    //   case 'CANCELLED':
    //     return order?.canceledAt;
    //   case 'COMPLETED':
    //     return order?.completedAt;
    //   default:
    //     return null;
    // }
  }

  Future<void> _onRefresh(BuildContext context) async {
    context.read<OrderHistoryCubit>()
      ..onChangedSearchTextClear()
      ..onChangedPageClear()
      ..onChangedStatus(OrderHistoryFilterStatus.ALL)
      ..onChangedDateTimeType(OrderHistoryFilterDateTime.ALL)
      ..getOrders(page: 0);
  }

  Widget _buildLoading() {
    return ListView(
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      children: [
        SkeletonLoadingWidget(
          itemCount: 25,
          paddingList: const EdgeInsets.only(top: 12, left: 12),
          paddingItem: EdgeInsets.zero,
          marginItem: const EdgeInsets.only(bottom: 6),
          widget: SkeletonLoadingItem(height: 76),
        ),
      ],
    );
  }
}
