// ignore_for_file: inference_failure_on_instance_creation

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-history/orders-history.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/@widgets/popover-menu-control-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/history-refund/history-refund-page.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';

class OrderDetailsScreen extends StatelessWidget {
  final Order order;
  final bool? isMobile;
  const OrderDetailsScreen({
    super.key,
    required this.order,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    return OrderDetailsScreenWidgetContent(
      order: order,
      isMobile: isMobile,
    );
  }
}

class OrderDetailsScreenWidgetContent extends StatelessWidget {
  final Order order;
  final bool? isMobile;

  const OrderDetailsScreenWidgetContent({
    Key? key,
    required this.order,
    this.isMobile = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    return SafeArea(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            if (!isTablet) ...{
              AppBar(
                elevation: 0.5,
                scrolledUnderElevation: 0.5,
                shadowColor: Colors.grey.withValues(alpha: .2),
                automaticallyImplyLeading: false,
                backgroundColor: AppColors.lightPrimaryBackgroundColor,
                centerTitle: true,
                leading: IconButton(
                  icon: SvgPicture.asset('assets/svgs/arrow-back.svg'),
                  onPressed: () async {
                    await Navigator.maybePop(context);
                  },
                ),
                title: Text(
                  l10n.odHistoryOrderDetail,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              OrderHeaderContent(l10n: l10n, order: order),
              const SizedBox(height: 8),
              const Divider(
                thickness: 8,
                height: 8,
                color: AppColors.appBGAvatarColor,
              ),
            } else ...{
              OrderHeaderContent(l10n: l10n, order: order),
            },
            // Row(
            //   children: [
            //     SizedBox(
            //       width: 48, //56
            //       child: MaterialButton(
            //         elevation: 0,
            //         highlightElevation: 0,
            //         hoverElevation: 0,
            //         hoverColor: AppColors.lightPrimaryBackgroundColor,
            //         onPressed: () => {
            //           Navigator.of(context).pop(),
            //         },
            //         color: AppColors.darkPrimaryBackgroundColor.withValues(alpha: .08),
            //         padding: EdgeInsets.zero,
            //         shape: const CircleBorder(),
            //         child: Padding(
            //           padding: const EdgeInsets.all(8.0),
            //           child: SvgPicture.asset(
            //             'assets/svgs/close.svg',
            //             colorFilter: const ColorFilter.mode(
            //               AppColors.darkPrimaryBackgroundColor,
            //               BlendMode.srcIn,
            //             ),
            //           ),
            //         ),
            //       ),
            //     ),
            //     Expanded(child: OrderHeaderContent(l10n: l10n, order: order)),
            //   ],
            // ),
            Expanded(
              child: SingleChildScrollView(
                child: OrderDetails(order: order, l10n: l10n),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OrderDetails extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderDetails({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocSelector<OrderHistoryCubit, OrderHistoryState,
        OrderHistoryStatus>(
      selector: (state) {
        return state.status;
      },
      builder: (context, state) {
        switch (state) {
          case OrderHistoryStatus.Loading:
            return const OrderHistoryDetailLoading();
          default:
            return _buildContent(context);
        }
      },
    );
  }

  Widget _buildContent(BuildContext context) {
    if (order.id == null) {
      return Center(
        child: Text(l10n.selectedAnOrderToViewDetail),
      );
    }
    return OrderDetailWidgetContent(order: order);
  }
}

class OrderHeaderContent extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderHeaderContent({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final orderNumber = '#${order.orderNumber}';
    final auth = context.read<AuthBloc>().state;
    final isShowAddNewDelivery =
        auth.business?.enableOrderDeliveryFeature == true &&
            auth.business?.orderAllowDelivery == true;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return BlocBuilder<OrderHistoryDetailCubit, OrderHistoryDetailState>(
          builder: (context, state) {
            final detail = state.order;
            final stt = order.status ?? '';
            final isAllowRefund = detail?.allowRefund ?? false;
            final isFromPOS = detail?.isFromPOS ?? false;

            return Container(
              padding: const EdgeInsets.fromLTRB(8, 8, 0, 8),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1,
                    color: AppColors.appBGAvatarColor,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (isTablet)
                    Container(
                      margin: const EdgeInsets.only(right: 16),
                      width: 48,
                      child: MaterialButton(
                        elevation: 0,
                        highlightElevation: 0,
                        hoverElevation: 0,
                        hoverColor: AppColors.lightPrimaryBackgroundColor,
                        onPressed: () => Navigator.of(context).pop(),
                        color: AppColors.darkPrimaryBackgroundColor
                            .withValues(alpha: .08),
                        padding: EdgeInsets.zero,
                        shape: const CircleBorder(),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SvgPicture.asset(
                            'assets/svgs/close.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.darkPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              orderNumber,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: AppColors.appBlackColor,
                              ),
                            ),
                            if (order.type?.toUpperCase() ==
                                OrderType.DELIVERY.name)
                              Padding(
                                padding: const EdgeInsets.only(left: 2.0),
                                child: SvgPicture.asset(
                                  'assets/svgs/delivery-black.svg',
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: OrderHistoryStatusWidget(order: order),
                        ),
                      ],
                    ),
                  ),
                  // Padding(
                  //   padding: const EdgeInsets.only(right: 12),
                  //   child: CustomPopoverMenu(
                  //     order: order,
                  //     detail: detail,
                  //     isShowAddNewDelivery: isShowAddNewDelivery,
                  //     isAllowRefund: isAllowRefund,
                  //     stt: stt,
                  //     isFromPOS: isFromPOS,
                  //     stateAuth: stateAuth,
                  //     l10n: l10n,
                  //   ),
                  // ),
                  Padding(
  padding: const EdgeInsets.only(right: 12),
  child: PopoverMenuWidget(
    showDivider: true,
    onChanged: (value) {
      switch (value.id) {
        case '1':
          AppBased.go(context, AppRoutes.addNewDeliveryPage, 
              args: ScreenArguments(data: order));
          return;
        case '2':
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) => 
                  HistoryRefundPage(order: detail ?? order),
              transitionDuration: const Duration(milliseconds: 200),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(1.0, 0.0),
                    end: Offset.zero,
                  ).animate(animation),
                  child: child,
                );
              },
            ),
          );
          return;
        default:
      }
    },
    menuItems: [
      if (isShowAddNewDelivery)
        PopoverModel(
          id: '1',
          title: l10n.addNewDelivery,
          icon: Icons.add,
        ),
      if (stateAuth.checkPermissions(PermissionBusiness.orders_orders_history.name, [2, 3]) &&
          isAllowRefund && 
          stt != '' && 
          stt.toUpperCase() != 'CANCELLED' && 
          !isFromPOS)
        PopoverModel(
          id: '2',
          title: l10n.refund,
        ),
    ],
    padding: EdgeInsets.zero,
    textColor: Colors.black,
    fontWeight: FontWeight.w600,
    child: Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.black.withValues(alpha: .05),
        border: Border.all(
          width: 1,
          color: Colors.black,
        ),
      ),
      child: const Icon(
        Icons.more_horiz,
        size: 24,
        color: Colors.black,
      ),
    ),
  ),
),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

// Widget riêng biệt để tránh rebuild liên tục
class _StablePopoverMenu extends StatefulWidget {
  final Order order;
  final Order? detail;
  final bool isShowAddNewDelivery;
  final bool isAllowRefund;
  final String stt;
  final bool isFromPOS;
  final AuthState stateAuth;
  final AppLocalizations l10n;

  const _StablePopoverMenu({
    required this.order,
    required this.detail,
    required this.isShowAddNewDelivery,
    required this.isAllowRefund,
    required this.stt,
    required this.isFromPOS,
    required this.stateAuth,
    required this.l10n,
  });

  @override
  State<_StablePopoverMenu> createState() => _StablePopoverMenuState();
}

class _StablePopoverMenuState extends State<_StablePopoverMenu> {
  // Key ổn định, chỉ thay đổi khi cần thiết
  late Key _popoverKey;

  @override
  void initState() {
    super.initState();
    _popoverKey = ValueKey('popover_${widget.order.id}');
  }

  @override
  void didUpdateWidget(_StablePopoverMenu oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Chỉ tạo key mới khi thực sự cần thiết
    if (oldWidget.order.id != widget.order.id ||
        oldWidget.isShowAddNewDelivery != widget.isShowAddNewDelivery ||
        oldWidget.isAllowRefund != widget.isAllowRefund) {
      _popoverKey = ValueKey(
          'popover_${widget.order.id}_${DateTime.now().millisecondsSinceEpoch}');
    }
  }

  void _forceRebuild() {
    if (mounted) {
      setState(() {
        _popoverKey = ValueKey(
            'popover_${widget.order.id}_${DateTime.now().millisecondsSinceEpoch}');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopoverMenuWidget(
      key: _popoverKey,
      showDivider: true,
      onChanged: (value) async {
        try {
          switch (value.id) {
            case '1':
              AppBased.go(context, AppRoutes.addNewDeliveryPage,
                  args: ScreenArguments(data: widget.order));
              _forceRebuild(); // Rebuild sau khi quay lại
              return;
            case '2':
              await Navigator.push(
                context,
                PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      HistoryRefundPage(order: widget.detail ?? widget.order),
                  transitionDuration: const Duration(milliseconds: 200),
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(1.0, 0.0),
                        end: Offset.zero,
                      ).animate(animation),
                      child: child,
                    );
                  },
                ),
              );
              _forceRebuild(); // Rebuild sau khi quay lại
              return;
            default:
          }
        } catch (e) {
          // Handle lỗi navigation nếu có
          AppLog.e('Navigation error: $e');
        }
      },
      menuItems: [
        if (widget.isShowAddNewDelivery)
          PopoverModel(
            id: '1',
            title: widget.l10n.addNewDelivery,
          ),
        if (widget.stateAuth.checkPermissions(
                PermissionBusiness.orders_orders_history.name, [2, 3]) &&
            widget.isAllowRefund &&
            widget.stt != '' &&
            widget.stt.toUpperCase() != 'CANCELLED' &&
            !widget.isFromPOS)
          PopoverModel(
            id: '2',
            title: widget.l10n.refund,
          ),
      ],
      padding: EdgeInsets.zero,
      textColor: Colors.black,
      fontWeight: FontWeight.w600,
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: Colors.black.withValues(alpha: .05),
          border: Border.all(
            width: 1,
            color: Colors.black,
          ),
        ),
        child: const Icon(
          Icons.more_horiz,
          size: 24,
          color: Colors.black,
        ),
      ),
    );
  }
}

class CustomPopoverMenu extends StatefulWidget {
  final Order order;
  final Order? detail;
  final bool isShowAddNewDelivery;
  final bool isAllowRefund;
  final String stt;
  final bool isFromPOS;
  final AuthState stateAuth;
  final AppLocalizations l10n;

  const CustomPopoverMenu({
    Key? key,
    required this.order,
    required this.detail,
    required this.isShowAddNewDelivery,
    required this.isAllowRefund,
    required this.stt,
    required this.isFromPOS,
    required this.stateAuth,
    required this.l10n,
  }) : super(key: key);

  @override
  State<CustomPopoverMenu> createState() => _CustomPopoverMenuState();
}

class _CustomPopoverMenuState extends State<CustomPopoverMenu> {
  OverlayEntry? _overlayEntry;
  final GlobalKey _buttonKey = GlobalKey();

  void _showPopover() {
    final RenderBox? renderBox =
        _buttonKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final offset = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    const popupWidth = 200.0;
    const popupMaxHeight = 300.0;

    // Tính toán vị trí để không bị tràn màn hình
    double left = offset.dx + size.width - popupWidth; // Căn phải với button
    double top = offset.dy + size.height + 5;

    // Kiểm tra tràn phải
    if (left + popupWidth > screenWidth) {
      left = screenWidth - popupWidth - 16;
    }

    // Kiểm tra tràn trái
    if (left < 16) {
      left = 16;
    }

    // Kiểm tra tràn dưới
    if (top + popupMaxHeight > screenHeight) {
      top = offset.dy - popupMaxHeight - 5; // Hiển thị phía trên button
      if (top < 50) {
        top = 50; // Đảm bảo không che status bar
      }
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Background để đóng popup khi tap outside
          GestureDetector(
            onTap: _hidePopover,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
          // Popup content
          Positioned(
            left: left,
            top: top,
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: popupWidth,
                constraints: const BoxConstraints(
                  maxHeight: popupMaxHeight,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: _buildMenuItems(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hidePopover() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  List<Widget> _buildMenuItems() {
    final List<Widget> items = [];

    // Add New Delivery item
    if (widget.isShowAddNewDelivery) {
      items.add(
        _PopoverMenuItem(
          onTap: () {
            _hidePopover();
            AppBased.go(context, AppRoutes.addNewDeliveryPage,
                args: ScreenArguments(data: widget.order));
          },
          title: widget.l10n.addNewDelivery,
        ),
      );
    }

    // Divider
    if (widget.isShowAddNewDelivery &&
        widget.stateAuth.checkPermissions(
            PermissionBusiness.orders_orders_history.name, [2, 3]) &&
        widget.isAllowRefund &&
        widget.stt != '' &&
        widget.stt.toUpperCase() != 'CANCELLED' &&
        !widget.isFromPOS) {
      items.add(
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
        ),
      );
    }

    // Refund item
    if (widget.stateAuth.checkPermissions(
            PermissionBusiness.orders_orders_history.name, [2, 3]) &&
        widget.isAllowRefund &&
        widget.stt != '' &&
        widget.stt.toUpperCase() != 'CANCELLED' &&
        !widget.isFromPOS) {
      items.add(
        _PopoverMenuItem(
          onTap: () {
            _hidePopover();
            Navigator.push(
              context,
              PageRouteBuilder(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    HistoryRefundPage(order: widget.detail ?? widget.order),
                transitionDuration: const Duration(milliseconds: 200),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(animation),
                    child: child,
                  );
                },
              ),
            );
          },
          title: widget.l10n.refund,
        ),
      );
    }

    return items;
  }

  @override
  void dispose() {
    _hidePopover();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _buttonKey,
      onTap: _showPopover,
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0),
          color: Colors.black.withValues(alpha: .05),
          border: Border.all(
            width: 1,
            color: Colors.black,
          ),
        ),
        child: const Icon(
          Icons.more_horiz,
          size: 24,
          color: Colors.black,
        ),
      ),
    );
  }
}

class _PopoverMenuItem extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final IconData? icon;

  const _PopoverMenuItem({
    required this.onTap,
    required this.title,
    // ignore: unused_element_parameter
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: Colors.black.withValues(alpha: 0.7),
              ),
              const SizedBox(width: 8),
            ],
            Text(
              title,
              style: TextStyle(
                color: Colors.black.withValues(alpha: 0.7),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
