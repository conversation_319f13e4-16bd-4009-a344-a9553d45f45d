// ignore_for_file: inference_failure_on_instance_creation, empty_catches

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customer-segment/customer-segment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail-time-line/order-history-detail-time-line.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/segment-target-offers-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/billing-payment/widgets/billing-define.dart';
import 'package:stickyqrbusiness/pages/order-history/widgets/@order-history-widgets.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';

class OrderDetailWidgetContent extends StatelessWidget {
  final Order order;
  const OrderDetailWidgetContent({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final isNewDelivery = order.isDeliveryExpress ?? false;

    return BlocProvider(
      create: (context) => CustomerSegmentCubit()..getCustomersSegment([order.customerId ?? order.customer?.id ?? '']),
      child: RefreshIndicator(
        onRefresh: () => _onRefresh(context),
        color: AppColors.appColor,
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // OrderHeader(order: order, l10n: l10n),
              // const Divider(
              //   thickness: 8,
              //   height: 8,
              //   color: AppColors.appBGAvatarColor,
              // ),
              OrderHeaderDetail(order: order, l10n: l10n),
              const Divider(
                thickness: 8,
                height: 8,
                color: AppColors.appBGAvatarColor,
              ),
              if (order.type?.toUpperCase() == OrderType.DELIVERY.name) ...[
                //order.status != OrderStatusDefine.CANCELLED.name &&
                OrderDeliveryHeaderDetail(order: order, l10n: l10n),
                const Divider(
                  thickness: 8,
                  height: 8,
                  color: AppColors.appBGAvatarColor,
                ),
              ],

              Padding(
                padding: const EdgeInsets.only(left: 16, top: 16),
                child: Text(
                  isNewDelivery ? l10n.paymentSummary.toUpperCase() : l10n.items.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.appBlackColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (!isNewDelivery) ...[
                OrderItemsList(order: order, items: order.items ?? []),
              ],
              // const Divider(
              //   thickness: 8,
              //   height: 8,
              //   color: AppColors.appBGAvatarColor,
              // ),
              OrderSummary(order: order, l10n: l10n),
              OrderStatusTimelineWidget(
                orderId: order.id ?? '',
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    try {
      await context.read<OrderHistoryDetailCubit>().getOrderHistoryDetail(id: order.id ?? '');
      await context.read<OrderHistoryDetailTimeLineCubit>().getOrderHistoryDetailTimeLine(id: order.id ?? '');
    } catch (e) {}
  }
}

class OrderHeaderDetail extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderHeaderDetail({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final timeZone = context.read<AuthBloc>().state.businessTimeZone;
    AppLog.e('order = ${jsonEncode(order)}');
    final customerName = (order.customerName != '' && order.customerName != null)
        ? order.customerName
        : (order.customer?.displayName != '' && order.customer?.displayName != null)
            ? order.customer?.displayName
            : l10n.guest;

    final customerPhone = order.customer?.phone ?? order.phone ?? '';
    final isActive = order.customer?.isActive ?? true;
    final isFromPOS = order.isFromPOS ?? false;
    final isNewDelivery = order.isDeliveryExpress ?? false;
    final customerNewDeliveryPhone = order.phone ?? order.deliveryAddress?.phone ?? '';
    final customerNewDeliveryName = order.customerName ?? order.deliveryAddress?.name ?? l10n.guest;
    final bool hasPOSItem = isFromPOS;
    final bool hasPaymentItem = !isNewDelivery && ((order.paymentMethodType?.toUpperCase() == OrderPaymentAtStore.PAY_AT_STORE.name) || (order.metadata != null && order.metadata?.last4 != null));

    final uid = order.customerId ?? order.customer?.id;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return BlocBuilder<OrderHistoryDetailCubit, OrderHistoryDetailState>(
          builder: (context, state) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1,
                    color: AppColors.appBGAvatarColor,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(
                      l10n.customerInformation.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  _buildItem(
                    title: l10n.customer,
                    value: customerName,
                    widget: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (isNewDelivery) ...[
                          RichText(
                            textAlign: TextAlign.right,
                            softWrap: true,
                            text: TextSpan(
                              text: customerNewDeliveryName,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ] else
                          RichText(
                            textAlign: TextAlign.right,
                            softWrap: true,
                            text: TextSpan(
                              text: isFromPOS
                                  ? (customerName ?? l10n.guest)
                                  : isActive
                                      ? (customerName ?? l10n.guest)
                                      : l10n.guestDelete,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                              children: [
                                if (isActive && uid != null && uid != '')
                                  WidgetSpan(
                                    child: Container(
                                      margin: const EdgeInsets.only(left: 2),
                                      width: 24,
                                      height: 24,
                                      child: IconButton(
                                        padding: EdgeInsets.zero,
                                        onPressed: () {
                                          final user = Search(
                                            userId: uid,
                                            displayName: customerName,
                                            phoneNumber: customerPhone,
                                            businessId: order.businessId,
                                            avatar: order.customer?.avatar,
                                            user: User(
                                              id: uid,
                                              displayName: customerName,
                                              email: order.customer?.email,
                                              phone: customerPhone,
                                            ),
                                          );
                                          final data = {'user': user};
                                          AppBased.go(
                                            context,
                                            AppRoutes.redeemPoint,
                                            args: ScreenArguments(
                                              data: data,
                                              type: 'activities-page',
                                            ),
                                            onChanged: (data) {},
                                          );
                                        },
                                        icon: SvgPicture.asset(
                                          'assets/svgs/open-link.svg',
                                          colorFilter: const ColorFilter.mode(
                                            Color(0xFF3448F0),
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        if (isActive)
                          BlocBuilder<CustomerSegmentCubit, CustomerSegmentState>(
                            builder: (context, state) {
                              if (state.status == CustomerSegmentStatus.Success) {
                                final typeByID = state.selectedCustomer(uid ?? '')?.type;
                                if (typeByID != null) {
                                  return SegmentTargetOffersWidget(
                                    title: typeByID,
                                    uid: uid,
                                    titleColor: AppColors.homeShowQRBGColor,
                                    margin: const EdgeInsets.only(top: 4),
                                  );
                                }
                              }
                              return const SizedBox.shrink();
                            },
                          )
                      ],
                    ),
                  ),
                  if (isNewDelivery && customerNewDeliveryPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerNewDeliveryPhone) != '') ...[
                    _buildItem(
                      title: l10n.phoneNumber,
                      isBorder: (order.metadata != null && order.metadata?.last4 != null) || (_detectTimeValue(order) != null),
                      value: AppValidations.formatPhoneNumber(customerNewDeliveryPhone),
                    ),
                  ] else if (customerPhone.isNotEmpty && AppValidations.formatPhoneNumber(customerPhone) != '')
                    _buildItem(
                      title: l10n.phoneNumber,
                      isBorder: (order.metadata != null && order.metadata?.last4 != null) || (_detectTimeValue(order) != null),
                      value: AppValidations.formatPhoneNumber(customerPhone),
                    ),
                  if (_detectTimeValue(order) != null && _detectTimeValue(order) != '')
                    _buildItem(
                      title: l10n.orderTime,
                      isBorder: hasPOSItem || hasPaymentItem,
                      value: DateTimeHelper.dateTimeLongFormat(
                        _detectTimeValue(order),
                        timeZone: timeZone,
                        isWeekDay: false,
                        langLocale: Localizations.localeOf(context).languageCode,
                      ),
                    ),
                  if (isFromPOS)
                    _buildItem(
                      title: l10n.orderFrom,
                      isBorder: hasPaymentItem,
                      value: 'Square',
                    ),
                  if (!isNewDelivery && order.paymentMethodType?.toUpperCase() == OrderPaymentAtStore.PAY_AT_STORE.name) ...[
                    _buildItem(
                      title: l10n.paymentMethod,
                      isBorder: false,
                      widget: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: _buildPayAtStore(l10n),
                      ),
                    ),
                  ] else if (!isNewDelivery && order.metadata != null && order.metadata?.last4 != null) ...[
                    _buildItem(
                      title: l10n.paymentMethod,
                      isBorder: false,
                      widget: _buildCard(order.metadata),
                    ),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildPayAtStore(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: AppColors.orderingProductThumbnailGBColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        l10n.payAtStore,
        textAlign: TextAlign.center,
        style: const TextStyle(
          color: AppColors.appBlackColor,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  DateTime? _detectTimeValue(Order? order) {
    return order?.createdAt;
  }

  String _detectTimeTitle(BuildContext context, String? status) {
    if (status == null || status.isEmpty) {
      return '';
    }

    final l10n = context.l10n;
    switch (status.toUpperCase()) {
      case 'PREPARING':
        return l10n.preparingTime;
      case 'READY':
        return l10n.readyTime;
      case 'SCHEDULED':
        return l10n.scheduledOrder;
      case 'CANCELLED':
        return l10n.cancelTime;
      case 'COMPLETED':
        return l10n.completedTime;

      default:
        return '';
    }
  }

  Widget _buildItem({
    String title = '',
    String? value,
    Widget? widget,
    bool isBorder = true,
  }) {
    return Container(
      padding: const EdgeInsets.only(top: 12, bottom: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isBorder ? AppColors.appBorderColor : AppColors.appTransparentColor,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          if (widget != null) ...{Expanded(child: widget)} else
            Expanded(
              child: Text(
                value ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlackColor,
                ),
                textAlign: TextAlign.right,
              ),
            )
        ],
      ),
    );
  }

  Widget _buildCard(MetadataCard? card) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (BillingDefine().getIconCredit(card?.wallet ?? '') != '')
          Padding(
            padding: const EdgeInsets.only(right: 4.0),
            child: SvgPicture.asset(
              BillingDefine().getIconCredit(card?.wallet ?? ''),
              width: 40,
              height: 30,
            ),
          ),
        if (BillingDefine().getIconCredit(card?.brand ?? '') != '')
          SvgPicture.asset(
            BillingDefine().getIconCredit(card?.brand ?? ''),
            width: 40,
            height: 30,
          ),
        Padding(
          padding: const EdgeInsets.only(left: 2.0),
          child: RichText(
            textAlign: TextAlign.right,
            text: TextSpan(
              children: <InlineSpan>[
                WidgetSpan(
                  child: Transform.translate(
                    offset: const Offset(0, 4),
                    child: const Text(
                      '**** ',
                      style: TextStyle(
                        fontSize: 18,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                WidgetSpan(
                  child: Text(
                    card?.last4 ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.appBlackColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class OrderDeliveryHeaderDetail extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderDeliveryHeaderDetail({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final timeZone = context.read<AuthBloc>().state.businessTimeZone;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return BlocBuilder<OrderHistoryDetailCubit, OrderHistoryDetailState>(
          builder: (context, state) {
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1,
                    color: AppColors.appBGAvatarColor,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(
                      l10n.deliveryDetails.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (order.deliveryData?.courier != null)
                    _buildItem(
                      title: l10n.courier,
                      value: order.deliveryData?.courier?.name ?? '',
                    ),
                  if (order.deliveryData?.pickupReady != null)
                    _buildItem(
                      title: l10n.pickedUp,
                      value: DateTimeHelper.dateTimeLongFormat(
                        order.deliveryData?.pickupReady,
                        timeZone: timeZone,
                        isWeekDay: false,
                        langLocale: Localizations.localeOf(context).languageCode,
                      ),
                    ),
                  if (checkStatusDeliveryError(order)) ...{
                    _buildItem(
                        title: l10n.deliveryStatus,
                        widget: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              checkStatusDeliveryErrorTitle(l10n, order),
                              textAlign: TextAlign.right,
                              style: const TextStyle(
                                fontSize: 16,
                                color: AppColors.orderingProductTypeColor,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            if (checkStatusDeliveryError(order) && checkStatusDelivery(l10n, order) != null && checkStatusDelivery(l10n, order) != '')
                              Text(
                                checkStatusDelivery(l10n, order) ?? '',
                                textAlign: TextAlign.right,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: AppColors.orderingIntegrationTextColor,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                          ],
                        )),
                  } else if (order.completedAt != null) ...{
                    _buildItem(
                      title: l10n.delivered,
                      value: DateTimeHelper.dateTimeLongFormat(
                        order.completedAt,
                        timeZone: timeZone,
                        isWeekDay: false,
                        langLocale: Localizations.localeOf(context).languageCode,
                      ),
                    ),
                  },
                  _buildItem(
                    title: l10n.deliveryAddress,
                    value: order.deliveryAddress?.street ?? '',
                    isBorder: false,
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  String checkStatusDeliveryErrorTitle(AppLocalizations l10n, Order? order) {
    if (order?.deliveryStatus == OrderStatusDelivery.RETURNED.name || order?.deliveryStatus == OrderStatusDelivery.FAILED.name) {
      return l10n.failedOrder;
    } else if (order?.deliveryStatus == OrderStatusDelivery.CANCELLED.name) {
      return l10n.canceledOrder;
    }
    return '';
  }

  bool checkStatusDeliveryError(Order? order) {
    if (order?.deliveryStatus == OrderStatusDelivery.RETURNED.name || order?.deliveryStatus == OrderStatusDelivery.FAILED.name || order?.deliveryStatus == OrderStatusDelivery.CANCELLED.name) {
      return true;
    }
    return false;
  }

  String? checkStatusDelivery(AppLocalizations l10n, Order? order) {
    final deliveryStatus = order?.deliveryStatus?.toUpperCase();
    final courierImminent = order?.deliveryData?.courierImminent ?? false;

    if (deliveryStatus == OrderStatusDelivery.PICKUP_IN_PROGRESS.name) {
      if (courierImminent) {
        return l10n.courierIsArrivingSoonPleasePrepareTheOrder;
      } else {
        return l10n.courierIsHeadingToThePickup;
      }
    } else if (deliveryStatus == OrderStatusDelivery.DROPOFF_IN_PROGRESS.name || deliveryStatus == OrderStatusDelivery.PICKED_UP.name) {
      if (courierImminent) {
        return l10n.courierIsPreparingForDropoff;
      } else {
        return l10n.courierIsHeadingYourWay;
      }
    } else if (deliveryStatus == OrderStatusDelivery.DELIVERED.name) {
      return l10n.deliveredOrder;
    } else if (deliveryStatus == OrderStatusDelivery.RETURNED.name || deliveryStatus == OrderStatusDelivery.FAILED.name || deliveryStatus == OrderStatusDelivery.CANCELLED.name) {
      final undeliverableReason = order?.deliveryData?.undeliverableReason;
      if (undeliverableReason == OrderDeliveryUndeliverableReason.CUSTOMER_REJECTED_ORDER.name) {
        return l10n.customerRefusedDelivery;
      } else if (undeliverableReason == OrderDeliveryUndeliverableReason.CANNOT_ACCESS_CUSTOMER_LOCATION.name || undeliverableReason == OrderDeliveryUndeliverableReason.CANNOT_FIND_CUSTOMER_ADDRESS.name || undeliverableReason == OrderDeliveryUndeliverableReason.CUSTOMER_UNAVAILABLE.name) {
        return l10n.cannotFindCustomer;
      }
    }
    return null;
  }

  Widget _buildItem({
    String title = '',
    String? value,
    Widget? widget,
    bool isBorder = true,
  }) {
    return Container(
      padding: const EdgeInsets.only(top: 12, bottom: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isBorder ? AppColors.appBorderColor : AppColors.appTransparentColor,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.left,
            ),
          ),
          if (widget != null) ...{Expanded(child: widget)} else
            Expanded(
              child: Text(
                value ?? '',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.appBlackColor,
                ),
                textAlign: TextAlign.right,
              ),
            )
        ],
      ),
    );
  }
}

class OrderItemsList extends StatelessWidget {
  final Order order;
  final List<ItemOrder> items;

  const OrderItemsList({
    Key? key,
    required this.order,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final currencyBusiness = order.currencyCode ?? context.read<AuthBloc>().state.currencyBusiness ?? 'USD';
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final modifiers = item.modifiers ?? [];
        final itemPrice = CurrencyHelper.convertMoney('${item.subtotal ?? 0}', code: currencyBusiness);
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Text(
                    '${item.quantity ?? 1} x',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 8, 16, 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                item.title ?? '',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.appBlackColor,
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 16),
                              child: Text(
                                item.isVoucherFreeItem == true ? l10n.free : itemPrice,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (item.notes != null && item.notes != '') ...[
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFEF3EB),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              item.notes ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Color(0xFFC25D19),
                              ),
                            ),
                          ),
                        ],
                        if (modifiers.isNotEmpty) ...[
                          const SizedBox(height: 12),
                          ListView.separated(
                            separatorBuilder: (context, index) => const Divider(
                              thickness: 1,
                              height: 17,
                              color: Color(0xFFEBEBEB),
                            ),
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: modifiers.length,
                            itemBuilder: (context, index) {
                              final modifier = modifiers[index];
                              final modifierPrice = (modifier.option != null && modifier.option?.price != null && num.parse((modifier.option!.price ?? 0).toString()) != 0 && num.parse((modifier.option!.price ?? 0).toString()) != 0.0) ? CurrencyHelper.convertMoney('${modifier.option?.price ?? 0}', code: currencyBusiness) : null;

                              return Text(
                                '${modifier.option?.name}${modifierPrice != null ? ' ($modifierPrice)' : ''}',
                                textAlign: TextAlign.left,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Color(0xFF707070),
                                ),
                              );
                            },
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16),
              child: Divider(color: Colors.grey.shade300),
            ),
          ],
        );
      },
    );
  }
}

class OrderSummary extends StatelessWidget {
  final Order order;
  final AppLocalizations l10n;

  const OrderSummary({
    Key? key,
    required this.order,
    required this.l10n,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final currencyBusiness = order.currencyCode ?? context.read<AuthBloc>().state.currencyBusiness ?? 'USD';

    final applicationFee = num.parse(order.applicationFee.toString()) > 0 ? CurrencyHelper.convertMoney('${order.applicationFee ?? 0}', code: currencyBusiness) : null;
    final deliveryFee = num.parse(order.deliveryFee.toString()) >= 0 ? CurrencyHelper.convertMoney('${order.deliveryFee ?? 0}', code: currencyBusiness) : null;
    final discountTotal = num.parse(order.discountTotal.toString()) > 0 ? CurrencyHelper.convertMoney('${order.discountTotal ?? 0}', code: currencyBusiness) : null;

    final taxTotal = num.parse(order.taxTotal.toString()) > 0 ? CurrencyHelper.convertMoney('${order.taxTotal ?? 0}', code: currencyBusiness) : null;
    final subtotal = CurrencyHelper.convertMoney('${order.subtotal ?? 0}', code: currencyBusiness);
    final total = CurrencyHelper.convertMoney('${order.total ?? 0}', code: currencyBusiness);
    final isNewDelivery = order.isDeliveryExpress ?? false;
    final tipAmount = num.parse(order.tipAmount.toString()) > 0 ? CurrencyHelper.convertMoney('${order.tipAmount ?? 0}', code: currencyBusiness) : null;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Padding(
          //   padding: const EdgeInsets.only(bottom: 8),
          //   child: Text(
          //     '$itemsCount $itemsCountText',
          //     style: const TextStyle(
          //       fontSize: 16,
          //       fontWeight: FontWeight.bold,
          //     ),
          //     textAlign: TextAlign.left,
          //   ),
          // ),

          /// SUBTOTAL PRICE
          if (!isNewDelivery && subtotal != null && subtotal != '') ...[
            // Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.odHistorySubtotal,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  Text(
                    subtotal,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          /// TAX TOTAL
          if (!isNewDelivery && taxTotal != null && taxTotal != '') ...[
            Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.odHistoryTax,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  Text(
                    taxTotal,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (order.discounts != null && order.discounts!.isNotEmpty) ...[
            Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                l10n.discounts,
                                style: const TextStyle(
                                  color: AppColors.appBlackColor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (discountTotal != null && discountTotal != '') ...[
                              const SizedBox(width: 16),
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0),
                                child: Text(
                                  '-$discountTotal',
                                  style: const TextStyle(
                                    color: AppColors.appResendColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          primary: false,
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemBuilder: (context, index) {
                            final discount = order.discounts?[index];
                            return Padding(
                              padding: const EdgeInsets.only(top: 4.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    ' • ',
                                    style: TextStyle(
                                      color: AppColors.appBlackColor.withValues(alpha: .56),
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      discount?.rewardName ?? discount?.voucherName ?? '',
                                      style: TextStyle(
                                        color: AppColors.appBlackColor.withValues(alpha: .56),
                                        fontWeight: FontWeight.w400,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                          itemCount: order.discounts?.length ?? 0,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (applicationFee != null && applicationFee != '') ...[
            Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.serviceFee,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  Text(
                    applicationFee,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (order.type?.toUpperCase() == OrderType.DELIVERY.name && deliveryFee != null && deliveryFee != '') ...[
            if (isNewDelivery) const SizedBox(height: 16) else Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.deliveryFee,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  // Text(
                  //   deliveryFee,
                  //   style: const TextStyle(
                  //     fontSize: 16,
                  //     fontWeight: FontWeight.w500,
                  //     color: AppColors.appBlackColor,
                  //   ),
                  // ),

                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if ((order.deliveryFeeSupport ?? 0) > 0) ...[
                        Padding(
                          padding: const EdgeInsets.only(right: 4.0),
                          child: Text(
                            CurrencyHelper.convertMoney((order.deliveryFeeTotal ?? 0).toString(), code: order.currencyCode ?? 'USD'),
                            style: const TextStyle(
                              color: AppColors.primaryBackColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ),
                        Text(
                          deliveryFee,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.appResendColor,
                          ),
                        ),
                      ] else ...[
                        Text(
                          deliveryFee,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ]
                    ],
                  ),
                ],
              ),
            ),
          ],
          if (tipAmount != null && tipAmount != '') ...[
            Divider(color: Colors.grey.shade300),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    l10n.tip,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  Text(
                    tipAmount,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ],
              ),
            ),
          ],

          /// TOTAL PRICE
          Divider(color: Colors.grey.shade300),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.odHistoryTotal,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlackColor,
                  ),
                ),
                Text(
                  total,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appBlackColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
