// ignore_for_file: empty_catches, unused_local_variable, inference_failure_on_instance_creation, cast_nullable_to_non_nullable, strict_raw_type, use_build_context_synchronously, unawaited_futures, cancel_subscriptions

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ably_flutter/ably_flutter.dart' as ably;
import 'package:audioplayers/audioplayers.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/ably.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@common/sse-service.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/business-features/business-features.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-ably/call-button-ably.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-sound/call-button-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/chat-messages.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-support-count-badge/chat-support-count-badge.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-account/multiple-account.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multiple-home-list/multiple-home-list.cubit.dart';
import 'package:stickyqrbusiness/@core/store/network/network.cubit.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/notification-fcm/notification_cubit.dart';
import 'package:stickyqrbusiness/@core/store/onboarding/onboarding.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-change-tab/order-active-change-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-auto-reload/order-detail-auto-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-for-print/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-new-notification/order-new-notification.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims-sound/point-claims-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/print-reward/print.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-local/printers-local.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@core/store/role-permission/role-permission.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/first-screen/first-screen-page.dart';
import 'package:stickyqrbusiness/pages/login/login-page.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/kitchen-ticket-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/star-customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/star-kitchen-ticket-template.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/setting-menu-define.dart';
import 'package:stickyqrbusiness/pages/start-page/start-page.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key, this.screenArgs}) : super(key: key);
  final ScreenArguments? screenArgs;

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with WidgetsBindingObserver {
  late final l10n = context.l10n;
  AppSSEService? _appSSEService;
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;
  late AudioPlayer _audioPlayer;

  StreamSubscription? _subscription;
  StreamSubscription? _sseEventSubscription;
  late final AudioPlayer _audioPlayerCallButton = AudioPlayer();
  bool enableSoundNewPointClaim = false;
  int beepRepeat = 3;
  int beepName = 1;
  int beepRepeatMax = 0;
  String path = 'sounds/tink.mp3';

  Timer? _timerCallButton;
  Timer? _debounce;

  final Map<String, bool> _printerInProgress = {};

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _subscription?.cancel();
    WidgetsFlutterBinding.ensureInitialized();
    // Initialize WebView cho iOS
    // WebViewPlatform.instance ??= WebKitWebViewPlatform();
    WakelockPlus.enable();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
    _playSoundLoop();
  }

  @override
  void didChangeDependencies() {
    try {
      initConnectivity();
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    } catch (e) {}
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    AppAbly.cancelChannel();
    WidgetsBinding.instance.removeObserver(this);
    _connectivitySubscription.cancel();
    _audioPlayer.dispose();
    _audioPlayerCallButton.dispose();
    _timerCallButton?.cancel();
    _debounce?.cancel();

    // Cancel SSE subscription and dispose service
    _sseEventSubscription?.cancel();
    _appSSEService?.dispose();

    clearPrinterStates();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        if (context.read<AuthBloc>().state.authStatus == AuthenticationStatus.authenticated) {
          context.read<QrStatisticsCubit>().onBusinessStatistics(isLoading: false);
          if (context.read<AuthBloc>().state.checkRole(PermissionBusiness.cb_manage_customers_requests.name)) {
            context.read<CallButtonCubit>().getSections(withLoading: false);
          }
        }

        // AppLog.e('app in resumed from background');
        break;
      case AppLifecycleState.inactive:
        // AppLog.e('app is in inactive state');
        break;
      case AppLifecycleState.paused:
        // AppLog.e('app is in paused state');
        break;
      case AppLifecycleState.detached:
        // AppLog.e('app has been removed');
        break;
      default:
    }
  }

  @override
  Widget build(BuildContext contextMain) {
    final l10n = contextMain.l10n;
    return MultiBlocListener(
      listeners: [
        BlocListener<NotificationCubit, NotificationState>(
          listener: (context, state) {
            // AppLog.e('NotificationCubit Listener: ${state.toString()}', logType: AppLoggerType.BLOC_CUBIT, classParent: 'AppBloc');
            if (state is NotificationLoaded) {
              _onFCMChanged(context, state);
            }
          },
        ),
        BlocListener<AppBloc, AppState>(
          listener: (context, state) {
            if (state is AppGlobalError) {
              _onAppGlobalError(context, state);
            }
          },
        ),
        BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            try {
              if (state.isAuthenticated) {
                _intialAblyLoggedIn(contextMain, uid: state.uid, bid: state.bid, businessRoleId: state.roleIdBusiness);

                _intialFCMLoggedIn(contextMain, uid: state.uid, bid: state.bid, businessRoleId: state.roleIdBusiness);
                if (state.business != null) {
                  _doGetData(state.business!);
                  _onSettingSound(state.business!);
                }
                final token = state.accessToken;
                _initializeSSE(token: token ?? '');
              }
              switch (state.authStatus) {
                case AuthenticationStatus.unauthenticated:
                  // Dispose SSE service when logout
                  if (_appSSEService != null) {
                    AppLog.e('User logged out, disposing SSE service...');
                    _sseEventSubscription?.cancel();
                    _appSSEService!.dispose();
                    _appSSEService = null;
                  }
                  if (state.isUnauthenticatedGoHome == true) {
                    AppBased.goBackAllPage(context);
                  }
                  break;
                case AuthenticationStatus.authenticated:
                  if (state.business != null) {
                    try {
                      _doGetData(state.business!);
                      _onSettingSound(state.business!);
                      final isPointClaims = state.business?.acceptCustomerClaimsPoint;
                      if (isPointClaims == true) {
                      } else {
                        _audioPlayer.stop();
                      }
                      final enableCallButton = state.enableCallButton;
                      if (enableCallButton == false) {
                        _timerCallButton?.cancel();
                        _audioPlayerCallButton.stop();
                      }
                      // Future.delayed(const Duration(milliseconds: 1000), () {
                      //   AppLog.e('doGetMultipleHome index???????? splash');
                      //   context.read<HomeDefaultWidgetCubit>().onChangeWidget();
                      // });
                    } catch (e) {}
                  }

                  break;
                default:
                  break;
              }
            } catch (e) {}
          },
        ),
        BlocListener<NetworkCubit, NetworkState>(
          listener: (context, state) {
            final String language = Localizations.localeOf(context).languageCode;
            final l10n = context.l10n;
            // if (state.checkNetworkNow == true) {}
            if (state.status == NetworkStatus.OFFLINE) {
              AppLog.e('OFFLINE');
              context.read<PrintCubit>().doCheckLabelPrinter().then((value) {
                if (!value) {
                  context.read<PrintCubit>().doCheckLabelPrinter();
                }
              });
              try {
                AppBased.openSnackBarNetwork(
                  context,
                  msgNotNetwork: l10n.titleNotNetwork,
                  msgNetwork: l10n.titleNetwork,
                  duration: const Duration(hours: 3),
                  onChange: (value) {
                    Future.delayed(const Duration(seconds: 1), () {
                      try {
                        if (mounted) context.read<QrStatisticsCubit>().onBusinessStatistics(isLoading: true);
                      } catch (e) {}
                    });
                  },
                );
              } catch (e) {}
            } else if (state.status == NetworkStatus.ONLINE) {
              AppLog.e('ONLINE');
              context.read<PrintCubit>().doCheckLabelPrinter().then((value) {
                if (!value) {
                  context.read<PrintCubit>().doCheckLabelPrinter();
                }
              });
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            }
          },
        ),
        BlocListener<OnboardingCubit, OnboardingState>(
          listener: (context, state) {
            if (state.status == OnboardingStatus.Success) {
              final isCompleted = state.onboarding?.isCompleted ?? true;
              if (state.onboarding != null && state.onboarding?.isCompleted == false) {
                context.read<OnboardingCubit>().onUpdateExpandedFAB(true);
              }
            }
          },
        ),
        BlocListener<PointClaimsSoundCubit, PointClaimsSoundState>(
          listener: (context, state) {
            switch (state.status) {
              case PointClaimsSoundStateStatus.Play:
                return;
              case PointClaimsSoundStateStatus.Pause:
                return;
              case PointClaimsSoundStateStatus.Stop:
                _audioPlayer.stop();
                context.read<PointClaimsSoundCubit>().onChangeStatus(PointClaimsSoundStateStatus.Initial);

                // _audioPlayer.setReleaseMode(ReleaseMode.stop);

                return;
              default:
            }
          },
        ),
        BlocListener<CallButtonSoundCubit, CallButtonSoundState>(
          listener: (context, state) {
            // print('status sound = ${state.status}');
            // print('update sound cubit: enableSound = ${state.enableSound} /// soundName = ${state.soundName} //// soundRepeat = ${state.soundRepeat}');
            if (state.soundRepeat == 0) {
              state.onDetailPage ? _onPauseSound() : _onHandlePlaySound();
            }
            switch (state.status) {
              case CallButtonSoundStatus.Play:
                _onHandlePlaySound();
                return;
              case CallButtonSoundStatus.Pause:
                _onPauseSound();
                _audioPlayerCallButton.stop();
                // _audioPlayerCallButton.audioCache.clearAll();
                context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Initial);
                return;
              case CallButtonSoundStatus.Stop:
                _onPauseSound();
                _audioPlayerCallButton.stop();
                _subscription?.cancel();
                // _audioPlayerCallButton.audioCache.clearAll();
                context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Initial);
                return;
              default:
            }
          },
        ),
        BlocListener<CallButtonCubit, CallButtonState>(
          listener: (context, state) {
            final status = state.status;
            if (status == CallButtonStatus.Success) {
              final allSections = state.allSections ?? [];
              final isHaveRequest = allSections.any((section) => section.tables!.any((table) => table.requests!.isNotEmpty));
              final soundRepeat = context.read<CallButtonSoundCubit>().state.soundRepeat;
              final typeRequest = context.read<CallButtonAblyCubit>().state.type;
              // AppLog.e('isHaveRequest 1111 ====== $isHaveRequest');
              if (!isHaveRequest) {
                context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Stop);
              } else {
                if (soundRepeat == 0 && typeRequest != TypeRealtime.callButtonSendRequest) {
                  // context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Play);
                  _onHandlePlaySound();
                }
              }
              try {
                _timerCallButton?.cancel();
                // timerSartCalButton();
              } catch (e) {}
            }
          },
        ),
        BlocListener<OrderDetailForPrintCubit, OrderDetailForPrintState>(
          listener: (context, state) async {
            final status = state.status;
            if (status == OrderDetailForPrintStatus.Success) {
              final order = state.order;
              if (order != null) {
                final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
                final EpsonPrinterKitchenTicket kitchenPrint = EpsonPrinterKitchenTicket();
                final EpsonPrinterReceipt customerPrint = EpsonPrinterReceipt();
                final StarPrinterReceipt starPrinterReceipt = StarPrinterReceipt();
                final StarPrinterKitchen starPrinterKitchen = StarPrinterKitchen();
                final printersReceipt = context.read<PrintersLocalCubit>().state.printersReceiptEnable ?? [];
                final printersKitchenTicket = context.read<PrintersLocalCubit>().state.printersKitchenTicketEnable ?? [];
                final authUser = context.read<AuthBloc>().state.user;
                if (printersKitchenTicket.isNotEmpty) {
                  await onPrintKitchenTicket(
                    l10n: l10n,
                    order: order,
                    timeZone: timeZone,
                    printersKitchen: printersKitchenTicket,
                    kitchenPrint: kitchenPrint,
                    starPrinterKitchen: starPrinterKitchen,
                    authUser: authUser ?? User(),
                  );
                }
                if (printersReceipt.isNotEmpty) {
                  await Future.delayed(const Duration(milliseconds: 1500));
                  await onPrintCustomerReceipt(
                    l10n: l10n,
                    order: order,
                    printersReceipt: printersReceipt,
                    epsonPrintReceipt: customerPrint,
                    starPrinterReceipt: starPrinterReceipt,
                  );
                }
                await Future.delayed(const Duration(milliseconds: 1000), () {
                  context.read<OrderDetailForPrintCubit>().onResetStatus();
                });
              }
            }
          },
        ),
        BlocListener<NewDeliveryCubit, NewDeliveryState>(
          listener: (context, state) {
            if (state.status == NewDeliveryStatus.Success) {
              Future.delayed(const Duration(milliseconds: 500), () {
                context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
              });
            }
          },
        ),
      ],
      child: GestureDetector(
        onTap: () {
          try {
            final currentFocus = FocusScope.of(context);
            if (!currentFocus.hasPrimaryFocus) {
              currentFocus.unfocus();
            }
          } catch (e) {
            // AppLog.e('Error', stackTrace: e);
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            switch (state.authStatus) {
              case AuthenticationStatus.firstOpen:
                return const FirstScreenPage();
              case AuthenticationStatus.unauthenticated:
                return const LoginPage();
              case AuthenticationStatus.authenticated:
                return const StartPage();
              case AuthenticationStatus.unknown:
                return const Scaffold(
                  body: Center(
                    child: Text('Unknown Error'),
                  ),
                );
              default:
                return const Scaffold(
                  body: Center(
                    child: SizedBox(
                      width: 48,
                      height: 48,
                      child: CircularProgressIndicator(),
                    ),
                  ),
                );
            }
          },
        ),
      ),
    );
  }

  void _buildCompleted() {
    AppHttp.initContext(context);
    AppError.initContext(context);
    _initCubit();
  }

  void _doGetData(Business business) {
    try {
      enableSoundNewPointClaim = business.enableSoundNewPointClaim ?? false;
      if (enableSoundNewPointClaim != true) {
        _audioPlayer.stop();
      }
      beepRepeat = business.soundNewPointClaimsRepeat ?? 3;
      beepName = business.soundNewPointClaimName ?? 1;
      path = setAssetSourceBeep(beepName);
      // AppLog.e('setState sound: $enableSoundNewPointClaim - $beepRepeat - $beepName - $path');
      setState(() {});
    } catch (e) {
      AppLog.e('_doGetData sound: $e');
    }
  }

  void _onSettingSound(Business business) {
    try {
      final isRepeat = business.callButtonSoundRepeat ?? 3;
      context.read<CallButtonSoundCubit>().onChangeSoundSetting(
            enableSound: business.enableCallButtonSound ?? false,
            soundName: business.callButtonSoundName ?? 1,
            soundRepeat: business.callButtonSoundRepeat ?? 3,
            status: isRepeat != 0 ? CallButtonSoundStatus.Pause : CallButtonSoundStatus.Play,
          );
    } catch (e) {}
  }

  void _initCubit() {
    try {
      final notificationCubit = context.read<NotificationCubit>();
      AppFCM.fcmCubit(notificationCubit);
      AppFCM.onTerminatedAppTap();
      _updateLocalStartedGuide();
      Future.delayed(
        const Duration(milliseconds: 1000),
        () {
          try {
            final loggedIn = context.read<AuthBloc>().state.isLoggedIn;
            if (loggedIn) {
              context.read<OnboardingCubit>()
                ..onUpdateExpandedFAB(false)
                ..onGetOnboarding();
            }
          } catch (e) {}
        },
      );
    } catch (e) {}
  }

  String topic1 = '';
  String topic2 = '';
  Future<void> _intialFCMLoggedIn(
    BuildContext context, {
    required String uid,
    required String bid,
    required String businessRoleId,
  }) async {
    try {
      final permission = await AppFCM.requestPermission();
      if (uid != '' && bid != '') {
        if (topic1 != '' && topic2 != '') {
          return;
        }
        topic1 = bid;
        topic2 = '$bid-$uid';
        await AppFCM.subscribeTopic(topic1, topic2);
      }
    } catch (e) {}
  }

  Future<void> _initializeSSE({required String token}) async {
    try {
      if (token != '' && token != null) {
        // Dispose old SSE service if exists
        if (_appSSEService != null) {
          AppLog.e('Disposing old SSE service...');
          _sseEventSubscription?.cancel();
          await _appSSEService!.disconnect();
          _appSSEService!.dispose();
          _appSSEService = null;
        }

        AppLog.e('Creating new SSE service...');
        _appSSEService = SSEClientBuilder()
            .url(AppBased.appEnv.sseURL ?? '')
            .bearerToken(token)
            .connectTimeout(const Duration(seconds: 30))
            .receiveTimeout(const Duration(seconds: 120))
            .retryConfig(
              maxAttempts: 3,
              initialDelay: const Duration(seconds: 10),
              delayMultiplier: 2.0,
              maxDelay: const Duration(seconds: 120),
            )
            .autoReconnect(true)
            .build();

        _setupListeners();

        try {
          await _connect();
        } catch (e) {
          AppLog.e('Auto connect failed: $e');
        }
      }
    } catch (e) {
      AppLog.e('_initializeSSE error: $e');
    }
  }

  Future<void> _connect() async {
    try {
      if (_appSSEService == null) {
        AppLog.e('SSE service is null, cannot connect');
        return;
      }

      // Reset reconnection state before connecting
      _appSSEService!.resetReconnection();

      AppLog.e('Bắt đầu kết nối SSE...');
      await _appSSEService!.connect();
      AppLog.e('Yêu cầu kết nối SSE đã được gửi');
    } catch (e) {
      AppLog.e('Lỗi kết nối SSE: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi kết nối: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _setupListeners() {
    if (_appSSEService == null) {
      AppLog.e('SSE service is null, cannot setup listeners');
      return;
    }

    // Lắng nghe tất cả events (heartbeat đã được filter ở service level)
    _sseEventSubscription = _appSSEService!.events.listen(
      (event) {
        AppLog.e('SSE Event received message ????: ${event.event} - ${event.data}');
        if (event.event.toUpperCase() == 'MESSAGE') {
          if (event.data != null) {
            try {
              // Parse JSON string to Map
              final Map<String, dynamic> jsonData = jsonDecode(event.data) as Map<String, dynamic>;

              final dataSSEMessageEvent = SSEMessageEvent.fromJson(jsonData);
              final Message? message = dataSSEMessageEvent.data;

              if (message != null) {
                // Sync latest messages từ server thay vì chỉ add message từ SSE
                try {
                  if (context.mounted) {
                    final chatCubit = context.read<ChatMessagesCubit?>();
                    final chatCountBadgeCubit = context.read<ChatSupportCountBadgeCubit?>();
                    if (chatCubit != null) {
                      if (message.senderType == null || message.senderType?.toUpperCase() == 'SYSTEM') {
                        chatCubit.syncLatestMessages();
                        // chatCountBadgeCubit?.onChangedBadge();
                      } else {
                        if (message.senderType?.toUpperCase() == 'AI') {
                          message.isSmoothAI = true;
                        }
                        chatCountBadgeCubit?.onChangedBadge();
                        chatCubit.addMessage(message);
                      }
                    }
                  }
                } catch (e) {
                  AppLog.d('ChatMessagesCubit not available in context: $e');
                }
              }
            } catch (e) {
              AppLog.e('Error parsing SSE message data: $e');
            }
          }
        }
      },
      onError: (Object error) {
        AppLog.e('SSE stream error: $error');
      },
      onDone: () {
        AppLog.e('SSE stream closed');
      },
    );
  }

  void _doDebounce({Function? onChange, int milliseconds = 1000}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(Duration(milliseconds: milliseconds), () {
      onChange?.call();
    });
  }

  Future<void> _intialAblyLoggedIn(
    BuildContext context, {
    required String uid,
    required String bid,
    required String businessRoleId,
  }) async {
    try {
      if (bid == null || bid == '') return;
      AppAbly.cancelChannel();
      AppAbly.init(AppBased.appEnv.ablyKey!);
      AppAbly.channel(bid).subscribe().listen((event) async {
        AppLog.e('Ably Message Key: ${event.name}', obj: event);
        // final isPointClaimsAllHome = context.read<HomeDefaultWidgetCubit>().state.isPointClaimsAllHome;
        // final isCallButtonAllHome = context.read<HomeDefaultWidgetCubit>().state.isCallButtonAllHome;

        if (event.name != null) {
          switch (event.name) {
            case 'business.roles.added':
            case 'business.roles.deleted':
            case 'business.roles.changed':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                final ablyData = ablyRoleJsonDecode(data);
                AppLog.e('ROLE ID: ${ablyData?.businessRoleId} - $businessRoleId');
                // if (businessRoleId != '' && ablyData?.businessRoleId == businessRoleId) {
                _getRolePermission();
                // }
              }
              break;
            case 'business.roles.updated':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                final ablyData = ablyRoleJsonDecode(data);

                _getRolePermission();
              }
              break;
            case 'business.user.added':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                final ablyData = ablyRoleJsonDecode(data);

                if (ablyData?.userId == uid) {
                  _getRolePermission();
                }
              }
              break;
            case 'business.user.updated':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                final ablyData = ablyRoleJsonDecode(data);

                if (ablyData?.userId == uid) {
                  _getRolePermission();
                }
              }
              break;
            case 'business.user.deleted':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                final ablyData = ablyRoleJsonDecode(data);

                if (ablyData?.userId == uid) {
                  AppAbly.cancelChannel();
                  context.read<AuthBloc>().add(AuthLogoutRequested(isGoHome: false));
                }
              }
              break;

            case 'business.account.deleted':
              if (event.data != null) {
                AppAbly.cancelChannel();
                context.read<AuthBloc>().add(AuthLogoutRequested(isGoHome: false));
              }
              break;
            case 'business.account.updated':
              try {
                context.read<BusinessFeaturesCubit>().getBusinessFeatures();
                context.read<MultipleAccountCubit>().geMultipleAccount();

                context.read<BusinessProfileCubit>().getBusinessProfile().then(
                  (value) {
                    context.read<AuthBloc>().add(AuthStarted());
                    if (value != null) {
                      _onSettingSound(value);
                      _doGetData(value);
                      final isPintClaims = value.acceptCustomerClaimsPoint;
                      if (isPintClaims == true) {
                        context.read<PointClaimsCubit>().getPointClaims(isLoading: false);
                      }
                    }
                  },
                );

                context.read<MultipleHomeListCubit>().doUpdateMultipleHomes().then((value) {
                  context.read<HomeDefaultWidgetCubit>().onChangeCurrentWidget();
                  // context.read<HomeDefaultWidgetCubit>().onChangeWidget();
                });
              } catch (e) {
                AppLog.e('usiness.account.updated error: $e');
              }
              break;
            case 'business.multiple_home.changes':
              try {
                context.read<MultipleHomeListCubit>().doUpdateMultipleHomes().then((value) {
                  context.read<HomeDefaultWidgetCubit>().onChangeCurrentWidget();
                });
              } catch (e) {}
              break;
            case 'business.point_claims.new':
              if (event.data != null) {
                final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                setPointClaimNew(data);
                beepRepeatMax = 0;
                _playSound();
              }
              break;

            case 'business.point_claims.given':
              try {
                context.read<PointClaimsCubit>().getPointClaims(isLoading: false);
                context.read<QrStatisticsCubit>().onBusinessStatistics();
              } catch (e) {}
              break;

            case 'business.point_claims.rejected':
              if (event.data != null) {
                context.read<PointClaimsCubit>().getPointClaims(isLoading: false).then((value) {
                  if (value.isEmpty) {
                    _audioPlayer.stop();
                  }
                });
              }
              break;
            case 'business.point_claims.rejected_all':
              try {
                context.read<PointClaimsCubit>().onChangeRemoveAllItem();
                _audioPlayer.stop();
              } catch (e) {}
              break;

            case 'business.customers.offer_claimed':
              break;
            case 'business.customers.points_earned':
            case 'business.customers.reward_redeemed':
            case 'business.customers.offer_redeemed':
            case 'business.customers.referred_points_earned':
            case 'business.customers.referrer_points_earned':
            // context.read<QrStatisticsCubit>().onChangedStatistics(); // +1 khi có notif
            // context.read<QrStatisticsCubit>().onBusinessStatistics(); // reload data
            // break;
            case 'business.customers.void_points_earned':
            case 'business.customers.void_reward_redeemed':
              try {
                context.read<QrStatisticsCubit>().onBusinessStatistics(); // reload data
              } catch (e) {}
              break;
            case 'business.call_button.customer_join_table':
            case 'business.call_button.customer_cancel_request':
            case 'business.call_button.all_requests_done':
            case 'business.call_button.request_done':
            case 'business.call_button.leave_table':
            case 'business.call_button.table_changed':
            case 'business.call_button.customer_checkin':
            case 'business.call_button.customer_checkout':
              if (event.data != null) {
                _callRealTimeCallButton(event);
              }
              context.read<CallButtonCubit>().getSections(withLoading: false);
              break;
            case 'business.call_button.customer_send_request':
              if (event.data != null) {
                _callRealTimeCallButton(event);
                context.read<CallButtonCubit>().getSections(withLoading: false);
                _onHandlePlaySound(isPlaySound: true);
              }
              break;

            case 'business.orders.paid':
              Future.delayed(const Duration(milliseconds: 500), () {
                context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
              });

              final isOpenDetail = context.read<OrderActiveChangeTabCubit>().state.openOrderDetail == OrderActiveOpen.Open;
              if (isOpenDetail) {
                Future.delayed(const Duration(milliseconds: 500), () {
                  context.read<OrderDetailAutoReloadCubit>()
                    ..onChangedCloseDialog(closeDialog: OrderDetailAutoCloseDialogStatus.Close)
                    ..onChangedStatus(status: OrderDetailAutoReloadStatus.Loading);
                });
              }
              break;
            case 'business.orders.updated':
              try {
                final isPermission = context.read<AuthBloc>().state.checkPermissions(PermissionBusiness.orders_active_orders.name, [1, 2, 3, 4]);
                if (event.data != null && isPermission) {
                  final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
                  final order = ablyOrderJsonDecode(data);
                  AppLog.e('LOG order status = ${order?.status} /// id = ${order?.id}');
                  if (order?.status == OrderStatusDefine.PREPARING.name) {
                    context.read<OrderDetailForPrintCubit>().getOrderDetail(id: order?.id ?? '');
                  }
                  if (order?.status == OrderStatusDefine.CONFIRMED.name) {
                    // final isActiveOrdersWidget = context.read<HomeDefaultWidgetCubit>().state.isActiveOrdersWidget;
                    // AppLog.e('isActiveOrdersWidget???????: $isActiveOrdersWidget');
                    // if (isActiveOrdersWidget) {
                    if (order?.scheduledAt != null) {
                      Future.delayed(const Duration(milliseconds: 1000), () {
                        context.read<OrderNewNotificationCubit>().onChangeCountScheduledOrder(order?.id).then((onValue) {
                          if (!onValue) {
                            AppBased.go(
                              context,
                              AppRoutes.orderActiveNotifPage,
                              onChanged: (value) {},
                            );
                          }
                        });
                      });
                    } else {
                      Future.delayed(const Duration(milliseconds: 1000), () {
                        context.read<OrderNewNotificationCubit>().onChangeCountNewOrder(order?.id).then((onValue) {
                          if (!onValue) {
                            AppBased.go(
                              context,
                              AppRoutes.orderActiveNotifPage,
                              onChanged: (value) {},
                            );
                          }
                        });
                      });
                    }
                    // }
                  } else {
                    Future.delayed(const Duration(milliseconds: 500), () {
                      context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
                    });
                  }
                  final isOpenDetail = context.read<OrderActiveChangeTabCubit>().state.openOrderDetail == OrderActiveOpen.Open;
                  if (isOpenDetail) {
                    // OrderDetailCubitRegistry.refreshOrderReload(order?.id ?? '', isLoading: false);
                    Future.delayed(const Duration(milliseconds: 500), () {
                      context.read<OrderDetailAutoReloadCubit>().onChangedStatus(status: OrderDetailAutoReloadStatus.Loading);
                    });
                  }
                }
              } catch (e) {}
              break;

            default:
              break;
          }
        }
      });
    } catch (e) {}
  }

  void _getRolePermission() {
    try {
      context.read<MultipleHomeListCubit>().doUpdateMultipleHomes().then((value) {
        context.read<HomeDefaultWidgetCubit>().onChangeCurrentWidget();
      });
    } catch (e) {}
    try {
      context.read<RolePermissionCubit>().getRolePermission().then((value) {
        context.read<AuthBloc>().add(AuthStarted());
      });
    } catch (e) {}
  }

  static AblyData? ablyJsonDecode(Map<String, dynamic> jsonData) {
    try {
      final AblyData obj = AblyData.fromJson(jsonData);
      AppLog.e('obj == ${jsonEncode(obj)}');
      final tags = json.encode(jsonData['tags']);
      final list = (json.decode(tags) as List).map((item) => item as Map<String, dynamic>).toList();
      final ablyTags = list.map((item) => item['tag']['name'] as String).toList();
      final listAblyTag = ablyTags.map((e) => AplyTags(tag: AblyTag(name: e))).toList();
      obj.tags = listAblyTag;
      return obj;
    } catch (e) {
      AppLog.e('ablyJsonDecode error: $e');
      return null;
    }
  }

  static AblyData? ablyRoleJsonDecode(Map<String, dynamic> jsonData) {
    try {
      final AblyData obj = AblyData.fromJson(jsonData);
      AppLog.e('obj == ${jsonEncode(obj)}');
      // final tags = json.encode(jsonData['tags']);
      // final list = (json.decode(tags) as List).map((item) => item as Map<String, dynamic>).toList();
      // final ablyTags = list.map((item) => item['tag']['name'] as String).toList();
      // final listAblyTag = ablyTags.map((e) => AplyTags(tag: AblyTag(name: e))).toList();
      // obj.tags = listAblyTag;
      return obj;
    } catch (e) {
      AppLog.e('ablyJsonDecode error: $e');
      return null;
    }
  }

  static AblyData? ablyOrderJsonDecode(Map<String, dynamic> jsonData) {
    try {
      final AblyData obj = AblyData.fromJson(jsonData);
      return obj;
    } catch (e) {
      AppLog.e('ablyJsonDecode error: $e');
      return null;
    }
  }

  Future<void> setPointClaimNew(Map<String, dynamic> data) async {
    try {
      final ablyData = ablyJsonDecode(data);
      AppLog.e('ablyData: ${ablyData?.toJson()}');
      final name = (ablyData?.user?.displayName != 'null' && ablyData?.user?.displayName != '' && ablyData?.user?.displayName != null) ? ablyData?.user?.displayName : l10n.guest;

      final itemNew = PointClaim(
        id: ablyData?.id ?? '',
        userId: ablyData?.userId ?? '',
        status: 'ACTIVE',
        businessId: ablyData?.businessId ?? '',
        createdAt: DateTime.parse(ablyData?.createdAt ?? ''),
        updatedAt: DateTime.parse(ablyData?.updatedAt ?? ''),
        user: User(
          id: ablyData?.user?.id ?? '',
          displayName: name,
          phone: ablyData?.user?.phone ?? '',
        ),
        tags: ablyData?.tags?.map((e) => PointClaimsTags(tag: Tag(name: e.tag?.name ?? ''))).toList(),
      );

      context.read<PointClaimsCubit>().onChangeAddNewItem(itemNew);
      AppLog.e('pointClaimNotif: $name');
      AppBased.toastSuccess(context, title: l10n.pointClaimNotif(name ?? ''));
    } catch (e) {}
  }

  void _onAppGlobalError(BuildContext context, AppGlobalError state) {
    final l10n = context.l10n;
    switch (state.type) {
      case AppErrorType.network_error:
        ScaffoldMessenger.of(context)
          ..removeCurrentMaterialBanner()
          ..showMaterialBanner(
            MaterialBanner(
              content: Text(
                state.title ?? l10n.checkNetwork,
              ),
              actions: const [],
            ),
          );
        break;
      default:
    }
  }

  void _onFCMChanged(BuildContext context, NotificationLoaded state) {
    final fcmData = state.data;
    final fcmType = state.type;
    if (fcmData == null) {
      return;
    } else {
      switch (fcmData.type) {
        case TypeNotif.forceLoggedOut:
          context.read<AuthBloc>().add(AuthLogoutRequested(isGoHome: false));
          return;
        case TypeNotif.upgradeQR:
        case TypeNotif.upgradeReferral:
        case TypeNotif.upgradeJoin:
          context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
          return;
        case TypeNotif.customerAppliedOfferCode:
          AppBased.toastSuccess(context, title: fcmData.message);
          return;
        case TypeNotif.businessClaimPointSendNotification:
          // AppBased.toastSuccess(context, title: l10n.pointClaimNotif(fcmData.name ?? fcmData.phone ?? ''));
          return;
        default:
      }
    }
    if (fcmType == FCMActionType.tap) {
      _onFCMTap(context, fcmData);
    } else {
      _onFCMAppOpen(context, fcmData);
    }
  }

  void _onFCMAppOpen(BuildContext context, FcmData fcmData) {
    switch (fcmData.type) {
      case TypeNotif.customerApplyCode:
        // context.read<QrStatisticsCubit>().onChangedStatistics();
        break;
      case TypeNotif.upgradeQR:
      case TypeNotif.upgradeReferral:
      case TypeNotif.upgradeJoin:
        context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
        break;
      case TypeNotif.customerAppliedOfferCode:
        // context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
        return;
      case TypeNotif.businessClaimPointSendNotification:
        // AppBased.toastSuccess(context, title: fcmData.message);
        return;
      case TypeNotif.businessOrdersConfirmed:
        // AppBased.go(context, AppRoutes.orderActivePage);
        return;
      default:
        break;
    }
  }

  void _onFCMTap(BuildContext context, FcmData fcmData) {
    switch (fcmData.type) {
      case TypeNotif.customerApplyCode:
        context.read<QrStatisticsCubit>().onBusinessStatistics(isLoading: false);
        break;
      case TypeNotif.upgradeQR:
      case TypeNotif.upgradeReferral:
      case TypeNotif.upgradeJoin:
        context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
        break;
      case TypeNotif.customerAppliedOfferCode:
        // context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
        return;
      case TypeNotif.businessClaimPointSendNotification:
        // context.read<NotificationCubit>().onIsShowUpgrade(isShow: true, typeNotif: fcmData.type);
        return;
      case TypeNotif.businessOrdersConfirmed:
        AppBased.go(context, AppRoutes.orderActivePage);
        return;
      default:
        break;
    }
  }

  Future<void> initConnectivity() async {
    late ConnectivityResult result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      AppLog.e("Couldn't check connectivity status error: $e");
      return;
    }

    if (!mounted) {
      return Future.value(null);
    }

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(ConnectivityResult connectivityResult) async {
    try {
      if (connectivityResult == ConnectivityResult.none) {
        context.read<NetworkCubit>().onChangedStatus(NetworkStatus.OFFLINE);
      } else {
        context.read<NetworkCubit>().onChangedStatus(NetworkStatus.ONLINE);
      }
    } catch (e) {}
  }

  Future<void> _updateLocalStartedGuide() async {
    await AppLocalStorage.saveStartedGuide(false);
  }

  Future<void> _playSoundLoop() async {
    try {
      _audioPlayer.onPlayerComplete.listen((event) {
        AppLog.e('path: $path - $beepRepeatMax');
        beepRepeatMax++;

        if (beepRepeat == 0) {
          _audioPlayer
            ..setSource(AssetSource(path))
            // if (!Platform.isAndroid)
            ..setReleaseMode(ReleaseMode.loop)
            // ..setReleaseMode(!Platform.isAndroid ? ReleaseMode.loop : ReleaseMode.release)

            ..setVolume(1)
            ..play(AssetSource(path));
        } else {
          if (beepRepeatMax < 3) {
            _audioPlayer.setSource(AssetSource(path));
            // _audioPlayer.setReleaseMode(Platform.isAndroid ? ReleaseMode.release : ReleaseMode.loop);
            if (!Platform.isAndroid) _audioPlayer.setReleaseMode(ReleaseMode.loop);
            _audioPlayer
              ..setVolume(1)
              ..play(AssetSource(path));
          } else {
            _audioPlayer.setReleaseMode(ReleaseMode.stop);
          }
        }
      });

      await _audioPlayer.resume();
    } catch (e) {
      AppLog.e('error _playSoundLoop: $e');
    }
  }

  Future<void> _playSound() async {
    try {
      if (enableSoundNewPointClaim != true) {
        await _audioPlayer.stop();
      }

      path = setAssetSourceBeep(beepName);
      if (enableSoundNewPointClaim != true) {
        await _audioPlayer.setReleaseMode(ReleaseMode.stop);
        return;
      }
      AppLog.e('SOUND _playSound:  $beepRepeat - $path ');
      await _audioPlayer.setSource(AssetSource(path));
      await _audioPlayer.setVolume(1);
      if (Platform.isAndroid) {
        await _audioPlayer.setReleaseMode(ReleaseMode.release);
      }
      await _audioPlayer.play(AssetSource(path));
    } catch (e) {
      AppLog.e('error _playSound: $e');
    }
  }

  String setAssetSourceBeep(int name) {
    switch (name) {
      case 1:
        return 'sounds/tink.mp3';
      case 2:
        return 'sounds/pop.mp3';
      default:
        return 'sounds/beep.mp3';
    }
  }

  void _callRealTimeCallButton(ably.Message event) {
    final String type = event.name.toString();
    final data = Map<String, dynamic>.from(event.data as Map<dynamic, dynamic>);
    // final ablyData = ablyJsonDecode(data);
    final tableId = data['tableId'].toString();
    final callButtonId = data['callButtonId'].toString();
    final serviceId = data['serviceId'].toString();
    final notes = data['notes'].toString();
    final userId = data['user']?['id'].toString() ?? '';
    final checkinId = data['user']?['checkinId'].toString() ?? '';
    AppLog.e('_callRealTimeCallButton 1 = $callButtonId');
    if (mounted) {
      AppLog.e('_callRealTimeCallButton 2 = $callButtonId /// checkinId = $checkinId');
      context.read<CallButtonAblyCubit>().realTimeData(
            type: type,
            data: data.toString(),
            // ablyData: ablyData,
            tableId: tableId,
            callButtonId: callButtonId,
            serviceId: serviceId,
            notes: notes,
            userId: userId,
            checkinId: checkinId,
          );
    }
  }

  void timerSartCalButton() {
    try {
      _timerCallButton = Timer.periodic(const Duration(seconds: 15), (timer) {
        context.read<CallButtonCubit>().autoGetSections().then((value) {
          if (value != null && value == true) {
            AppLog.e('gọi âm thanh...');
            context.read<CallButtonCubit>().getSections(withLoading: false);
            context.read<CallButtonSoundCubit>().onChangeStatus(CallButtonSoundStatus.Play);
          }
        });
      });
    } catch (e) {
      AppLog.e('timerSart error: $e');
    }
  }

  void _onHandlePlaySound({bool? isPlaySound}) {
    final soundState = context.read<CallButtonSoundCubit>().state;
    final isDetail = soundState.onDetailPage;
    final isEnableSound = soundState.enableSound;
    final repeat = soundState.soundRepeat;
    final soundName = soundState.soundName;

    final allSections = context.read<CallButtonCubit>().state.allSections ?? [];
    final isHaveRequest = isPlaySound ?? allSections.any((section) => section.tables!.any((table) => table.isInactive == false && table.requests!.isNotEmpty));
    // print('isDetail == $isDetail \n isEnableSound = $isEnableSound \n repeat = $repeat \n soundName = $soundName \n typeRequest = $typeRequest');
    if (isHaveRequest) {
      if (isEnableSound) {
        if (isDetail) {
          _onPauseSound();
        } else {
          if (repeat == 3) {
            // _audioPlayerCallButton.setReleaseMode(ReleaseMode.stop);
            _onPlayThreeTimes(setAssetSourceBeep(soundName));
          } else {
            _onPlayLoop(setAssetSourceBeep(soundName));
          }
        }
      }
    } else {
      _onPauseSound();
    }
  }

  Future<void> _onPlayLoop(String soundPath) async {
    AppLog.e('_onPlayLoop ///// soundPath = $soundPath');
    if (_audioPlayerCallButton.state == PlayerState.playing) return; // Return if already playing
    _audioPlayerCallButton
      ..setSource(AssetSource(soundPath))
      // if (!Platform.isAndroid)
      ..setReleaseMode(ReleaseMode.loop)
      // ..setReleaseMode(!Platform.isAndroid ? ReleaseMode.loop : ReleaseMode.release)
      ..setVolume(1)
      ..play(AssetSource(soundPath));
  }

  Future<void> _onPauseSound() async {
    await _audioPlayerCallButton.pause();
  }

  Future<void> _onPlayThreeTimes(String soundPath) async {
    try {
      await _audioPlayer.stop();
      await _subscription?.cancel();
      if (_audioPlayer.state == PlayerState.disposed) {
        _audioPlayer = AudioPlayer();
      }
      await _audioPlayer.setReleaseMode(ReleaseMode.release);
      await _audioPlayer.setVolume(1.0);
      const Duration soundDuration = Duration(milliseconds: 1800);
      await _audioPlayer.play(AssetSource(soundPath));
      await Future.delayed(soundDuration);
      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(soundPath));
      await Future.delayed(soundDuration);
      await _audioPlayer.stop();
      await _audioPlayer.play(AssetSource(soundPath));
      await Future.delayed(soundDuration);
      await _audioPlayer.stop();
    } catch (e) {
      await _audioPlayer.stop();
    }
  }

  Future<void> onPrintCustomerReceipt({
    required AppLocalizations l10n,
    required Order order,
    required List<Printer> printersReceipt,
    required EpsonPrinterReceipt epsonPrintReceipt,
    required StarPrinterReceipt starPrinterReceipt,
  }) async {
    if (printersReceipt.isEmpty) {
      // AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    }
    for (final printer in printersReceipt) {
      final ip = printer.address ?? '';
      if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;

      // Kiểm tra xem máy in có đang trong quá trình in không
      final printerKey = '${ip}_receipt';
      if (_printerInProgress[printerKey] == true) {
        AppLog.d('RECEIPT Printer $ip is already printing, skipping...');
        continue;
      }

      // Đánh dấu máy in đang hoạt động
      _printerInProgress[printerKey] = true;

      try {
        final receiptCopies = printer.receiptCopies ?? 1;
        final numberOfCopies = receiptCopies > 0 ? receiptCopies : 1;

        AppLog.d('RECEIPT Printing $numberOfCopies copies for printer $ip with mode ${printer.mode}');

        // In theo số lượng bản sao đã định
        for (int copyIndex = 1; copyIndex <= numberOfCopies; copyIndex++) {
          try {
            if (printer.mode == 'ESC_POS') {
              AppLog.e('RECEIPT PRINT => copyIndex = $copyIndex // numberOfCopies = $numberOfCopies');

              // Thêm delay trước khi in để tránh conflict
              if (copyIndex > 1) {
                await Future.delayed(const Duration(milliseconds: 1000));
              }
              await epsonPrintReceipt.printCustomerReceiptAsText(
                context: context,
                ipAddress: ip,
                order: order,
                pageSize: printer.pageSize ?? '80mm',
              );
              await Future.delayed(const Duration(milliseconds: 800));
            } else if (printer.mode == 'STAR_LINE') {
              await starPrinterReceipt.printReceipt(
                context: context,
                l10n: l10n,
                ipAddress: ip,
                order: order,
              );
              if (copyIndex < numberOfCopies) {
                await Future.delayed(const Duration(milliseconds: 500));
              }
            } else {
              // Xử lý trường hợp mode không được hỗ trợ
              break;
            }

            AppLog.d('RECEIPT Completed copy $copyIndex/$numberOfCopies for printer $ip');
          } catch (e) {
            AppLog.e('RECEIPT Error printing copy $copyIndex for printer $ip: $e');
            // Dừng in các bản sao còn lại nếu có lỗi
            break;
          }
        }
      } catch (e) {
        AppLog.e('RECEIPT Error in printing process for printer $ip: $e');
      } finally {
        // Luôn reset trạng thái máy in
        _printerInProgress[printerKey] = false;
        await Future.delayed(const Duration(milliseconds: 500));
      }
    }
  }

  Future<void> onPrintKitchenTicket({
    required AppLocalizations l10n,
    required User authUser,
    required Order order,
    required String timeZone,
    required List<Printer> printersKitchen,
    required EpsonPrinterKitchenTicket kitchenPrint,
    required StarPrinterKitchen starPrinterKitchen,
  }) async {
    if (printersKitchen.isEmpty) {
      // AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    }
    for (final printer in printersKitchen) {
      final ip = printer.address ?? '';
      if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;

      // Kiểm tra xem máy in có đang trong quá trình in không
      final printerKey = '${ip}_kitchen';
      if (_printerInProgress[printerKey] == true) {
        AppLog.d('DKMMM TICKET Printer $ip is already printing, skipping...');
        continue;
      }
      // Đánh dấu máy in đang hoạt động
      _printerInProgress[printerKey] = true;

      try {
        // Kiểm tra kitchenCategories
        final kitchenCategories = printer.kitchenCategories ?? <String>[];

        Order orderToPrint;
        if (kitchenCategories.isEmpty) {
          AppLog.d('TICKET Printer $ip has empty kitchenCategories, printing full order...');
          orderToPrint = order;
        } else {
          final filteredOrder = _createFilteredOrder(order, kitchenCategories);
          if (filteredOrder == null || _isOrderEmpty(filteredOrder)) {
            AppLog.d('TICKET No items to print for printer $ip');
            continue;
          }
          orderToPrint = filteredOrder;
        }

        AppLog.e('orderToPrint == ${jsonEncode(orderToPrint)}');
        if (printer.mode == 'ESC_POS') {
          await Future.delayed(const Duration(milliseconds: 200));
          await kitchenPrint.printKitchenTicketAsText(
            l10n: l10n,
            timezone: timeZone,
            ipAddress: ip,
            order: orderToPrint,
            printerName: printer.productName ?? printer.name ?? '',
            authUser: authUser,
          );

          await Future.delayed(const Duration(milliseconds: 500));
        } else if (printer.mode == 'STAR_LINE') {
          await starPrinterKitchen.printKitchenTicket(
            context: context,
            l10n: l10n,
            ipAddress: ip,
            order: orderToPrint,
            printerName: printer.productName ?? printer.name ?? '',
            authUser: authUser,
          );
        } else {
          // Xử lý trường hợp mode không được hỗ trợ
          continue;
        }
        AppLog.d('TICKET Printed order for printer $ip with mode ${printer.mode}');
      } catch (e) {
        AppLog.e('TICKET Error printing kitchen ticket for printer $ip: $e');
      } finally {
        // Luôn reset trạng thái máy in
        _printerInProgress[printerKey] = false;
        await Future.delayed(const Duration(milliseconds: 300));
      }
    }
  }

  void clearPrinterStates() {
    _printerInProgress.clear();
  }

  // Hàm tạo order mới chỉ chứa các item được phép in
  Order? _createFilteredOrder(Order? originalOrder, List<String> allowedCategoryIds) {
    if (originalOrder == null) return null;

    // Tạo bản copy của order gốc
    final filteredOrder = originalOrder.copyWith();

    // Lọc các item dựa trên categories được phép
    final filteredItems = <ItemOrder>[];

    for (final item in originalOrder.items ?? <ItemOrder>[]) {
      final productCategories = item.product?.categories ?? <ProductOrderCetegories>[];

      // Kiểm tra xem item có category được phép in không
      bool hasAllowedCategory = false;
      for (final category in productCategories) {
        if (allowedCategoryIds.contains(category.id)) {
          hasAllowedCategory = true;
          break;
        }
      }

      // Nếu item có category được phép thì thêm vào danh sách
      if (hasAllowedCategory) {
        filteredItems.add(item);
      }
    }

    // Cập nhật danh sách items đã lọc
    return filteredOrder.copyWith(items: filteredItems);
  }

  // Hàm kiểm tra order có rỗng không
  bool _isOrderEmpty(Order order) {
    return order.items == null || order.items!.isEmpty;
  }
}
