// ignore_for_file: library_private_types_in_public_api, inference_failure_on_function_invocation, library_prefixes

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/chat-messages.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/chat-messages.state.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/message-send-status.dart';
import 'package:stickyqrbusiness/@core/store/chat-support-count-badge/chat-support-count-badge.cubit.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/chatbot-ai/widgets/@chatbot-ai.dart';

// import 'package:html/parser.dart' show parse;
// import 'package:html/dom.dart' as dom;

class ChatBoxMessagesWidget extends StatefulWidget {
  const ChatBoxMessagesWidget({super.key});

  @override
  State<ChatBoxMessagesWidget> createState() => _ChatBoxMessagesWidgetState();
}

class _ChatBoxMessagesWidgetState extends State<ChatBoxMessagesWidget> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatSupportCountBadgeCubit>().onChangedBadge(clear: true);

      context.read<ChatMessagesCubit>()
        ..clearData()
        ..loadMessages(isRefresh: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final language = Localizations.localeOf(context).languageCode;

    return ChatBoxContentWidget(lang: language);
  }
}

class ChatBoxContentWidget extends StatefulWidget {
  final String lang;

  const ChatBoxContentWidget({super.key, this.lang = 'en'});

  @override
  State<ChatBoxContentWidget> createState() => _ChatBoxContentWidgetState();
}

class _ChatBoxContentWidgetState extends State<ChatBoxContentWidget> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();
  final ValueNotifier<bool> _isInputEmpty = ValueNotifier<bool>(true);
  int _previousMessageCount = 0;
  bool _isLoadingMore = false;
  bool _justSentMessage = false;
  bool _isFirstLoad = true;
  Timer? _loadMoreDebounce;

  final Map<String, GlobalKey> _smoothAIMessageKeys = <String, GlobalKey>{};
  final Set<String> _smoothAIRunningMessages = <String>{};

  @override
  void initState() {
    super.initState();
    _textController.addListener(_updateInputState);
    _scrollController.addListener(_onScroll);
  }

  void _updateInputState() {
    _isInputEmpty.value = _textController.text.trim().isEmpty;
  }

  void _onScroll() {
    if (_scrollController.hasClients && !_isLoadingMore) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      if (maxScroll - currentScroll <= 100) {
        _loadMoreDebounce?.cancel();
        _loadMoreDebounce = Timer(const Duration(milliseconds: 300), () {
          if (!_isLoadingMore) {
            final state = context.read<ChatMessagesCubit>().state;

            if (state.hasMore && state.status != ChatMessagesStatus.loadingMore && state.status != ChatMessagesStatus.loading) {
              context.read<ChatMessagesCubit>().loadMoreMessages();
            }
          }
        });
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0.0,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _scrollToBottomInstant() {
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(0);
    }
  }

  void _showMessageContextMenu(
    BuildContext context,
    Message message,
    bool isUser,
    Offset globalPosition,
  ) {
    final l10n = context.l10n;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DecoratedBox(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                leading: const Icon(Icons.copy, color: AppColors.appBlackColor),
                title: Text(l10n.copy,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.appBlackColor,
                    )),
                onTap: () {
                  Navigator.pop(context);
                  _handleMenuAction('copy', message, context);
                },
              ),
              const SizedBox(height: 32),

              // if (!isUser)
              //   ListTile(
              //     leading: Icon(Icons.reply, color: AppColors.appBlackColor),
              //     title: const Text('Reply', style: TextStyle(fontSize: 16)),
              //     onTap: () {
              //       Navigator.pop(context);
              //       _handleMenuAction('reply', message, context);
              //     },
              //   ),
              // ListTile(
              //   leading: Icon(Icons.share, color: AppColors.appBlackColor),
              //   title: const Text('Share', style: TextStyle(fontSize: 16)),
              //   onTap: () {
              //     Navigator.pop(context);
              //     _handleMenuAction('share', message, context);
              //   },
              // ),
            ],
          ),
        );
      },
    );
  }

  void _handleMenuAction(String action, Message message, BuildContext context) {
    switch (action) {
      case 'copy':
        if (message.content != null && message.content!.isNotEmpty) {
          // Clipboard.setData(ClipboardData(text: message.content ?? ''));
          Clipboard.setData(ClipboardData(text: markdownToPlainText(message.content ?? '')));
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(context.l10n.copied),
              duration: const Duration(seconds: 2),
            ),
          );
        }
        break;
      // case 'reply':
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(
      //       content: Text('Reply feature coming soon!'),
      //       duration: Duration(seconds: 2),
      //     ),
      //   );
      //   break;
      //   case 'share':
      //     ScaffoldMessenger.of(context).showSnackBar(
      //       const SnackBar(
      //         content: Text('Share feature coming soon!'),
      //         duration: Duration(seconds: 2),
      //       ),
      //     );
      //     break;
    }
  }

  String markdownToPlainText(String markdown) {
    String decodeHtmlEntities(String text) {
      return text.replaceAll('&quot;', '"').replaceAll('&amp;', '&').replaceAll('&lt;', '<').replaceAll('&gt;', '>').replaceAll('&apos;', "'").replaceAll('&nbsp;', ' ').replaceAll('&#39;', "'").replaceAll('&#34;', '"');
    }

    String removeMarkdownFormatting(String text) {
      return text.replaceAll(RegExp(r'^#{1,6}\s*'), '').replaceAll('**', '').replaceAll('*', '').replaceAll('```', '').replaceAll('`', '').replaceAll('~~', '').replaceAll('> ', '').replaceAll('---', '').replaceAll('***', '').replaceAll('#', '').replaceAll('##', '').replaceAll('###', '').replaceAll('####', '').replaceAll('* ', '').replaceAll('+ ', '').replaceAll('__', '').replaceAll('_', '');
    }

    String cleanedText = decodeHtmlEntities(markdown);
    cleanedText = removeMarkdownFormatting(cleanedText);

    cleanedText = cleanedText.replaceAll('\n\n\n', '\n\n').trim();

    return cleanedText;
  }

  Future<void> _sendMessage() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    final businessId = context.read<AuthBloc>().state.bid;
    _textController.clear();
    final tempMessage = Message(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      content: text,
      senderType: 'user',
      createdAt: null,
    );

    context.read<ChatMessagesCubit>().addTempMessage(tempMessage, MessageSendStatus.sending);

    // Scroll to bottom
    _scrollToBottomInstant();
    _justSentMessage = true;

    try {
      // Gửi message lên server
      await context.read<ChatMessagesCubit>().sendMessage(
            text,
            businessId,
            lang: widget.lang,
            tempMessage: tempMessage,
          );
    } catch (e) {
      // Update status thành failed nếu gửi thất bại
      if (context.mounted) {
        context.read<ChatMessagesCubit>().updateMessageStatus(
              tempMessage,
              MessageSendStatus.failed,
              errorMessage: e.toString(),
            );
      }
    }
  }

  @override
  void dispose() {
    _loadMoreDebounce?.cancel();
    _scrollController.dispose();
    _textController
      ..removeListener(_updateInputState)
      ..dispose();
    _isInputEmpty.dispose();
    _smoothAIMessageKeys.clear();
    _smoothAIRunningMessages.clear();
    try {
      if (mounted) {
        context.read<ChatSupportCountBadgeCubit>().onChangedBadge(clear: true);
      }
    } catch (e) {}
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (value, f) async {
        try {
          context.read<ChatSupportCountBadgeCubit>().onChangedBadge(clear: true);
        } catch (e) {}
      },
      child: GestureDetector(
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          appBar: _buildAppbar(l10n.stickyAISupport),
          body: SafeArea(
            bottom: false,
            child: BlocConsumer<ChatMessagesCubit, ChatMessagesState>(
              listener: (context, state) {
                final currentMessageCount = state.messages.length;

                // First load - always scroll to bottom (position 0 with reverse: true)
                if (_isFirstLoad && state.status == ChatMessagesStatus.success && currentMessageCount > 0) {
                  _isFirstLoad = false;
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToBottomInstant();
                  });
                  _previousMessageCount = currentMessageCount;
                  return;
                }

                // Load more handling - với reverse: true, không cần complex logic
                if (state.status == ChatMessagesStatus.loadingMore && !_isLoadingMore) {
                  _isLoadingMore = true;
                } else if (_isLoadingMore && state.status != ChatMessagesStatus.loadingMore) {
                  _isLoadingMore = false;
                  _previousMessageCount = currentMessageCount;
                  return;
                }

                // Smart auto scroll cho messages mới
                final isNewMessage = currentMessageCount > _previousMessageCount && !_isLoadingMore && state.status != ChatMessagesStatus.loadingMore && !_isFirstLoad;

                bool shouldAutoScroll = false;

                if (_justSentMessage) {
                  // Luôn scroll khi user vừa gửi tin nhắn
                  shouldAutoScroll = true;
                } else if (isNewMessage && _scrollController.hasClients) {
                  // Với reverse: true, check nếu user đang ở gần bottom (position gần 0)
                  final currentPosition = _scrollController.position.pixels;

                  if (currentPosition <= 100) {
                    shouldAutoScroll = true;
                  }
                }

                if (shouldAutoScroll) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToBottom();
                  });

                  if (_justSentMessage) {
                    _justSentMessage = false;
                  }
                }

                _previousMessageCount = currentMessageCount;
              },
              builder: (context, state) {
                return Column(
                  children: [
                    Expanded(
                      child: _buildMessagesList(l10n, state),
                    ),
                    _buildInputField(state, l10n),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessagesList(AppLocalizations l10n, ChatMessagesState state) {
    if (state.status == ChatMessagesStatus.loading && state.messages.isEmpty) {
      return const ChatBoxLoadingWidget();
    }

    if (state.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              l10n.noMessagesYet,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.appBlackColor,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.all(16),
      reverse: true, // Reverse để messages mới nhất ở bottom
      itemCount: state.messages.length + (state.status == ChatMessagesStatus.loadingMore && state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (state.status == ChatMessagesStatus.loadingMore && state.hasMore && index == state.messages.length) {
          return Center(
            // child: Container(margin: EdgeInsets.only(top: 24, bottom: 24), child: PulseLoadingDots()),
            child: Container(
              width: 32,
              height: 32,
              padding: const EdgeInsets.all(2.0),
              margin: const EdgeInsets.only(top: 24, bottom: 16),
              child: const CircularProgressIndicator(
                color: AppColors.appColor,
                strokeWidth: 2,
              ),
            ),
          );
        }
        final messageIndex = state.messages.length - 1 - index;
        final message = state.messages[messageIndex];
        final senderType = message.senderType ?? '';
        final messageStatus = state.messageStatuses[message.content];
        final isUser = senderType == 'user';

        final messageId = message.id ?? '';
        final isSmoothAIEnabled = !isUser && (message.isSmoothAI == true);

        // Xử lý system messages
        if (senderType == 'system') {
          return SystemMessageWidget(
            message: message,
            timeZone: context.read<AuthBloc>().state.businessTimeZone?.toString() ?? '',
          );
        }
        if (isSmoothAIEnabled && !_smoothAIMessageKeys.containsKey(messageId)) {
          _smoothAIMessageKeys[messageId] = GlobalKey();
        }
        return GestureDetector(
          onLongPressStart: (LongPressStartDetails details) {
            _showMessageContextMenu(context, message, isUser, details.globalPosition);
          },
          child: MessageBubbleWidget(
            key: _smoothAIMessageKeys[messageId] ?? ValueKey('${message.id}_${message.content.hashCode}'),
            enableSmoothAI: !isUser && (message.isSmoothAI == true),
            typingSpeed: const Duration(milliseconds: 20),
            autoStart: true,
            message: message,
            onStart: () {
              if (message.isSmoothAI == true) {
                _smoothAIRunningMessages.add(messageId);
              }
            },
            onComplete: () {
              if (_smoothAIRunningMessages.contains(message.id ?? '')) {
                _smoothAIRunningMessages.remove(message.id ?? '');
              }
              message.isSmoothAI = false;
              _smoothAIMessageKeys.remove(messageId);
            },
            smoothAITextStyle: TextStyle(
              color: isUser ? AppColors.lightPrimaryBackgroundColor : AppColors.appBlackColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            isUser: isUser,
            timeZone: context.read<AuthBloc>().state.businessTimeZone?.toString() ?? '',
            sendStatus: isUser ? messageStatus?.status : null,
            errorMessage: isUser ? messageStatus?.errorMessage : null,
            onResend: isUser && messageStatus?.status == MessageSendStatus.failed
                ? () {
                    final businessId = context.read<AuthBloc>().state.bid;
                    context.read<ChatMessagesCubit>().sendMessage(
                          message.content ?? '',
                          businessId,
                          lang: widget.lang,
                          tempMessage: message,
                        );
                  }
                : null,
          ),
        );
      },
    );
  }

  Widget _buildInputField(ChatMessagesState state, AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 18),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey, width: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: AnimatedGradientBorderTextFormField(
              controller: _textController,
              onFieldSubmitted: (_) => _sendMessage(),
              hintText: l10n.enterTextstickyAISupport,
            ),
          ),
          const SizedBox(width: 12),
          ValueListenableBuilder<bool>(
            valueListenable: _isInputEmpty,
            builder: (context, isEmpty, child) {
              return InkWell(
                radius: 24,
                focusColor: AppColors.appTransparentColor,
                hoverColor: AppColors.appTransparentColor,
                highlightColor: AppColors.appTransparentColor,
                splashColor: AppColors.appTransparentColor,
                onTap: (isEmpty || state.isSending) ? null : _sendMessage,
                child: Opacity(
                  opacity: (isEmpty || state.isSending) ? 0.7 : 1,
                  child: Container(
                    decoration: BoxDecoration(
                      color: (isEmpty || state.isSending) ? AppColors.appBGAvatarColor : AppColors.appBlackColor,
                      borderRadius: BorderRadius.circular(99),
                    ),
                    padding: const EdgeInsets.all(12),
                    child: state.isSending
                        ? const Icon(
                            Icons.hourglass_empty,
                            size: 20,
                            color: AppColors.appBlackColor,
                          )
                        : SvgPicture.asset(
                            'assets/svgs/arrow-top.svg',
                            colorFilter: ColorFilter.mode(
                              (isEmpty || state.isSending) ? AppColors.appBlackColor : AppColors.lightPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                            clipBehavior: Clip.none,
                          ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  AppBar _buildAppbar(String title) {
    return AppBar(
      systemOverlayStyle: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      automaticallyImplyLeading: false,
      centerTitle: true,
      leadingWidth: 48,
      leading: IconButton(
        splashRadius: 16,
        icon: SvgPicture.asset(
          'assets/svgs/arrow-back.svg',
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/svgs/chat-support.svg',
          ),
          AnimatedGradientText(
            text: title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class SystemMessageWidget extends StatelessWidget {
  final Message message;
  final String timeZone;

  const SystemMessageWidget({
    required this.message,
    required this.timeZone,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Center(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 6, top: 6),
        child: Text(
          _getSystemMessageText(l10n, message.agent?.displayName ?? ''),
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.appBlackColor,
            fontWeight: FontWeight.w400,
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }

  String _getSystemMessageText(AppLocalizations l10n, String agentName) {
    final content = message.content ?? '';

    switch (content) {
      case 'LIVECHAT.AGENT.JOIN':
        return '$agentName ${l10n.joinedTheConversation}';
      case 'LIVECHAT.AGENT.LEAVE':
        return '$agentName ${l10n.leftTheConversation}';
      default:
        return content;
    }
  }
}
