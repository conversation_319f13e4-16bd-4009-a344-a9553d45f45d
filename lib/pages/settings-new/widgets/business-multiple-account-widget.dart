// ignore_for_file: avoid_dynamic_calls

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/models.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';

class BusinessMultibleAccountWidget extends StatelessWidget {
  final String? icon;
  final String? titleValue;
  final String? subTitle;
  final Widget? subTitleWidget;
  final Function? onTap;
  final bool isBorder;
  final Color? textColor;
  final Color? textColorSubTitle;
  final Color? borderColor;
  final Color? iconColor;
  final Color? suffixIconColor;
  final String? suffixIcon;
  final FontWeight? fontWeight;
  final FontWeight? fontWeightSubTitle;
  final double? fontSize;
  final double? fontSizeSubTitle;
  final String? linkAvatar;
  final double width;
  final double height;
  final Business? business;
  final List<Business>? listBusiness;
  final ValueChanged<Business>? onChanged;

  const BusinessMultibleAccountWidget({
    super.key,
    this.onChanged,
    this.business,
    this.listBusiness,
    this.width = 40,
    this.height = 40,
    this.icon,
    this.titleValue,
    this.subTitle,
    this.subTitleWidget,
    this.textColorSubTitle,
    this.onTap,
    this.isBorder = true,
    this.textColor,
    this.borderColor = AppColors.homeBorderColor,
    this.iconColor,
    this.suffixIconColor = AppColors.darkPrimaryBackgroundColor,
    this.suffixIcon,
    this.fontWeight = FontWeight.w600,
    this.fontWeightSubTitle,
    this.fontSize,
    this.fontSizeSubTitle,
    this.linkAvatar,
  });

  @override
  Widget build(BuildContext context) {
    // final isTablet = MediaQuery.of(context).size.width > 600;
    return InkWell(
      hoverColor: AppColors.appTransparentColor,
      splashColor: AppColors.appTransparentColor,
      highlightColor: AppColors.appTransparentColor,
      focusColor: AppColors.appTransparentColor,
      onTap: null,
      // onTap: () => onTap?.call(),
      child: Theme(
        data: Theme.of(context).copyWith(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
        ),
        child: PopupMenuButton(
          enabled: (listBusiness ?? []).isNotEmpty && (listBusiness ?? []).length > 1,
          constraints: const BoxConstraints(
            minWidth: 300,
            maxWidth: 360,
            // maxWidth: isTablet ? 450 : 350,
          ),
          offset: Offset(0, AppBar().preferredSize.height),
          onSelected: (valueChange) {
            if (valueChange.id != business?.id) {
              onChanged?.call(valueChange);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              border: isBorder
                  ? Border(
                      bottom: BorderSide(
                        width: 1,
                        color: borderColor ?? AppColors.appBlackColor,
                      ),
                    )
                  : null,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        child: AvatarControlWidget(
                          boxFit: BoxFit.cover,
                          width: width,
                          height: height,
                          name: titleValue ?? '',
                          urlImage: linkAvatar ?? '',
                          borderRadius: 4,
                          textColor: AppColors.lightPrimaryBackgroundColor,
                          backgroundColor: (linkAvatar == '' || linkAvatar == null) ? AppColors.appColor : AppColors.appTransparentColor,
                        ),
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      titleValue ?? '',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: fontSize ?? 16,
                                        fontWeight: fontWeight ?? FontWeight.w400,
                                        color: textColor ?? AppColors.appBlackColor,
                                        // fontStyle: FontStyle.normal,
                                      ),
                                    ),
                                    if (subTitleWidget != null) ...{
                                      Container(
                                        child: subTitleWidget,
                                      ),
                                    },
                                    if (subTitle != null)
                                      Text(
                                        subTitle ?? '',
                                        style: TextStyle(
                                          fontSize: fontSizeSubTitle ?? 14,
                                          fontWeight: fontWeightSubTitle ?? FontWeight.w400,
                                          color: textColorSubTitle ?? AppColors.appBlackColor,
                                          fontStyle: FontStyle.normal,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              if ((listBusiness ?? []).isNotEmpty && (listBusiness ?? []).length > 1)
                                Padding(
                                  padding: const EdgeInsets.only(left: 4, right: 8),
                                  child: SvgPicture.asset(
                                    'assets/svgs/arrow-down-icon.svg',
                                    colorFilter: const ColorFilter.mode(
                                      AppColors.appBlackColor,
                                      BlendMode.srcIn,
                                    ),
                                    // width: 16,
                                    // height: 16,
                                  ),
                                )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                if (icon != '' && icon != null)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    child: SvgPicture.asset(
                      icon ?? '',
                      colorFilter: ColorFilter.mode(
                        suffixIconColor ?? AppColors.appBlackColor,
                        BlendMode.srcIn,
                      ),
                      width: 24,
                      height: 24,
                    ),
                  ),
              ],
            ),
          ),
          itemBuilder: (_) => (listBusiness ?? []).map((Business value) {
            final isEnable = value.id == business?.id;
            final logoBusiness = value.logo ?? '';
            final nameBusiness = value.name ?? '';
            final addressBusiness = formatAddressFromAddress(value.address);
            final isBorder = value.id != listBusiness?.last.id;
            return PopupMenuItem(
              value: value,
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: isBorder ? AppColors.appBorderColor : AppColors.appTransparentColor, width: 1),
                  ),
                ),
                padding: const EdgeInsets.only(top: 8, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: AvatarControlWidget(
                        boxFit: BoxFit.cover,
                        width: 48,
                        height: 48,
                        name: nameBusiness,
                        urlImage: logoBusiness,
                        borderRadius: 100,
                        textColor: AppColors.lightPrimaryBackgroundColor,
                        backgroundColor: (logoBusiness == '' || logoBusiness == null) ? AppColors.appColor : AppColors.appTransparentColor,
                      ),
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            value.name ?? '',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 16,
                              height: 1.4,
                              fontWeight: FontWeight.w600,
                              color: AppColors.appBlackColor,
                            ),
                          ),
                          const SizedBox(height: 2),
                          if (addressBusiness.isNotEmpty)
                            Text(
                              addressBusiness,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.appBlackColor.withValues(alpha: .6),
                              ),
                            ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      child: SvgPicture.asset(
                        'assets/svgs/check-calendar.svg',
                        colorFilter: ColorFilter.mode(
                          isEnable ? AppColors.appBlackColor : AppColors.appTransparentColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  String formatAddressFromAddress(AddressUser? address) {
    if (address == null) return '';

    final List<String> addressParts = [];

    // Kiểm tra và thêm street
    final street = address.street?.toString().trim();
    if (street != null && street.isNotEmpty) {
      addressParts.add(street);
    }

    // Kiểm tra và thêm city
    final city = address.city?.toString().trim();
    if (city != null && city.isNotEmpty) {
      addressParts.add(city);
    }

    // Kiểm tra và thêm state
    final state = address.state?.toString().trim();
    if (state != null && state.isNotEmpty) {
      addressParts.add(state);
    }

    // Kiểm tra và thêm country
    final country = address.country?.toString().trim();
    if (country != null && country.isNotEmpty) {
      addressParts.add(country);
    }

    return addressParts.join(', ');
  }
}
