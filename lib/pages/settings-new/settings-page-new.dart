// ignore_for_file: avoid_positional_boolean_parameters, prefer_for_elements_to_map_fromiterable, library_private_types_in_public_api

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/business-check-pro-plan/business-check-pro-plan.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-features/business-features.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile-edit.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/change-plan-features-info-content/change-plan-features-info-content.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-customer-receipt/customize-customer-receipt.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-kitchen-ticket/customize-kitchen-ticket.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-account/multiple-account.cubit.dart';
import 'package:stickyqrbusiness/@core/store/onboarding/onboarding.cubit.dart';
import 'package:stickyqrbusiness/@core/store/print-reward/print.cubit.dart';
import 'package:stickyqrbusiness/@core/store/role-permission/role-permission.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tutorial-target/tutorial-target.cubit.dart';
import 'package:stickyqrbusiness/@utils/features.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/setting-page-tartget.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/chatbot-ai/chat-bubble.dart';
import 'package:stickyqrbusiness/pages/settings-new/widgets/@setting-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/@setting-widget.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsPageNew extends StatelessWidget {
  const SettingsPageNew({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => FeaturesProvider(),
      child: const SettingsPageContent(),
    );
  }
}

class SettingsPageContent extends StatefulWidget {
  const SettingsPageContent({super.key});

  @override
  State<SettingsPageContent> createState() => _SettingsPageContentState();
}

class _SettingsPageContentState extends State<SettingsPageContent> with WidgetsBindingObserver {
  int takeList = 5;
  int takeGeneralGroup = 5;
  int takeVoucherGroup = 5;
  int takePointsGroup = 5;
  int takeCallButtonGroup = 5;
  int takeAdditionalGroup = 5;
  int takeOrdersGroup = 5;
  GlobalKey profileButtonKey = GlobalKey();
  GlobalKey referralButtonKey = GlobalKey();
  GlobalKey printLabelButtonKey = GlobalKey();
  GlobalKey generalSettingKey = GlobalKey();

  late final Map<String, Widget Function(AppLocalizations, AuthState, TutorialTarget, List<SettingItem>, bool isHidePro)> groupWidgets = {
    'General': _buildGeneralGroup,
    'StickyVouchers': _buildVouchersGroup,
    'StickyPoints': _buildPointsGroup,
    'TargetOffers': _buildTargetOffersGroup,
    'Orders': _buildOrdersGroup,
    'CallButton': _buildCallButtonGroup,
    'AdditionalFeatures': _buildAdditionalFeaturesGroup,
  };
  List<SettingItem> allFeatures = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FeaturesProvider>().initializeFeatures(context);
      _buildCompleted();
    });

    // context.read<BusinessFeaturesCubit>().getBusinessFeatures();
    context.read<BusinessProfileCubit>().getBusinessProfile().then((value) {
      context.read<BusinessFeaturesCubit>().getBusinessFeatures();

      if (value?.timeZone?.toUpperCase() == 'Asia/SaiGon'.toUpperCase()) {
        context.read<BusinessProfileEditCubit>().onUpdateTimeZone('Asia/Ho_Chi_Minh');
      }
    });
    if (!context.read<AuthBloc>().state.businessOwner) {
      context.read<RolePermissionCubit>().getRolePermission();
    }
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      backgroundColor: AppColors.anouncementBGColor,
      appBar: _buildAppbar(l10n.settings),
      body: SafeArea(
        bottom: false,
        child: _buildContent(l10n),
      ),
    );
  }

  Widget _buildContent(AppLocalizations l10n) {
    // final String language = Localizations.localeOf(context).languageCode;
    return Consumer<FeaturesProvider>(
      builder: (context, featuresProvider, child) {
        final currentLocale = Localizations.localeOf(context);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (featuresProvider.isInitialized && featuresProvider.lastUpdatedLocale != currentLocale) {
            featuresProvider
              ..updateFeatures(context)
              ..lastUpdatedLocale = currentLocale;
            // setState(() {}); // Trigger a rebuild of this widget
          }
        });

        if (!featuresProvider.isInitialized) {
          return const Center(child: SizedBox.shrink());
        }
        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(
            parent: ClampingScrollPhysics(),
          ),
          child: BlocBuilder<TutorialTargetCubit, TutorialTargetState>(
            builder: (context, state) {
              final tutorialTarget = state.target ?? TutorialTarget.NoTarget;

              return BlocBuilder<AuthBloc, AuthState>(
                builder: (context, stateAuth) {
                  final Map<String, int> moduleGroup = stateAuth.business?.modulesStatus?.modulesReorder?.toMap() ?? {};
                  final List<MapEntry<String, int>> sortedGroups = moduleGroup.entries.toList()..sort((a, b) => a.value.compareTo(b.value));
                  // AppLog.e('featuresProvider == ${featuresProvider.allFeatures}');
                  final isNewHomePage = context.read<AuthBloc>().state.isNewHomePage ?? false;
                  final List<SettingItem> allFeatures = featuresProvider.allFeatures;
                  return BlocBuilder<BusinessCheckProPlanCubit, BusinessCheckProPlanState>(
                    builder: (context, stateCheckProPlan) {
                      return Column(
                        children: [
                          if (context.read<AuthBloc>().state.businessOwner)
                            InkWell(
                              onTap: () {
                                AppBased.showDialogYesNo(
                                  context,
                                  title: null,
                                  isClose: true,
                                  noText: l10n.no,
                                  yesText: l10n.yes,
                                  msgContent: isNewHomePage ? l10n.switchToFlexibleHomeMsg : l10n.switchToFocusedHomeMsg,
                                  noTap: () {},
                                  yesTap: () {
                                    final body = {
                                      'isNewHomePage': isNewHomePage ? false : true,
                                    };
                                    context.read<BusinessProfileEditCubit>().onUpdateBusinessScreenNewHome(body).then((data) {
                                      if (data != null) {
                                        if (Navigator.canPop(context)) {
                                          Future.delayed(const Duration(microseconds: 600), () {
                                            Navigator.pop(context);
                                          });
                                        }
                                      }
                                    });
                                  },
                                );
                              },
                              child: Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(16),
                                color: AppColors.swichToFxibleHomeColor.withValues(alpha: .1),
                                child: Text(
                                  isNewHomePage ? l10n.switchToFlexibleHome : l10n.switchToFocusedHome,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.swichToFxibleHomeColor,
                                  ),
                                ),
                              ),
                            ),
                          if (stateAuth.businessOwner) ...{
                            const ChangePlanWidget(),
                          },
                          Padding(
                            padding: EdgeInsets.only(
                              top: stateAuth.businessOwner ? 0 : 16,
                            ),
                            child: Column(
                              children: sortedGroups.map((entry) {
                                final groupKey = entry.key;
                                final widgetBuilder = groupWidgets[groupKey];
                                if (widgetBuilder != null) {
                                  return widgetBuilder(
                                    l10n,
                                    stateAuth,
                                    tutorialTarget,
                                    allFeatures,
                                    stateCheckProPlan.hideProPlan ?? false,
                                  );
                                }
                                return const SizedBox.shrink();
                              }).toList(),
                            ),
                          ),
                          const SizedBox(height: 36),
                        ],
                      );
                    },
                  );
                },
              );
            },
          ),
        );
      },
    );
  }

  AppBar _buildAppbar(String title) {
    return AppBar(
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leadingWidth: 0,
      automaticallyImplyLeading: false,
      centerTitle: false,
      actionsPadding: EdgeInsets.zero,
      titleSpacing: 0,
      title: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, stateAuth) {
          final nameBusiness = stateAuth.nameBusiness ?? 'N/A';
          final logoBusiness = stateAuth.logoBusinessID != null ? '${AppBased.appEnv.cdnUrl}${stateAuth.logoBusinessID}' : stateAuth.logoBusiness;
          return BlocBuilder<TutorialTargetCubit, TutorialTargetState>(
            builder: (context, state) {
              return Row(
                children: [
                  Container(
                    width: 36,
                    margin: const EdgeInsets.only(right: 4, left: 12),
                    child: MaterialButton(
                      elevation: 0,
                      highlightElevation: 0,
                      hoverElevation: 0,
                      hoverColor: AppColors.lightPrimaryBackgroundColor,
                      onPressed: () {
                        context.read<OnboardingCubit>().onGetOnboarding(isExpandedFAB: true);
                        SystemChrome.setPreferredOrientations([
                          DeviceOrientation.portraitUp,
                          DeviceOrientation.portraitDown,
                          DeviceOrientation.landscapeLeft,
                          DeviceOrientation.landscapeRight,
                        ]);
                        Navigator.of(context).pop();
                      },
                      color: AppColors.lightPrimaryBackgroundColor,
                      padding: EdgeInsets.zero,
                      shape: const CircleBorder(),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-back.svg',
                      ),
                    ),
                  ),
                  Expanded(
                    child: BlocConsumer<MultipleAccountCubit, MultipleAccountState>(
                      listener: (context, state) {
                        if (state.status == MultipleAccountStatus.Error && state.errorMsg != null && state.errorMsg != '') {
                          AppBased.toastError(context, title: state.errorMsg);
                        } else if (state.status == MultipleAccountStatus.Success) {}
                      },
                      builder: (context, stateMultipleAccount) {
                        final businesses = stateMultipleAccount.data;
                        return BusinessMultibleAccountWidget(
                          business: stateAuth.business,
                          listBusiness: businesses,
                          isBorder: false,
                          onTap: state.target != TutorialTarget.NoTarget ? () {} : () => AppBased.go(context, AppRoutes.profileBusiness),
                          icon: '',
                          linkAvatar: logoBusiness,
                          titleValue: nameBusiness,
                          fontWeight: FontWeight.bold,
                          fontWeightSubTitle: FontWeight.w600,
                          fontSize: 18,
                          onChanged: (business) {
                            AppLog.e('business: ${business.toJson()}');
                            context.read<MultipleAccountCubit>().doSwitchAccount(business.id ?? '').then((auth) {
                              BubbleOverlay.resetGlobalBubbleState();
                              RestartWidget.restartApp(context);
                            });
                          },
                        );
                      },
                    ),
                  ),
                  // Expanded(
                  //   child: AccountProfileItemWidget(
                  //     isBorder: false,
                  //     onTap: state.target != TutorialTarget.NoTarget ? () {} : () => AppBased.go(context, AppRoutes.profileBusiness),
                  //     icon: '',
                  //     linkAvatar: logoBusiness,
                  //     titleValue: nameBusiness,
                  //     fontWeight: FontWeight.bold,
                  //     fontWeightSubTitle: FontWeight.w600,
                  //     fontSize: 18,
                  //   ),
                  // ),
                ],
              );
            },
          );
        },
      ),
      actions: [
        BlocBuilder<AuthBloc, AuthState>(
          builder: (context, authState) {
            return Padding(
              padding: const EdgeInsets.only(right: 4.0),
              child: BlocBuilder<TutorialTargetCubit, TutorialTargetState>(
                builder: (context, state) {
                  return IconButton(
                    splashRadius: 16,
                    icon: SvgPicture.asset(
                      'assets/svgs/setting.svg',
                      colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
                    ),
                    onPressed: () => AppBased.go(context, AppRoutes.settingGeneral),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  void _onUpdateTemplateReceipt() {
    final l10n = context.l10n;
    final authState = context.read<AuthBloc>().state;
    final logoId = authState.business?.logo ?? '';
    context.read<CustomizeCustomerReceiptCubit>().getReceiptTemplates(l10n: l10n, logoId: logoId);
    context.read<CustomizeKitchenTicketCubit>().getReceiptTemplates();
  }

  Widget _blockSetting({
    required String title,
    required Widget children,
    String? subTitle,
    EdgeInsetsGeometry? childPadding,
    bool isSetting = false,
    Function? onTapSetting,
    EdgeInsets? padding,
  }) {
    return Padding(
      padding: padding ?? const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != '')
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Row(
                children: [
                  Text(
                    title.toUpperCase(),
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (isSetting)
                    _buildButtonSetting(
                      subTitle ?? '',
                      'assets/svgs/setting-menu.svg',
                      onTap: () => onTapSetting?.call(),
                    ),
                ],
              ),
            ),
          Container(
            decoration: BoxDecoration(
              color: AppColors.lightPrimaryBackgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            padding: childPadding ?? const EdgeInsets.symmetric(horizontal: 16),
            child: children,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonSetting(String title, String icon, {Function? onTap}) {
    return InkWell(
      radius: 8,
      highlightColor: AppColors.appTransparentColor,
      splashColor: AppColors.appTransparentColor,
      onTap: () => onTap?.call(),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 4.0),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.appResendColor,
              ),
            ),
          ),
          SizedBox(
            width: 20,
            height: 20,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.appTransparentColor,
              onPressed: null,
              color: AppColors.appTransparentColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                icon,
                colorFilter: const ColorFilter.mode(AppColors.iconColor, BlendMode.srcIn),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = state.business?.modulesStatus?.modules?.general ?? false;
    final features = state.features ?? [];
    // AppLog.e('features: $features');
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "General"
    final generalFeatures = features.where((f) => f.module == 'General' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    if (displayedItems.isEmpty) return const SizedBox.shrink();
    final isShowMore = (displayedItems.length > takeGeneralGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takeGeneralGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;
    return _blockSetting(
      title: l10n.general,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;
              final isPlanProEnterprise = state.isPro;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';
              return SettingsItemWidget(
                key: item.key == 'GENERAL_SETTINGS' ? generalSettingKey : null,
                title: item.title,
                icon: item.icon,
                onTap: isDisabledWidget
                    ? null
                    : feature?.isEnabled == true || isPlanProEnterprise
                        ? () => item.onTap.call()
                        : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takeGeneralGroup = displayedItems.length;
                  } else {
                    takeGeneralGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildShowMoreLess(
    AppLocalizations l10n,
    bool isShowMore,
    List<SettingItem> displayedItems, {
    Function? onTap,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        InkWell(
          onTap: () {
            onTap?.call();
          },
          child: isShowMore
              ? _buildShowMoreLessItem(
                  l10n.showMore,
                  'assets/svgs/arrow-down-icon.svg',
                )
              : _buildShowMoreLessItem(
                  l10n.showLess,
                  'assets/svgs/arrow-up-icon.svg',
                ),
        ),
      ],
    );
  }

  Widget _buildShowMoreLessItem(String title, String icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              // fontStyle: FontStyle.italic,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 4.0),
            child: SvgPicture.asset(
              icon,
              colorFilter: const ColorFilter.mode(AppColors.iconColor, BlendMode.srcIn),
              width: 20,
              height: 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVouchersGroup(AppLocalizations l10n, AuthState state, TutorialTarget tutorialTarget, List<SettingItem> allFeatures, bool isHidePro) {
    final isShow = state.business?.modulesStatus?.modules?.stickyVouchers ?? false;
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "StickyVouchers"
    if (features.isEmpty) return const SizedBox.shrink();
    final generalFeatures = features.where((f) => f.module == 'StickyVouchers' && (isHidePro ? f.isProRequired != true : true)).toList();
    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);
    if (displayedItems.isEmpty) return const SizedBox.shrink();
    final isShowMore = (displayedItems.length > takeVoucherGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takeVoucherGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;
    return _blockSetting(
      title: l10n.offers,
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;
              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';

              return SettingsItemWidget(
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : isDisabledWidget
                        ? null
                        : feature?.isEnabled == true || isPlanProEnterprise
                            ? () => item.onTap.call()
                            : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takeVoucherGroup = displayedItems.length;
                  } else {
                    takeVoucherGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildPointsGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = state.business?.modulesStatus?.modules?.stickyPoints ?? false;
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "StickyPoints"
    if (features.isEmpty) return const SizedBox.shrink();
    final generalFeatures = features.where((f) => f.module == 'StickyPoints' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    if (displayedItems.isEmpty) return const SizedBox.shrink();
    final isShowMore = (displayedItems.length > takePointsGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takePointsGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;
    return _blockSetting(
      title: l10n.stickyPoints,
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;

              final isEnabled = feature?.isEnabled == true;
              final isClickable = isEnabled;
              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';

              return SettingsItemWidget(
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : isDisabledWidget
                        ? null
                        : isClickable || isPlanProEnterprise
                            ? () => item.onTap.call()
                            : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takePointsGroup = displayedItems.length;
                  } else {
                    takePointsGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildTargetOffersGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = (state.business?.modulesStatus?.modules?.targetOffers ?? false) && (state.business?.enableTargetOffers ?? false);
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "TargetOffers"
    if (features.isEmpty) return const SizedBox.shrink();
    final generalFeatures = features.where((f) => f.module == 'TargetOffers' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    if (displayedItems.isEmpty) return const SizedBox.shrink();
    final isShowMore = (displayedItems.length > takePointsGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takePointsGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;
    return _blockSetting(
      title: l10n.targetedOffer,
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;

              final isEnabled = feature?.isEnabled == true;
              final isClickable = isEnabled;
              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';

              return SettingsItemWidget(
                isDisable: item.disable ?? false,
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : isDisabledWidget
                        ? null
                        : isClickable || isPlanProEnterprise
                            ? () => item.onTap.call()
                            : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takePointsGroup = displayedItems.length;
                  } else {
                    takePointsGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildCallButtonGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = state.business?.modulesStatus?.modules?.callButton ?? false;
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "CallButton"
    if (features.isEmpty) return const SizedBox.shrink();
    final generalFeatures = features.where((f) => f.module == 'CallButton' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    if (displayedItems.isEmpty) return const SizedBox.shrink();

    final checkSetting = generalFeatures.firstWhereOrNull((item) => item.key == 'CALL_BUTTON_SETTINGS' && featureMap[item.key]?.isEnabled == true) != null;
    final isShowMore = (displayedItems.length > takeCallButtonGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takeCallButtonGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;

    return _blockSetting(
      title: l10n.callButton,
      isSetting: checkSetting,
      subTitle: checkSetting ? l10n.settings : '',
      onTapSetting: () => AppBased.go(context, AppRoutes.callButtonPage),
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;

              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';

              return SettingsItemWidget(
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : isDisabledWidget
                        ? null
                        : feature?.isEnabled == true || isPlanProEnterprise
                            ? () => item.onTap.call()
                            : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takeCallButtonGroup = displayedItems.length;
                  } else {
                    takeCallButtonGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildAdditionalFeaturesGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = state.business?.modulesStatus?.modules?.additionalFeatures ?? false;
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "AdditionalFeatures"
    if (features.isEmpty) return const SizedBox.shrink();
    final generalFeatures = features.where((f) => f.module == 'AdditionalFeatures' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      generalFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    if (displayedItems.isEmpty) return const SizedBox.shrink();
    final isShowMore = (displayedItems.length > takeAdditionalGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takeAdditionalGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;

    return _blockSetting(
      title: l10n.additionalFeatures,
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;
              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              final isDisabledWidget = feature?.isEnabled != true && feature?.disabledType?.toUpperCase() == 'DISABLED';

              return SettingsItemWidget(
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : isDisabledWidget
                        ? null
                        : feature?.isEnabled == true || isPlanProEnterprise
                            ? () => item.onTap.call()
                            : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takeAdditionalGroup = displayedItems.length;
                  } else {
                    takeAdditionalGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  Widget _buildOrdersGroup(
    AppLocalizations l10n,
    AuthState state,
    TutorialTarget tutorialTarget,
    List<SettingItem> allFeatures,
    bool isHidePro,
  ) {
    final isShow = state.business?.modulesStatus?.modules?.orders ?? false;
    final features = state.features ?? [];
    if (!isShow) return const SizedBox.shrink();

    // Lọc ra các feature thuộc module "Orders"
    if (features.isEmpty) return const SizedBox.shrink();
    final ordersFeatures = features.where((f) => f.module == 'Orders' && (isHidePro ? f.isProRequired != true : true)).toList();

    final featureMap = Map<String, Feature>.fromIterable(
      ordersFeatures,
      key: (f) => (f as Feature).key ?? '',
      value: (f) => f as Feature,
    );

    final displayedItems = onGetItemForGroup(state, featureMap, allFeatures);

    final checkSetting = ordersFeatures.firstWhereOrNull((item) => item.key == 'ORDERS_SETTINGS' && featureMap[item.key]?.isEnabled == true) != null && state.checkPermissions(PermissionBusiness.orders_orders_settings.name, [0, 1, 2, 3, 4]);

    if (displayedItems.isEmpty && !checkSetting) return const SizedBox.shrink();

    final isShowMore = (displayedItems.length > takeOrdersGroup) && displayedItems.length > takeList;
    final displayedItemsShowMore = isShowMore ? displayedItems.take(takeOrdersGroup).toList() : displayedItems;
    final isShowMoreWidget = displayedItems.length > takeList;

    return _blockSetting(
      title: l10n.stickyOrders,
      isSetting: checkSetting,
      subTitle: checkSetting ? l10n.settings : '',
      onTapSetting: () => AppBased.go(context, AppRoutes.settingsOrders),
      children: Column(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: displayedItemsShowMore.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final feature = featureMap[item.key];
              final isLastItem = index == displayedItemsShowMore.length - 1;

              final isPlanProEnterprise = state.isPro;
              final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
              return SettingsItemWidget(
                checkPro: checkPro,
                title: item.title,
                icon: item.icon,
                suffixIcon: item.suffixIcon,
                onTap: checkPro
                    ? () => _diaLogProPlan(context, l10n, item.key)
                    : feature?.isEnabled == true || isPlanProEnterprise
                        ? () => item.onTap.call()
                        : null,
                isBorder: isShowMoreWidget ? true : !isLastItem,
              );
            }).toList(),
          ),
          if (isShowMoreWidget)
            _buildShowMoreLess(
              l10n,
              isShowMore,
              displayedItems,
              onTap: () {
                setState(() {
                  if (isShowMore) {
                    takeOrdersGroup = displayedItems.length;
                  } else {
                    takeOrdersGroup = takeList;
                  }
                });
              },
            )
        ],
      ),
    );
  }

  List<SettingItem> onGetItemForGroup(
    AuthState state,
    Map<String, Feature> featureMap,
    List<SettingItem> allFeatures,
  ) {
    allFeatures.sort((a, b) {
      final featureA = featureMap[a.key];
      final featureB = featureMap[b.key];
      final indexA = featureA?.index;
      final indexB = featureB?.index;

      if (indexA is int && indexB is int) {
        return indexA.compareTo(indexB);
      } else if (indexA is int) {
        return 1;
      } else if (indexB is int) {
        return -1;
      }
      return 0;
    });

    final displayedItems = allFeatures.where((item) {
      final feature = featureMap[item.key];
      if (feature == null) return false;
      if (feature.isEnabled != true && feature.disabledType == 'HIDDEN') return false;
      return item.permissions == null || state.checkMultipleRoles(item.permissions!);
    }).toList();
    return displayedItems;
  }

  void _diaLogProPlan(BuildContext context, AppLocalizations l10n, String key) {
    final bid = context.read<AuthBloc>().state.bid;
    context.read<ChangePlanFeaturesInfoCubit>().doGetPlanFeaturesInfo(bid: bid, key: key);
    AppBased.openPROButtomSheetDraggableScrollable(
      context,
      titleFeature: l10n.labelTemplate,
      widget: UpgradeProContentButtomSheetWidget(type: key),
      widgetButtom: const UpgradeProIndexSliderWidget(),
      onTap: () {
        Navigator.pop(context);
      },
    );
  }

  Future<void> _launchURLInAppBrowser(BuildContext context) async {
    final bid = context.read<AuthBloc>().state.bid;
    final String lang = Localizations.localeOf(context).languageCode;
    final Uri url = Uri.parse('${AppBased.appEnv.getStartedURL}/$lang/pricing?id=$bid');
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch');
    }
  }

  Future<void> onGetOnboarding() async {
    final started = await AppLocalStorage.getStartedGuide() ?? false;
    if (!started) {
      context.read<OnboardingCubit>().onShowHideOnboarding(true);
    } else {
      // context.read<OnboardingCubit>()..onShowHideOnboarding(!started)..onGetOnboarding(isExpandedFAB: true);
    }
  }

  void showTutorial(BuildContext context, GlobalKey keyTarget) {
    final l10n = context.l10n;
    TutorialCoachMark(
      targets: settingPageTarget(
        generalSettingKey: keyTarget == generalSettingKey ? generalSettingKey : null,
        profileButtonKey: keyTarget == profileButtonKey ? profileButtonKey : null,
        referralButtonKey: keyTarget == referralButtonKey ? referralButtonKey : null,
        printLabelButtonKey: keyTarget == printLabelButtonKey ? printLabelButtonKey : null,
      ),
      textSkip: l10n.skip,
      unFocusAnimationDuration: const Duration(milliseconds: 350),
      onClickTarget: (target) {},
      onClickTargetWithTapPosition: (target, tapDetails) {},
      onClickOverlay: (target) {},
      onFinish: () {
        onClickTarget(keyTarget);
        // context.read<TutorialTargetCubit>().onReset();
      },
      onSkip: () {
        context.read<TutorialTargetCubit>().onReset();
        return true;
      },
    ).show(context: context);
  }

  void onClickTarget(GlobalKey keyTarget) {
    if (keyTarget == generalSettingKey) {
      context.read<TutorialTargetCubit>().onChangedTarget(TutorialTarget.Profile);
      AppBased.go(context, AppRoutes.settingGeneral);
    } else if (keyTarget == profileButtonKey) {
      AppBased.go(context, AppRoutes.profileBusiness);
    } else if (keyTarget == referralButtonKey) {
      AppBased.go(context, AppRoutes.referral);
    } else if (keyTarget == printLabelButtonKey) {
      AppBased.go(
        context,
        AppRoutes.labelsTemplate,
        onChanged: (value) {
          Future.delayed(const Duration(milliseconds: 200), () {
            context.read<PrintCubit>().doCheckLabelPrinter().then((value) {
              if (!value) {
                context.read<PrintCubit>().doCheckLabelPrinter();
              }
            });
          });
        },
      );
    }
  }

  void _buildCompleted() {
    final features = context.read<AuthBloc>().state.features ?? [];
    final target = context.read<TutorialTargetCubit>().state.target;
    if (features.isNotEmpty) {
      if (target == null || target == TutorialTarget.NoTarget) {
        context.read<OnboardingCubit>().onShowHideOnboarding(false);
      }
      if (mounted) {
        final tartget = context.read<TutorialTargetCubit>().state.target;
        Future.delayed(const Duration(milliseconds: 500), () {
          if (tartget != null) {
            switch (tartget) {
              case TutorialTarget.GeneralSetting:
              case TutorialTarget.Profile:
                Scrollable.ensureVisible(generalSettingKey.currentContext!);
                showTutorial(context, generalSettingKey);
                break;
              case TutorialTarget.PrintLabel:
                Scrollable.ensureVisible(printLabelButtonKey.currentContext!);
                showTutorial(context, printLabelButtonKey);
                break;
              case TutorialTarget.Referral:
                Scrollable.ensureVisible(referralButtonKey.currentContext!);
                showTutorial(context, referralButtonKey);
                break;
              default:
            }
          }
        });
      }
    }
  }
}

class SettingItem {
  final String key;
  final String title;
  final String icon;
  final String? route;
  final List<String>? permissions;
  final String? suffixIcon;
  final bool? disable;
  final Function onTap;

  SettingItem({
    required this.key,
    required this.title,
    required this.icon,
    this.route,
    this.permissions,
    this.suffixIcon,
    this.disable,
    required this.onTap,
  });
}
