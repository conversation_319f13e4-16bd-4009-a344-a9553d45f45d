import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/tags-customer/tags-customer.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/tags-customer/customer-tag-page.dart';

class OrderActiveCustomerInfoWidget extends StatelessWidget {
  final Search? user;
  const OrderActiveCustomerInfoWidget({super.key, this.user});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final avatar = user?.user?.avatar?.publicId != null ? '${AppBased.appEnv.cdnUrl}${user?.user?.avatar?.publicId}' : user?.user?.avatar?.file;
    final userName = user?.user?.displayName ?? user?.displayName ?? '';
    final phone = user?.phoneNumber ?? user?.user?.phone;
    final isActive = user?.user?.isActive ?? true;
    final segmentType = user?.segmentType ?? '';
    final uid = user?.userId ?? user?.user?.id;
    AppLog.e('segmentType: $segmentType');
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 8, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(top: 24),
                    child: Row(
                      children: [
                        AvatarControlWidget(
                          name: userName,
                          urlImage: avatar,
                          width: 64,
                          height: 64,
                          borderRadius: 50,
                          borderColor: Colors.grey.shade200,
                          backgroundColor: Colors.grey.shade100,
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 16.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Padding(
                                //   padding: const EdgeInsets.only(bottom: 4),
                                //   child: Text(
                                //     userName,
                                //     style: const TextStyle(
                                //       fontSize: 20,
                                //       fontWeight: FontWeight.bold,
                                //     ),
                                //   ),
                                // ),
                                RichText(
                                  textAlign: TextAlign.left,
                                  softWrap: true,
                                  text: TextSpan(
                                    text: isActive ? userName : l10n.guestDelete,
                                    style: const TextStyle(
                                      color: AppColors.appBlackColor,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    children: [
                                      if (isActive && uid != null && uid != '')
                                        WidgetSpan(
                                          child: Container(
                                            margin: const EdgeInsets.only(left: 2),
                                            width: 24,
                                            height: 24,
                                            child: IconButton(
                                              padding: EdgeInsets.zero,
                                              onPressed: () {
                                                final data = {'user': user};
                                                AppBased.go(
                                                  context,
                                                  AppRoutes.redeemPoint,
                                                  args: ScreenArguments(
                                                    data: data,
                                                    type: 'activities-page',
                                                  ),
                                                  onChanged: (data) {},
                                                );
                                              },
                                              icon: SvgPicture.asset(
                                                'assets/svgs/open-link.svg',
                                                colorFilter: const ColorFilter.mode(
                                                  AppColors.appResendColor,
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                SegmentTargetOffersWidget(
                                  title: segmentType,
                                  uid: uid,
                                  titleColor: AppColors.homeShowQRBGColor,
                                  margin: const EdgeInsets.only(top: 1, bottom: 2),
                                ),
                                if (phone != null && phone != '')
                                  Text(
                                    AppValidations.formatPhoneNumber(phone),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.black54,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  width: 48,
                  child: MaterialButton(
                    elevation: 0,
                    highlightElevation: 0,
                    hoverElevation: 0,
                    hoverColor: AppColors.appTransparentColor,
                    onPressed: () => {
                      Navigator.of(context).pop(),
                    },
                    color: AppColors.appTransparentColor,
                    padding: EdgeInsets.zero,
                    shape: const CircleBorder(),
                    child: SvgPicture.asset(
                      'assets/svgs/close.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.darkPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDivider(),
                  _buildTags(l10n),
                  _buildDivider(),
                  _buildBtnAISummary(context, l10n),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1,
      color: AppColors.appBorderColor,
    );
  }

  Widget _buildTags(AppLocalizations l10n) {
    return BlocBuilder<TagsCustomerCubit, TagsCustomerState>(
      builder: (context, state) {
        final tagsSelected = state.tagsSelected ?? [];
        return Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: tagsSelected.isNotEmpty
                      ? Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8.0,
                          runSpacing: 8.0,
                          children: tagsSelected.map((tag) {
                            return Container(
                              padding: const EdgeInsets.only(left: 12, right: 12),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                color: const Color(0xFFF5F5F5),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Flexible(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 5),
                                      child: Text(
                                        tag.name ?? '',
                                        style: const TextStyle(
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        )
                      : Text(
                          l10n.customerAddTags,
                          style: TextStyle(
                            color: Colors.grey.shade300,
                          ),
                        ),
                ),
              ),
              InkWell(
                onTap: () {
                  onShowCustomerTags(context, TagsCustomerPage(customer: user, isPreferredOrientations: false));
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 12, top: 8, bottom: 4),
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        'assets/svgs/plus.svg',
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(
                          AppColors.orderInProgressBGColor,
                          BlendMode.srcIn,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          l10n.addTag,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.orderInProgressBGColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void onShowCustomerTags(BuildContext context, Widget widget) {
    showDialog<dynamic>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.sizeOf(context).height * .6,
                ),
                color: Colors.white,
                child: widget,
              ),
            ),
          ),
        );
      },
    ).then((onValue) {});
  }

  Widget _buildBtnAISummary(BuildContext context, AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: AppColors.iconColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onPressed: () => AppBased.openCustomerSummaryAI(context, user?.userId ?? ''),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              'assets/svgs/ai-summary.svg',
              width: 20,
              height: 20,
              colorFilter: const ColorFilter.mode(
                Colors.black,
                BlendMode.srcIn,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              l10n.aiSummary,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
