import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/delivery-quote/delivery-quote.cubit.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/input-auto-resize-input-widget.dart';
import 'package:stickyqrbusiness/@widgets/phone-flag-widget.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/new-delivery/@new-devlivery-widgets.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/new-delivery/search-delivery-address-widget.dart';

class AddNewDeliveryPage extends StatelessWidget {
  const AddNewDeliveryPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    Order? order;
    if (args != null && args is ScreenArguments) {
      order = args.data as Order;
    } else {}
    final busAddress = context.read<AuthBloc>().state.addressFull;
    final deliveryAddress = Place(
      long: order?.deliveryAddress?.street,
      city: order?.deliveryAddress?.city,
      state: order?.deliveryAddress?.state,
      country: order?.deliveryAddress?.country,
      postalCode: order?.deliveryAddress?.zipCode,
    );
    AppLog.e('deliveryAddress: ${jsonEncode(deliveryAddress)}');
    return BlocProvider(
      create: (context) => NewDeliveryCubit()
        ..onChangedDeliveryType('ASAP')
        ..onChangedDropoffOption('HAND_IT_TO_ME')
        ..onChangedName(
            order?.customerName ?? order?.customer?.displayName ?? '')
        ..onChangedPhoneNumber(
            AppValidations.detectPhone(order?.phone ?? order?.customer?.phone)
                    ?.nsn ??
                '')
        ..onChangedCurrentCode(
            AppValidations.detectPhone(order?.phone ?? order?.customer?.phone)
                    ?.countryCode ??
                '')
        ..onChangedFlag(AppValidations.detectPhoneFlag(
                order?.phone ?? order?.customer?.phone) ??
            '')
        ..onChangedLocationNotes('')
        ..onChangedDeliveryNotes('')
        ..onChangedLocationName(busAddress ?? '')
        ..onChangedDeliveryAddress(deliveryAddress)
        ..onResetStatus(),
      child: NewDeliveryContentWidget(order: order),
    );
  }
}

class NewDeliveryContentWidget extends StatefulWidget {
  final Order? order;
  const NewDeliveryContentWidget({super.key, this.order});

  @override
  State<NewDeliveryContentWidget> createState() => _NewDeliveryContentWidgetState();
}

class _NewDeliveryContentWidgetState extends State<NewDeliveryContentWidget> {
  late final l10n = context.l10n;
  late final TextEditingController deliveryAddressController;

  String phoneNumber = '';
  String countryCode = '1';
  String flag = '🇺🇸';

  @override
  void initState() {
    super.initState();
    deliveryAddressController = TextEditingController();

    // Pre-fill with sample data as shown in the image
    if (mounted) {
      deliveryAddressController.text = '';
    }
    context.read<NewDeliveryCubit>().onResetStatus();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        bottom: false,
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
            child: BlocConsumer<NewDeliveryCubit, NewDeliveryState>(
              listener: (context, state) {
                final timeZone = context.read<AuthBloc>().state.businessTimeZone;
                if (!state.showValidatePhoneNumber &&
                    !state.isValidateDeliveryAddress) {
                  final address = state.place;
                  if (address != null) {
                    final scheduled = state.scheduledAt;
                      final scheduleTime = scheduled != null && scheduled != ''
                          ? DateTime.parse(scheduled)
                          : DateTime.now();
                          final scheduledTimeFormat = DateTimeHelper.convertToUtc(scheduleTime, timeZone ?? 'UTC')?.toIso8601String();
                    final body = {
                      'dropoff': {
                        'street': address.long,
                        'city': address.city,
                        'country': address.country,
                        'zipCode': address.postalCode,
                        'state': address.state,
                        'note': state.locationNotes?.value,
                        'name': state.name?.value,
                        'phone': '+${state.currentCode}${state.busPhone.value}',
                      },
                      'deliveryType': state.deliveryType,
                      'scheduledAt': scheduledTimeFormat,
                    };
                    context.read<DeliveryQuoteCubit>().getQuote(
                        widget.order?.metadata?.orderId ??
                            widget.order?.id ??
                            '',
                        body);
                  }
                }
              },
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPickupDetail(),

                    const SizedBox(height: 24),

                    // Location name
                    _buildInputLabel(l10n.locationName),
                    const SizedBox(height: 8),
                    // _buildInputField(
                    //   controller: pickupLocationController,
                    //   hintText: 'Enter pickup location',
                    //   maxLines: 4,
                    // ),
                    AutoResizeInputWidget(
                      hintText: l10n.locationName,
                      readOnly: true,
                      initialValue: state.locationName?.value ?? '',
                      minLines: 1,
                      maxLines: 3,
                      minHeight: 58,
                      errorColor: AppColors.appErrorColor,
                      isValidate: false,
                      errorText: '',
                      errorIcon: const Icon(
                        Icons.info,
                        size: 14,
                        color: AppColors.appErrorColor,
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      scrollPadding: const EdgeInsets.only(bottom: 40),
                      onChanged: (String value) {
                        context.read<NewDeliveryCubit>().onChangedLocationName(value);
                      },
                    ),

                    const SizedBox(height: 16),

                    AutoResizeInputWidget(
                      hintText: l10n.addPickupLocationNotes,
                      initialValue: state.locationNotes?.value ?? '',
                      minLines: 1,
                      maxLines: 3,
                      minHeight: 58,
                      errorColor: AppColors.appErrorColor,
                      isValidate: false,
                      errorText: '',
                      errorIcon: const Icon(
                        Icons.info,
                        size: 14,
                        color: AppColors.appErrorColor,
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      scrollPadding: const EdgeInsets.only(bottom: 40),
                      onChanged: (String value) {
                        context.read<NewDeliveryCubit>().onChangedLocationNotes(value);
                      },
                    ),
                    const SizedBox(height: 32),

                    // Dropoff details section
                    _buildSectionTitle(l10n.dropoffDetails),
                    const SizedBox(height: 16),

                    // Customer Name
                    _buildInputLabel(l10n.deliveryCustomerName),
                    const SizedBox(height: 8),
                    AutoResizeInputWidget(
                      hintText: l10n.enterName,
                      initialValue: state.name?.value ?? '',
                      minLines: 1,
                      maxLines: 3,
                      minHeight: 58,
                      errorColor: AppColors.appErrorColor,
                      isValidate: state.isValidateName,
                      errorText: state.name?.error == ValidateControlStatus.empty ? l10n.nameRequired : '',
                      errorIcon: const Icon(
                        Icons.info,
                        size: 14,
                        color: AppColors.appErrorColor,
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      scrollPadding: const EdgeInsets.only(bottom: 40),
                      onChanged: (String value) {
                        context.read<NewDeliveryCubit>().onChangedName(value);
                      },
                    ),

                    const SizedBox(height: 16),

                    // Phone number
                    _buildInputLabel(l10n.phoneNumber),
                    const SizedBox(height: 8),
                    PhoneNumberFlagWidget(
                      height: 58,
                      borderRadius: 8,
                      labelFontWeight: FontWeight.w600,
                      focusColor: AppColors.appBlackColor,
                      countryCode: state.currentCode ?? '1',
                      flag: state.flag ?? '🇺🇸',
                      phoneNumber: state.busPhone.value,
                      isValidate: state.showValidatePhoneNumber,
                      errorText:
                          state.busPhone.error == ValidateControlStatus.empty
                              ? l10n.phoneRequired
                              : !state.isPhoneNumber
                                  ? l10n.phoneIncorrect
                                  : '',
                      // onChanged: (value) {
                      //   print(value);
                      // },
                      onChangedPhoneNotCode: (phone) => context.read<NewDeliveryCubit>().onChangedPhoneNumber(phone),
                      onChangedCode: (code) => context.read<NewDeliveryCubit>().onChangedCurrentCode(code),
                      onChangedFlag: (flag) => context.read<NewDeliveryCubit>().onChangedFlag(flag),
                      onFieldSubmitted: (value) {
                        FocusScope.of(context).unfocus();
                      },
                    ),

                    const SizedBox(height: 16),

                    // Delivery address
                    _buildInputLabel(l10n.deliveryAddress),
                    const SizedBox(height: 8),
                    SearchDeliveryAddressWidget(
                      initialValue: state.deliveryAddress.value,
                      hintText: l10n.enterAddress,
                      debounceTime: const Duration(seconds: 2),
                      onAddressSelected: (address) {
                        context
                            .read<NewDeliveryCubit>()
                            .onChangedDeliveryAddress(address ?? Place())
                            .whenComplete(() {
                        });
                      },
                      onClear: () {
                        context.read<NewDeliveryCubit>().onChangedDeliveryAddress(Place());
                      },
                      // isValidate: state.isValidateDeliveryAddress && (state.status == NewDeliveryStatus.Edit || state.status == NewDeliveryStatus.Error),
                      isValidate: state.isValidateDeliveryAddress,
                    ),
                    BlocBuilder<DeliveryQuoteCubit, DeliveryQuoteState>(
                      builder: (context, state) {
                        if (state.status == DeliveryQuoteStatus.Error) {
                          final errMsg = state.errorMsg ?? '';
                          return Container(
                            padding: const EdgeInsets.all(8),
                            margin: const EdgeInsets.only(top: 8),
                            decoration: const BoxDecoration(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(8),
                                ),
                                color: Color(0xFFfef3eb)),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(right: 8.0),
                                  child: Icon(
                                    Icons.warning_rounded,
                                    color: Colors.orange,
                                  ),
                                ),
                                Flexible(
                                  child: Text(
                                    errMsg,
                                    style: const TextStyle(
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    const SizedBox(height: 16),

                    AutoResizeInputWidget(
                      hintText: l10n.addDeliveryNotes,
                      initialValue: state.deliveryNotes?.value ?? '',
                      minLines: 1,
                      maxLines: 3,
                      minHeight: 58,
                      errorColor: AppColors.appErrorColor,
                      isValidate: false,
                      errorText: '',
                      errorIcon: const Icon(
                        Icons.info,
                        size: 14,
                        color: AppColors.appErrorColor,
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      scrollPadding: const EdgeInsets.only(bottom: 40),
                      onChanged: (String value) {
                        context.read<NewDeliveryCubit>().onChangedDeliveryNotes(value);
                      },
                    ),

                    const SizedBox(height: 32),

                    // Drop-off Options section
                    _buildSectionTitle(l10n.dropOffOptions),
                    const SizedBox(height: 16),

                    if (isTablet)
                      Row(
                        children: [
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildDropOffOption(
                              l10n.handDelivery,
                              state.dropoffOption == 'HAND_IT_TO_ME',
                              valueSelected:
                                  state.dropoffOption ?? 'HAND_IT_TO_ME',
                              currentValueSelected: 'HAND_IT_TO_ME',
                              onTap: () {
                                context.read<NewDeliveryCubit>().onChangedDropoffOption('HAND_IT_TO_ME');
                              },
                            ),
                          ),
                          Expanded(
                            child: _buildDropOffOption(
                              l10n.leaveAtDoor,
                              state.dropoffOption == 'DOOR_DELIVERY',
                              valueSelected: state.dropoffOption ?? 'DOOR_DELIVERY',
                              currentValueSelected: 'DOOR_DELIVERY',
                              onTap: () {
                                context.read<NewDeliveryCubit>().onChangedDropoffOption('DOOR_DELIVERY');
                              },
                            ),
                          ),
                        ],
                      )
                    else
                      Column(
                        children: [
                          _buildDropOffOption(
                            l10n.handDelivery,
                            state.dropoffOption == 'HAND_IT_TO_ME',
                            valueSelected: state.dropoffOption ?? 'HAND_IT_TO_ME',
                            currentValueSelected: 'HAND_IT_TO_ME',
                            onTap: () {
                              context.read<NewDeliveryCubit>().onChangedDropoffOption('HAND_IT_TO_ME');
                            },
                          ),
                          _buildDropOffOption(
                            l10n.leaveAtDoor,
                            state.dropoffOption == 'DOOR_DELIVERY',
                            valueSelected: state.dropoffOption ?? 'DOOR_DELIVERY',
                            currentValueSelected: 'DOOR_DELIVERY',
                            onTap: () {
                              context.read<NewDeliveryCubit>().onChangedDropoffOption('DOOR_DELIVERY');
                            },
                          )
                        ],
                      ),

                    const SizedBox(height: 40),
                    if (isTablet) ...[
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SizedBox(
                            width: 200,
                            child: _buildButton(
                              text: l10n.cancel,
                              isPrimary: false,
                              onPressed: () {
                                if (mounted) {
                                  Navigator.of(context).pop();
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          SizedBox(
                            width: 200,
                            child: _buildButton(
                              text: l10n.reviewQuote,
                              isPrimary: true,
                              onPressed: () {
                                showReviewQuote();
                              },
                              isAllowPress: state.isValidateForm &&
                                  !state.showValidatePhoneNumber,
                            ),
                          ),
                        ],
                      ),
                    ] else
                      Row(
                        children: [
                          Expanded(
                            child: _buildButton(
                              text: l10n.cancel,
                              isPrimary: false,
                              onPressed: () {
                                if (mounted) {
                                  Navigator.of(context).pop();
                                }
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildButton(
                              text: l10n.reviewQuote,
                              isPrimary: true,
                              onPressed: () {
                                showReviewQuote();
                                print(
                                    'order id = ${widget.order?.metadata?.orderId}');
                                // print('phone = ${'+${state.currentCode}${state.busPhone.value}'} // state.isValidateForm && state.showValidatePhoneNumber = ${state.isValidateForm} // ${state.showValidatePhoneNumber}');
                              },
                              isAllowPress: state.isValidateForm &&
                                  !state.showValidatePhoneNumber,
                            ),
                          ),
                        ],
                      ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPickupDetail() {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final showRowLayout = (screenWidth > 600 && screenWidth > screenHeight) || screenWidth > 800;
    
    return BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
      builder: (context, state) {
        final scheduleAt = state.scheduledAt ?? '';
        final scheduleTime = scheduleAt != '' ? DateTime.parse(state.scheduledAt ?? '') : DateTime.now();
        final formatter = DateFormat('MMM dd, yyyy - h:mm a');
        final scheduleFormat = scheduleAt != '' ? formatter.format(scheduleTime) : '';
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSectionTitle(l10n.pickupDetails),
            const SizedBox(height: 16),
            if (showRowLayout)
              IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Expanded(
                      child: _buildDeliveryTimingOption(
                        'ASAP',
                        l10n.avgDeliveryTime20to35mins,
                        state.deliveryType == 'ASAP',
                        valueSelected: state.deliveryType ?? 'ASAP',
                        currentValueSelected: 'ASAP',
                        isInRow: true,
                        onTap: () {
                          context.read<NewDeliveryCubit>().onChangedDeliveryType('ASAP');
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDeliveryTimingOption(
                        l10n.scheduleAhead,
                        state.scheduledAt != null && state.scheduledAt != '' ? scheduleFormat : l10n.chooseATime,
                        state.deliveryType == 'SCHEDULED',
                        currentValueSelected: 'SCHEDULED',
                        valueSelected: state.deliveryType ?? 'SCHEDULED',
                        isInRow: true,
                        onTap: () {
                          context.read<NewDeliveryCubit>().onChangedDeliveryType('SCHEDULED');
                          showSelectDateTime(scheduleAt != '' ? scheduleTime.toIso8601String() : '');
                        },
                      ),
                    ),
                  ],
                ),
              )
            else
              Column(
                children: [
                  _buildDeliveryTimingOption(
                    'ASAP',
                    l10n.avgDeliveryTime20to35mins,
                    state.deliveryType == 'ASAP',
                    currentValueSelected: 'ASAP',
                    valueSelected: state.deliveryType ?? 'ASAP',
                    isInRow: false,
                    onTap: () {
                      context.read<NewDeliveryCubit>().onChangedDeliveryType('ASAP');
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildDeliveryTimingOption(
                    l10n.scheduleAhead,
                    state.scheduledAt != null && state.scheduledAt != ''
                        ? scheduleFormat
                        : l10n.chooseATime,
                    state.deliveryType == 'SCHEDULED',
                    currentValueSelected: 'SCHEDULED',
                    valueSelected: state.deliveryType ?? 'SCHEDULED',
                    isInRow: false,
                    onTap: () {
                      context.read<NewDeliveryCubit>().onChangedDeliveryType('SCHEDULED');
                      showSelectDateTime(scheduleAt != '' ? scheduleTime.toIso8601String() : '');
                    },
                  ),
                ],
              ),
          ],
        );
      },
    );
  }

  Widget _buildDeliveryTimingOption(
    String title,
    String subtitle,
    bool isSelected, {
    Function()? onTap,
    required String valueSelected,
    required String currentValueSelected,
    required bool isInRow,
  }) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: Container(
        width: double.infinity,
        height: isInRow ? double.infinity : null, // Chỉ set height infinity khi ở Row
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.appBlackColor : AppColors.appBorderColor,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: isInRow 
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.appLabelTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Radio<String>(
              value: currentValueSelected,
              groupValue: valueSelected,
              onChanged: (value) => onTap?.call(),
              activeColor: AppColors.appBlackColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.darkPrimaryBackgroundColor,
      ),
    );
  }

  Widget _buildInputLabel(String label) {
    return Text(
      label,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppColors.darkPrimaryBackgroundColor,
      ),
    );
  }

  Widget _buildDropOffOption(
    String title,
    bool isSelected, {
    Function()? onTap,
    required String valueSelected,
    required String currentValueSelected,
  }) {
    return GestureDetector(
      onTap: () => onTap?.call(),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Radio<String>(
              value: currentValueSelected,
              groupValue: valueSelected,
              onChanged: (value) => onTap?.call(),
              activeColor: AppColors.appBlackColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlackColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String hintText,
    int maxLines = 1,
  }) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.appBorderColor,
          width: 1,
        ),
      ),
      child: TextField(
        controller: controller,
        minLines: 1,
        maxLines: maxLines,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: const TextStyle(
            color: AppColors.appLabelTextColor,
            fontSize: 16,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        style: const TextStyle(
          fontSize: 16,
          color: AppColors.darkPrimaryBackgroundColor,
        ),
      ),
    );
  }

  Widget _buildButton({
    required String text,
    required bool isPrimary,
    required VoidCallback onPressed,
    bool isAllowPress = true,
  }) {
    return ButtonLoading(
      height: 48,
      callback: onPressed,
      label: text,
      fontSize: 16,
      buttonBackgroundColor:
          !isPrimary ? Colors.white : const Color(0xFF444444),
      labelColor: !isPrimary ? const Color(0xFF444444) : Colors.white,
      borderRadius: 8,
      side: !isPrimary
          ? const BorderSide(color: AppColors.appBorderColor, width: 1)
          : null,
      isAllowPress: isAllowPress,
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leadingWidth: 0,
      leading: null,
      automaticallyImplyLeading: false,
      title: Text(
        l10n.addNewDelivery,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.lightPrimaryBackgroundColor,
              onPressed: () => {
                Navigator.of(context).pop(),
              },
              color: AppColors.lightPrimaryBackgroundColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/close-black.svg',
              ),
            ),
          ),
        )
      ],
    );
  }

  Future<void> showReviewQuote() async {
    final result = await showDialog<bool>(
      context: context,
      useSafeArea: false,
      builder: (BuildContext cxt) {
        return BlocProvider.value(
          value: context.read<NewDeliveryCubit>(),
          child: ReviewQuoteWidget(
            order: widget.order ?? Order(),
          ),
        );
      },
    );

    // Kiểm tra kết quả trả về
    if (result == true) {
      if (mounted) {
        Navigator.pop(context, true);
      }
    } else {
      print('ReviewQuoteWidget closed with false or cancelled');
    }
  }

  Future<void> showSelectDateTime(String scheduleTime) async {
    final result = await showDialog<String>(
      context: context,
      useSafeArea: false,
      builder: (BuildContext cxt) {
        return BlocProvider.value(
          value: context.read<NewDeliveryCubit>(),
          child: AddNewDeliveryDateTimePage(
            order: widget.order ?? Order(),
            initSchedule: scheduleTime,
          ),
        );
      },
    );

    if (result == 'Cancel') {
      if (mounted) {
        context.read<NewDeliveryCubit>().onChangedDeliveryType('ASAP');
      }
    }
  }
}
