import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@utils/datetime.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class AddNewDeliveryDateTimePage extends StatefulWidget {
  final Order? order;
  final String? initSchedule; // Format: "2025-08-23 00:50:48.000"
  const AddNewDeliveryDateTimePage({super.key, this.order, this.initSchedule});

  @override
  State<AddNewDeliveryDateTimePage> createState() => _AddNewDeliveryDateTimePageState();
}

class _AddNewDeliveryDateTimePageState extends State<AddNewDeliveryDateTimePage> {
  late final l10n = context.l10n;
  late DateTime selectedDate;
  String selectedTime = '';
  bool showMoreDates = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeDateTime();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      appBar: _buildAppBar(),
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date Selection Section
                      _buildDateSelectionSection(isTablet),
                      const SizedBox(height: 32),
                                
                      // Time Selection Section
                      _buildTimeSelectionSection(),
                                
                      const SizedBox(height: 40),
                                
                      
                    ],
                  ),
                ),
              ),
            ),
            // Action Buttons
              Container(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      width: 1,
                      color: AppColors.appBorderColor,
                    ),
                  ),
                ),
                child: _buildActionButtons(isTablet)),
              const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leadingWidth: 0,
      leading: null,
      automaticallyImplyLeading: false,
      title: Text(
        l10n.selectDeliveryDateTime,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        Padding(
          padding: const EdgeInsets.all(10.0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.lightPrimaryBackgroundColor,
              onPressed: () => Navigator.of(context).pop('Cancel'),
              color: AppColors.lightPrimaryBackgroundColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/close-black.svg',
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildDateSelectionSection(bool isTablet) {
    final displayDates = showMoreDates ? availableDates : availableDates.take(2).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
         Text(
          l10n.selectDeliveryDate,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.darkPrimaryBackgroundColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: isTablet ? 2 : 1,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            mainAxisExtent: 56,
          ),
          itemCount: displayDates.length,
          itemBuilder: (context, index) {
            final date = displayDates[index];
            final isSelected = date.day == selectedDate.day && date.month == selectedDate.month && date.year == selectedDate.year;
            final dayName = _getDayName(date.weekday);
            final dateString = '${date.month}/${date.day}/${date.year}';

            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedDate = date;
                  _validateAndUpdate();
                });
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? AppColors.appBlackColor : AppColors.appBorderColor,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      dayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.appBlackColor,
                      ),
                    ),
                    Text(
                      dateString,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        if (availableDates.length > 2)
          GestureDetector(
            onTap: () {
              setState(() {
                showMoreDates = !showMoreDates;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Center(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      showMoreDates ? l10n.showLess : l10n.showMore,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      showMoreDates ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      color: AppColors.appBlackColor.withValues(alpha: .6),
                      size: 30,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTimeSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.selectDeliveryTime,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.darkPrimaryBackgroundColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: errorMessage != '' ? AppColors.appErrorColor : AppColors.appBorderColor,
              width: 1,
            ),
          ),
          child: PopupMenuButton<String>(
            constraints: BoxConstraints(
              minWidth: MediaQuery.of(context).size.width / 2,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedTime,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.darkPrimaryBackgroundColor,
                  ),
                ),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.appBlackColor,
                  size: 28,
                ),
              ],
            ),
            itemBuilder: (context) => availableTimes.map((time) {
              return PopupMenuItem<String>(
                value: time,
                child: SizedBox(
                  width: double.infinity,
                  child: Text(
                    time,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: time == selectedTime ? FontWeight.w600 : FontWeight.w400,
                      color: time == selectedTime ? AppColors.appBlackColor : AppColors.appBlackColor.withValues(alpha: .6),
                    ),
                  ),
                ),
              );
            }).toList(),
            onSelected: (time) {
              setState(() {
                selectedTime = time;
                _validateAndUpdate();
              });
            },
          ),
        ),
        if (errorMessage != '')
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(
            errorMessage,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.appErrorColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(bool isTablet) {
    if (isTablet) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            width: 200,
            child: _buildButton(
              text: l10n.cancel,
              isPrimary: false,
              onPressed: () {
                if (mounted) {
                  Navigator.of(context).pop('Cancel');
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 200,
            child: _buildButton(
              text: l10n.done,
              isPrimary: true,
              onPressed: () {
                final dateTime = combineDateTime(selectedDate, selectedTime);
                context.read<NewDeliveryCubit>().onChangedScheduleAt(dateTime.toIso8601String());
                if (mounted) {
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: _buildButton(
              text: l10n.cancel,
              isPrimary: false,
              onPressed: () {
                if (mounted) {
                  Navigator.of(context).pop('Cancel');
                }
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildButton(
              text: l10n.done,
              isPrimary: true,
              isAllowPress: errorMessage == '',
              onPressed: () {
                final dateTime = combineDateTime(selectedDate, selectedTime);
                context.read<NewDeliveryCubit>().onChangedScheduleAt(dateTime.toIso8601String());
                if (mounted) {
                  Navigator.of(context).pop();
                }
              },
            ),
          ),
        ],
      );
    }
  }

  Widget _buildButton({
    required String text,
    required bool isPrimary,
    required VoidCallback onPressed,
    bool isAllowPress = true,
  }) {
    return Opacity(
      opacity: isAllowPress ? 1 : .5,
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: isPrimary ? AppColors.appBlackColor : Colors.white,
          border: Border.all(
            color: isPrimary ? AppColors.appBlackColor : AppColors.appBorderColor,
            width: 1,
          ),
        ),
        child: MaterialButton(
          onPressed: isAllowPress ? onPressed : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isPrimary ? Colors.white : AppColors.darkPrimaryBackgroundColor,
            ),
          ),
        ),
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case DateTime.monday:
        return 'Mon';
      case DateTime.tuesday:
        return 'Tue';
      case DateTime.wednesday:
        return 'Wed';
      case DateTime.thursday:
        return 'Thu';
      case DateTime.friday:
        return 'Fri';
      case DateTime.saturday:
        return 'Sat';
      case DateTime.sunday:
        return 'Sun';
      default:
        return '';
    }
  }

  DateTime combineDateTime(DateTime selectedDate, String selectedTime) {
    final timeFormat = DateFormat('h:mm a'); 
    final parsedTime = timeFormat.parse(selectedTime);
    // Ghép date và time
    final combinedDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      parsedTime.hour,
      parsedTime.minute,
    );
    
    return combinedDateTime;
  }

  void _validateAndUpdate() {
    setState(() {
      errorMessage = _validateDateTime(selectedDate, selectedTime);
    });
  }
  
  String _validateDateTime(DateTime selectedDate, String selectedTime) {
    try {
      final timeFormat = DateFormat('h:mm a');
      final parsedTime = timeFormat.parse(selectedTime);
      
      final selectedDateTime = DateTime(
        selectedDate.year,
        selectedDate.month,
        selectedDate.day,
        parsedTime.hour,
        parsedTime.minute,
      );
      
      final timezone = context.read<AuthBloc>().state.businessTimeZone ?? '';
      final now = DateTimeHelper.currentTime(timezone);
      final minimumDateTime = now.add(const Duration(minutes: 90));
      
      if (selectedDateTime.isBefore(minimumDateTime)) {
        return l10n.deliveryTimeRequired;
      }
      
      return '';
    } catch (e) {
      return l10n.somethingWentWrong;
    }
  }

  void _initializeDateTime() {
    if (widget.initSchedule != null && widget.initSchedule!.isNotEmpty) {
      try {
        final initDateTime = DateTime.parse(widget.initSchedule!);
        selectedDate = DateTime(initDateTime.year, initDateTime.month, initDateTime.day);
        selectedTime = DateFormat('h:mm a').format(initDateTime);
      } catch (e) {
        _setDefaultDateTime();
      }
    } else {
      _setDefaultDateTime();
    }
  }

  void _setDefaultDateTime() {
    final timezone = context.read<AuthBloc>().state.businessTimeZone ?? '';
    final now = DateTimeHelper.currentTime(timezone);
    selectedDate = DateTime(now.year, now.month, now.day);
    selectedTime = _getDefaultTime();
  }

  String _getDefaultTime() {
    final timezone = context.read<AuthBloc>().state.businessTimeZone ?? '';
    final now = DateTimeHelper.currentTime(timezone);
    final futureTime = now.add(const Duration(minutes: 105));
    
    final remainder = futureTime.minute % 15;
    final roundedTime = remainder == 0 
        ? futureTime 
        : futureTime.add(Duration(minutes: 15 - remainder));
    
    return DateFormat('h:mm a').format(roundedTime);
  }

  List<DateTime> get availableDates {
    final timezone = context.read<AuthBloc>().state.businessTimeZone ?? '';
    final now = DateTimeHelper.currentTime(timezone);
    final List<DateTime> dates = [];
    for (int i = 0; i < 7; i++) {
      dates.add(now.add(Duration(days: i)));
    }
    return dates;
  }

  List<String> get availableTimes {
    final List<String> times = [];
    for (int hour = 0; hour < 24; hour++) {
      for (int minute = 0; minute < 60; minute += 15) {
        final time = DateTime(2024, 1, 1, hour, minute);
        final timeString = '${time.hour == 0 ? 12 : time.hour > 12 ? time.hour - 12 : time.hour}:${minute.toString().padLeft(2, '0')} ${time.hour < 12 ? 'AM' : 'PM'}';
        times.add(timeString);
      }
    }
    return times;
  }

}
