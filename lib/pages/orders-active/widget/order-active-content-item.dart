// ignore_for_file: curly_braces_in_flow_control_structures

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-time-pickup-tab/order-active-time-pickup-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-prep-time/order-change-prep-time.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-status/order-change-status.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-active-reload/ordering-active-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/setting-menu-define.dart';

class ActiveProductWidget extends StatefulWidget {
  final Order? order;
  final VoidCallback onStatusChange;
  final bool? isHomeWidget;
  const ActiveProductWidget({
    super.key,
    required this.order,
    required this.onStatusChange,
    this.isHomeWidget = false,
  });

  @override
  State<ActiveProductWidget> createState() => _ActiveProductWidgetState();
}

class _ActiveProductWidgetState extends State<ActiveProductWidget> {
  Order? _order;
  Timer? _debounce;
  Timer? _debounceButton;
  Timer? _timer;
  Duration _timeLeft = Duration.zero;
  String? timeZone;
  int timeAddOrder = 0;

  @override
  void initState() {
    super.initState();
    _order = widget.order;
    timeZone = context.read<AuthBloc>().state.businessTimeZone;
    _startTimer();
  }

  void _startTimer() {
    _timer?.cancel();
    _updateTimeLeft();
    _timer = Timer.periodic(const Duration(minutes: 1), (_) {
      _updateTimeLeft();
    });
  }

  void _updateTimeLeft() {
    try {
      setState(() {
        _timeLeft = DateTimeHelper.currentTime(
          timeZone,
          time: _order?.prepEndTime,
        ).difference(
          DateTimeHelper.currentTime(timeZone),
        );
        if (_timeLeft.isNegative) {
          _timer?.cancel();
        }
      });
    } catch (e) {}
  }

  String _formatTime(AppLocalizations l10n) {
    final totalSeconds = _timeLeft.inSeconds;

    if (totalSeconds <= 0 || _isLessThanOneMinute()) {
      _timer?.cancel();
      return l10n.expired;
    }

    final totalMinutesRounded = (totalSeconds / 60).ceil();
    final hours = totalMinutesRounded ~/ 60;
    final minutes = totalMinutesRounded % 60;

    final hourText = hours > 0 ? '${hours}h ' : '';
    final minutesText = minutes > 0 ? '${minutes.toString().padLeft(2, '0')}m' : '';

    return hourText + minutesText;
  }

  bool _isLessThanOneMinute() {
    final now = DateTimeHelper.currentTime(timeZone);

    if (DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime).isBefore(now)) {
      return true;
    }
    final difference = DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime).difference(now);
    if (difference.inSeconds < 60) {
      return true;
    }
    return false;
  }

  void _doDebounce({Function? onChange, int milliseconds = 1000}) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(Duration(milliseconds: milliseconds), () {
      onChange?.call();
    });
  }

  void _doDebounceDelayButton({Function? onChange, int milliseconds = 1000}) {
    if (_debounceButton?.isActive ?? false) return;

    onChange?.call();
    _debounceButton = Timer(Duration(milliseconds: milliseconds), () {});
  }

  @override
  void dispose() {
    try {
      _timer?.cancel();
      _debounce?.cancel();
      _debounceButton?.cancel();
    } catch (e) {}

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final width = MediaQuery.of(context).size.width;
    final double widthItem = width <= 920 ? 260 : 290;
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: widthItem,
      ),
      child: Card(
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.all(4),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
          side: const BorderSide(
            width: .5,
            color: AppColors.appBlackColor,
          ),
        ),
        color: AppColors.lightPrimaryBackgroundColor,
        // color: isPayAtStore ? AppColors.orderPayAtStoreColor : AppColors.lightPrimaryBackgroundColor,
        child: Stack(
          children: [
            InkWell(
              radius: 24,
              onTap: () {
                AppBased.goToOrderDetailPage(
                  context,
                  _order?.id ?? '',
                  onChanged: (value) {
                    if (widget.isHomeWidget == true) {
                      SystemChrome.setPreferredOrientations([
                        DeviceOrientation.portraitUp,
                        DeviceOrientation.portraitDown,
                        DeviceOrientation.landscapeLeft,
                        DeviceOrientation.landscapeRight,
                      ]);
                    }
                    try {
                      context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
                    } catch (e) {
                      AppLog.e('Error: $e');
                    }
                  },
                );
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _buildHeader(context, _order),
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: _buildContent(context),
                    ),
                  ),
                  _buildButton(context),
                ],
              ),
            ),
            if (_order?.isShowDialog == true) _buildDialogProgress(l10n),
          ],
        ),
      ),
    );
  }

  String detectTimeSchedule(AppLocalizations l10n, DateTime? time) {
    final timeSchedule = DateTimeHelper.currentTime(timeZone, time: time);
    final currentTime = DateTimeHelper.currentTime(timeZone);
    final difference = timeSchedule.difference(currentTime);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;
    return '${days > 0 ? '$days ' : ''}${days == 1 ? l10n.days(' ') : days > 1 ? l10n.days('s') : ''}${hours > 0 ? ' $hours ' : ''}${hours == 1 ? l10n.hours(' ') : hours > 1 ? l10n.hours('s') : ''}${minutes > 0 ? ' $minutes ' : ''}${minutes == 1 ? l10n.minutes('') : minutes > 1 ? l10n.minutes('s') : ''}'
        .trim();
  }

  bool checkTimeSchedule(DateTime? time) {
    final timeSchedule = DateTimeHelper.currentTime(timeZone, time: time);
    final currentTime = DateTimeHelper.currentTime(timeZone);
    final difference = timeSchedule.difference(currentTime);
    final minutes = difference.inMinutes;
    return minutes <= 15;
  }

  Widget _buildDialogProgress(AppLocalizations l10n) {
    final isMobile = ResponsiveBreakpoints.of(context).isMobile || MediaQuery.of(context).size.height < 480;

    return Positioned.fill(
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: AppColors.appBlackColor.withValues(alpha: .6),
          borderRadius: BorderRadius.circular(24),
        ),
        child: Dialog(
          insetPadding: const EdgeInsets.fromLTRB(8, 0, 8, 0),
          backgroundColor: AppColors.lightPrimaryBackgroundColor,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/svgs/order-warning.svg',
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          l10n.confirmation,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isMobile ? 8 : 12),
                MarkdownBody(
                  data: l10n.confirmScheduleProgress(detectTimeSchedule(l10n, _order?.scheduledAt)),
                  styleSheet: MarkdownStyleSheet(
                    p: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: AppColors.homeShowQRBGColor,
                    ),
                  ),
                  onTapLink: (text, href, title) {},
                ),
                SizedBox(height: isMobile ? 8 : 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
                          backgroundColor: AppColors.lightPrimaryBackgroundColor,
                        ),
                        onPressed: () {
                          setState(() {
                            _order?.isShowDialog = false;
                          });
                        },
                        child: Text(
                          l10n.noRemindMe,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
                          backgroundColor: AppColors.orderMarkBG,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        onPressed: () {
                          setState(() {
                            _order?.isShowDialog = false;
                          });

                          doUndoStatus(statusChange: OrderStatusDefine.PREPARING.name, onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name));
                        },
                        child: Text(
                          l10n.yes,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.lightPrimaryBackgroundColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, Order? order) {
    final l10n = context.l10n;

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return AbsorbPointer(
          absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
          child: BlocBuilder<OrderActiveTimePickupTabCubit, OrderActiveTimePickupState>(
            builder: (context, state) {
              switch (state.tab) {
                case OrderActiveTimePickupStatus.TimePickup:
                  return Container(
                    padding: const EdgeInsets.all(12),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                    ),
                    child: _buildTimePickup(l10n, stateAuth),
                  );
                default:
                  return Container(
                    padding: const EdgeInsets.all(12),
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: _getStatusColor(),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        _getStatusText(context),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .9),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                        ),
                                      ),
                                      if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                                        Padding(
                                          padding: const EdgeInsets.only(left: 4.0),
                                          child: SvgPicture.asset(
                                            'assets/svgs/delivery-white.svg',
                                            colorFilter: const ColorFilter.mode(
                                              AppColors.lightPrimaryBackgroundColor,
                                              BlendMode.srcIn,
                                            ),
                                          ),
                                        )
                                    ],
                                  ),
                                  Text(
                                    _order?.customerName ?? _order?.customer?.displayName ?? l10n.guest,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      color: AppColors.lightPrimaryBackgroundColor,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name || _order?.status == OrderStatusDefine.READY.name)
                              InkWell(
                                onTap: ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name)
                                    ? () {
                                        context.read<OrderActiveTimePickupTabCubit>().onChangedTab();
                                      }
                                    : null,
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      _detectTimeAutoTitle(context, l10n, order, stateAuth),
                                      textAlign: TextAlign.end,
                                      style: const TextStyle(
                                        color: AppColors.lightPrimaryBackgroundColor,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    if ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name) ...[
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(right: 4.0),
                                            child: Text(
                                              _formatTime(l10n),
                                              style: const TextStyle(
                                                color: AppColors.lightPrimaryBackgroundColor,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18,
                                              ),
                                            ),
                                          ),
                                          SvgPicture.asset(
                                            'assets/svgs/expand-up-down.svg',
                                          )
                                        ],
                                      ),
                                    ]
                                    //  else if (isAutoCompleted(context, l10n, order, stateAuth)) ...[
                                    // Text(
                                    //   textAutoCompleted(context, l10n, order, stateAuth),
                                    //   textAlign: TextAlign.start,
                                    //   style: const TextStyle(
                                    //     color: AppColors.lightPrimaryBackgroundColor,
                                    //     fontSize: 16,
                                    //     fontWeight: FontWeight.w500,
                                    //   ),
                                    // ),
                                    // ]
                                  ],
                                ),
                              ),
                          ],
                        ),
                        BlocBuilder<OrdersActiveCubit, OrdersActiveState>(
                          builder: (context, state) {
                            if (state.status == OrdersActiveStatus.Success) {
                              final uid = order?.customerId ?? '';
                              final typeByID = state.selectedCustomer(uid)?.type;
                              if (typeByID != null) {
                                return SegmentTargetOffersWidget(
                                  title: typeByID,
                                  uid: uid,
                                  titleColor: AppColors.lightPrimaryBackgroundColor,
                                  // margin: const EdgeInsets.only(top: 2),
                                );
                              }
                            }
                            return const SizedBox.shrink();
                          },
                        )
                      ],
                    ),
                  );
              }
            },
          ),
        );
      },
    );
  }

  String _detectTimeAutoTitle(
    BuildContext context,
    AppLocalizations l10n,
    Order? order,
    AuthState stateAuth,
  ) {
    final status = order?.status?.toUpperCase();
    final orderAutoConfirm = stateAuth.business?.orderAutoConfirm ?? false;
    final orderAutoReady = stateAuth.business?.orderAutoReady ?? false;
    // final orderAutoComplete = stateAuth.business?.orderAutoComplete ?? false;
    // final orderAutoCompleteAfter = stateAuth.business?.orderAutoCompleteAfter;

    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return '';
      }
      return orderAutoConfirm ? l10n.autoConfirm : l10n.readyIn;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return orderAutoReady ? l10n.autoReady : l10n.readyIn;
    } else if (status == OrderStatusDefine.READY.name && hasPaid(_order?.payments ?? [])) {
      return '';
      // return orderAutoComplete && orderAutoCompleteAfter != null ? l10n.completedAt('') : '';
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return '';
    }
    return '';
  }

  bool isAutoCompleted(
    BuildContext context,
    AppLocalizations l10n,
    Order? order,
    AuthState stateAuth,
  ) {
    final status = order?.status?.toUpperCase();
    final orderAutoComplete = stateAuth.business?.orderAutoComplete ?? false;
    if (status == OrderStatusDefine.READY.name && orderAutoComplete && hasPaid(_order?.payments ?? [])) return true;
    return false;
  }

  // String textAutoCompleted(
  //   BuildContext context,
  //   AppLocalizations l10n,
  //   Order? order,
  //   AuthState stateAuth,
  // ) {
  //   final status = order?.status?.toUpperCase();
  //   final orderAutoComplete = stateAuth.business?.orderAutoComplete ?? false;
  //   final orderAutoCompleteAfter = stateAuth.business?.orderAutoCompleteAfter;
  //   final String timeZone = stateAuth.businessTimeZone ?? '';
  //   final is24H = Localizations.localeOf(context).languageCode == 'vi' ? true : false;

  //   if (status == OrderStatusDefine.READY.name && orderAutoComplete) return formatHHMM(formatHHMMFromDateTime(DateTime.parse(formatUtcToLocal(DateTimeHelper.formatDateTime(parseHHMMToToday(orderAutoCompleteAfter), timeZone).toString()))), is24H: is24H);
  //   return '';
  // }

  int formatHHMMFromDateTime(DateTime formatTime) {
    return (formatTime.hour * 100) + formatTime.minute;
  }

  String formatHHMM(dynamic input, {bool is24H = true}) {
    String hhmm;
    if (input is int) {
      if (input < 0) throw const FormatException('Negative number not allowed');
      hhmm = input.toString().padLeft(4, '0');
    } else if (input is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(input)) {
        throw const FormatException('Input string must be 1 to 4 digits (HHMM)');
      }
      hhmm = input.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));

    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0–23), minute (0–59)');
    }

    if (is24H) {
      final String h = hour.toString().padLeft(2, '0');
      final String m = minute.toString().padLeft(2, '0');
      return '$h:$m';
    } else {
      final isPM = hour >= 12;
      final hour12 = hour % 12 == 0 ? 12 : hour % 12;
      final m = minute.toString().padLeft(2, '0');
      final suffix = isPM ? 'PM' : 'AM';
      return '$hour12:$m $suffix';
    }
  }

  String formatUtcToLocal(String utcTimeString) {
    try {
      DateTime utcDateTime;

      try {
        utcDateTime = DateTime.parse('${utcTimeString}Z');
      } catch (e) {
        try {
          if (utcTimeString.endsWith('Z') || utcTimeString.contains('+00:00')) {
            utcDateTime = DateTime.parse(utcTimeString);
          } else {
            utcDateTime = DateTime.parse(utcTimeString).toUtc();
          }
        } catch (e2) {
          final RegExp pattern = RegExp(r'(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?');
          final match = pattern.firstMatch(utcTimeString);

          if (match != null) {
            final year = int.parse(match.group(1)!);
            final month = int.parse(match.group(2)!);
            final day = int.parse(match.group(3)!);
            final hour = int.parse(match.group(4)!);
            final minute = int.parse(match.group(5)!);
            final second = int.parse(match.group(6)!);
            final millisecond = match.group(7) != null ? int.parse(match.group(7)!) : 0;

            utcDateTime = DateTime.utc(year, month, day, hour, minute, second, millisecond);
          } else {
            throw Exception('Không thể phân tích chuỗi thời gian: $utcTimeString');
          }
        }
      }

      // Chuyển sang múi giờ địa phương
      final localDateTime = utcDateTime.toLocal();

      return '${localDateTime.year}-${_padZero(localDateTime.month)}-${_padZero(localDateTime.day)} '
          '${_padZero(localDateTime.hour)}:${_padZero(localDateTime.minute)}:${_padZero(localDateTime.second)}';
    } catch (e) {
      return utcTimeString;
    }
  }

  String _padZero(int number) {
    return number.toString().padLeft(2, '0');
  }

  DateTime parseHHMMToToday(dynamic hhmmInput) {
    String hhmm;
    if (hhmmInput is int) {
      if (hhmmInput < 0) {
        throw const FormatException('Input must be a positive integer');
      }
      hhmm = hhmmInput.toString().padLeft(4, '0');
    } else if (hhmmInput is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(hhmmInput)) {
        throw const FormatException('String must be 1 to 4 digits in HHMM format');
      }
      hhmm = hhmmInput.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0-23), minute (0-59)');
    }

    final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59);
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  Widget _buildTimePickup(AppLocalizations l10n, AuthState stateAuth) {
    final checkPrepEndTime = _isLessThanOneMinute();
    return BlocConsumer<OrderChangePrepTimeCubit, OrderChangePrepTimeState>(
      listener: (context, state) {
        switch (state.status) {
          case OrderChangePrepTimeStatus.Error:
            if (state.errorMsg != null && state.errorMsg != '') {
              AppBased.toastError(context, title: state.errorMsg);
              BlocProvider.of<OrderChangeStatusCubit>(context).onResetStatus();
            }
            return;
          case OrderChangePrepTimeStatus.AddTime:
            AppLog.e('ADD Time: ${state.time}');
            context.read<OrderChangePrepTimeCubit>().doOrderChangePrepTime(id: _order?.id, time: state.time ?? 0);
            return;
          case OrderChangePrepTimeStatus.Success:
            return;
          default:
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: InkWell(
            onTap: () => context.read<OrderActiveTimePickupTabCubit>().onChangedTab(),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  splashRadius: 16,
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(checkPrepEndTime ? AppColors.lightPrimaryBackgroundColor.withValues(alpha: .2) : AppColors.lightPrimaryBackgroundColor), // Màu background
                    shape: WidgetStateProperty.all(const CircleBorder()),
                  ),
                  disabledColor: checkPrepEndTime ? AppColors.lightPrimaryBackgroundColor.withValues(alpha: .2) : AppColors.lightPrimaryBackgroundColor,
                  icon: const Text(
                    '-5',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  isSelected: false,
                  onPressed: checkPrepEndTime
                      ? null
                      : () {
                          timeAddOrder += -5;
                          // final prepEndTime = DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime);
                          final prepEndTime = _order?.prepEndTime;
                          _order?.prepEndTime = prepEndTime?.add(const Duration(minutes: -5));
                          int minute = getMinutesDiff(_order?.prepEndTime);
                          if (minute < 0) {
                            _order?.prepEndTime = DateTimeHelper.currentTime(timeZone);
                            minute = 0;
                          }
                          AppLog.e('timeAddOrder: $timeAddOrder');
                          preEndTime(time: timeAddOrder);
                        },
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        _detectTimeAutoTitle(context, l10n, _order, stateAuth),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.lightPrimaryBackgroundColor,
                        ),
                      ),
                      Text(
                        _formatTime(l10n),
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.lightPrimaryBackgroundColor,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  splashRadius: 16,
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(AppColors.lightPrimaryBackgroundColor), // Màu background
                    shape: WidgetStateProperty.all(const CircleBorder()),
                  ),
                  icon: const Text(
                    '+5',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  onPressed: () {
                    timeAddOrder += 5;
                    final prepEndTime = _order?.prepEndTime;
                    _order?.prepEndTime = prepEndTime?.add(const Duration(minutes: 5));
                    final minute = getMinutesDiff(_order?.prepEndTime);
                    if (minute < 0) {
                      _order?.prepEndTime = prepEndTime?.add(Duration(minutes: minute.abs() + 10));
                    }

                    preEndTime(time: timeAddOrder);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void preEndTime({int? time}) {
    _doDebounce(
      milliseconds: 1000,
      onChange: () {
        context.read<OrderChangePrepTimeCubit>().onChangedTime(timeAddOrder);
        timeAddOrder = 0;
      },
    );
    _startTimer();
  }

  int getMinutesDiff(DateTime? dateTimeStr) {
    final DateTime now = DateTimeHelper.currentTime(timeZone);
    final diff = DateTimeHelper.currentTime(timeZone, time: dateTimeStr).difference(now);
    return diff.inMinutes;
  }

  String _pickupAt() {
    if (_order?.status == OrderStatusDefine.SCHEDULED.name || (_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt != null)) {
      return DateTimeHelper.dateTimeLongFormat(
        _order?.scheduledAt,
        timeZone: timeZone,
        isWeekDay: false,
        langLocale: Localizations.localeOf(context).languageCode,
      );
      // return DateTimeHelper.dateTimeFormatMDY(
      //   _order?.scheduledAt ?? DateTime.now(),
      //   timeZone: timeZone,
      // );
    }

    return DateTimeHelper.timeFormat(context, _order?.prepEndTime, timeZone: timeZone);
  }

  String _orderTime() {
    return DateTimeHelper.timeFormat(context, _order?.createdAt, timeZone: timeZone);
  }

  bool hasPaid(List<Payment> payments) {
    return payments.any(
      (payment) => payment.status?.toUpperCase() == OrderPaymentAtStore.CAPTURED.name,
    );
  }

  Widget _buildContent(BuildContext context) {
    final l10n = context.l10n;
    final isPayAtStore = !hasPaid(_order?.payments ?? []);
    final orderAllowPayAtStore = context.read<AuthBloc>().state.business?.orderAllowPayAtStore ?? false;
    final isNewDelivery = _order?.isDeliveryExpress != null && _order?.isDeliveryExpress == true;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isNewDelivery && orderAllowPayAtStore) ...[
          Container(
            color: AppColors.lightPrimaryBackgroundColor,
            padding: const EdgeInsets.only(top: 4, bottom: 4),
            child: _buildPayAtStore(
              isPayAtStore ? l10n.payAtStore.toUpperCase() : l10n.paid.toUpperCase(),
              colorBG: isPayAtStore ? AppColors.orderUnpaidBGColor : AppColors.orderPaidBGColor,
              colorTitle: isPayAtStore ? AppColors.orderUnpaidColor : AppColors.orderPaidColor,
            ),
          ),
        ],
        if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name) ...[
          BoxOrderDeliveryWidget(
            order: _order,
            isShipper: false,
            isOrderDetail: false,
          ),
          _buildDivider(),
        ],
        Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              // _buildContentHeader('clock.svg', l10n.orderTimeAtive(_orderTime())),
              // const SizedBox(height: 8),
              if (_order?.status == OrderStatusDefine.SCHEDULED.name || (_order?.status == OrderStatusDefine.CONFIRMED.name || _order?.status == OrderStatusDefine.PREPARING.name)) ...{
                _buildContentHeader('clock.svg', l10n.pickupAt(_pickupAt())),
                const SizedBox(height: 8),
              },
              if (!isNewDelivery) _buildContentHeader('order-cart.svg', l10n.itemsOrder(getTotalQuantity(), getTotalQuantity() > 1 ? 's' : '')),
              if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                Container(
                  padding: const EdgeInsets.only(top: 8),
                  child: _buildContentHeader('delivery-black.svg', l10n.delivery),
                ),
            ],
          ),
        ),
        _buildDivider(),
        if (isNewDelivery) ...{
          Container(
            padding: const EdgeInsets.fromLTRB(32, 16, 32, 32),
            alignment: Alignment.center,
            child: SvgPicture.asset('assets/svgs/delivery-box.svg'),
          ),
          _buildDivider(),
        },
        if (!isNewDelivery) _buildItemsList(),
        _buildItemsTotal(context),
      ],
    );
  }

  Widget _buildPayAtStore(
    String title, {
    Color colorBG = AppColors.orderingProductTypeColor,
    Color colorTitle = AppColors.lightPrimaryBackgroundColor,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: colorBG,
        border: Border.all(
          width: 1,
          color: colorBG,
        ),
      ),
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: colorTitle,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return ClipRRect(
      child: Container(
        height: 8,
        width: double.infinity,
        color: AppColors.appBGAvatarColor,
      ),
    );
  }

  Widget _buildContentHeader(
    String iconName,
    String title, {
    Color colorTitle = AppColors.appBlackColor,
  }) {
    return Row(
      children: [
        SvgPicture.asset(
          'assets/svgs/$iconName',
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              color: colorTitle,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemsList() {
    final l10n = context.l10n;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final currencyCode = state.currencyBusiness;
        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _order?.items?.length,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            final item = _order?.items?[index];
            final textPrice = CurrencyHelper.convertMoney((item?.subtotal ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD');
            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 25,
                        child: Text(
                          '${item?.quantity ?? 1}x',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Text(
                                    item?.title ?? '',
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 6.0),
                                  child: Text(
                                    item?.isVoucherFreeItem == true ? l10n.free : textPrice,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (item?.notes != null)
                              Container(
                                width: double.infinity,
                                margin: EdgeInsets.only(top: 10, bottom: item?.modifiers != null && item!.modifiers!.isNotEmpty ? 0 : 16),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.specialRequirementsColor.withValues(alpha: .1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  item?.notes ?? '',
                                  style: const TextStyle(
                                    color: AppColors.specialRequirementsColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            if (item?.modifiers != null && item!.modifiers!.isNotEmpty) ...[
                              ListView.builder(
                                shrinkWrap: true,
                                itemCount: groupModifierOrders(item.modifiers ?? []).length,
                                physics: const NeverScrollableScrollPhysics(),
                                padding: EdgeInsets.zero,
                                itemBuilder: (context, index) {
                                  final itemData = groupModifierOrders(item.modifiers ?? []).elementAt(index);
                                  final isBorder = itemData.id != groupModifierOrders(item.modifiers ?? []).last.id;
                                  final bool isItemLast = itemData.id == groupModifierOrders(item.modifiers ?? []).last.id;
                                  return DecoratedBox(
                                    decoration: BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(
                                          color: isBorder ? AppColors.homeBorderColor : AppColors.appTransparentColor,
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                    child: ListTile(
                                      contentPadding: EdgeInsets.fromLTRB(0, 8, 0, isItemLast ? 0 : 8),
                                      horizontalTitleGap: 0,
                                      minVerticalPadding: 0,
                                      minTileHeight: 0,
                                      subtitle: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${itemData.modifier?.name}',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.appBlackColor.withValues(alpha: .5),
                                            ),
                                          ),
                                          ListView.builder(
                                              shrinkWrap: true,
                                              itemCount: itemData.options?.length,
                                              physics: const NeverScrollableScrollPhysics(),
                                              padding: EdgeInsets.zero,
                                              itemBuilder: (context, index) {
                                                final option = itemData.options?[index];
                                                final textItemPrice = (option?.price ?? 0) > 0.0 ? CurrencyHelper.convertMoney((option?.price ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD') : null;

                                                return Padding(
                                                  padding: const EdgeInsets.only(top: 4.0),
                                                  child: Text(
                                                    '${option?.name}${textItemPrice != null ? ' ($textItemPrice)' : ''}',
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis,
                                                    style: const TextStyle(
                                                      fontSize: 14,
                                                      fontWeight: FontWeight.w500,
                                                      color: AppColors.appBlackColor,
                                                    ),
                                                  ),
                                                );
                                              }),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ]
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildDivider(),
              ],
            );
          },
        );
      },
    );
  }

  // List<ModifierOrder> groupModifierOrders() {
  //   final items = _order?.items ?? [];
  //   final Map<String, ModifierOrder> groupedModifiers = {};

  //   for (final item in items) {
  //     if (item.modifiers != null && item.modifiers!.isNotEmpty) {
  //       for (final modifier in item.modifiers!) {
  //         if (modifier != null && modifier.modifier != null) {
  //           final modifierId = modifier.modifier!.id ?? '';

  //           if (!groupedModifiers.containsKey(modifierId)) {
  //             // Tạo ModifierOrder mới với modifier info
  //             groupedModifiers[modifierId] = ModifierOrder(
  //               id: modifier.id,
  //               createdAt: modifier.createdAt,
  //               updatedAt: modifier.updatedAt,
  //               orderItemId: modifier.orderItemId,
  //               modifierId: modifier.modifierId,
  //               optionId: modifier.optionId,
  //               price: modifier.price,
  //               quantity: modifier.quantity,
  //               taxRate: modifier.taxRate,
  //               taxAmount: modifier.taxAmount,
  //               taxInclusive: modifier.taxInclusive,
  //               options: [], // Khởi tạo list options rỗng
  //               modifier: modifier.modifier,
  //               option: modifier.option,
  //             );
  //           }

  //           // Thêm option vào list options của modifier tương ứng
  //           // if (modifier.options != null) {
  //           final existingModifier = groupedModifiers[modifierId]!;
  //           final existingOptions = existingModifier.options ?? [];
  //           if (modifier.option != null) {
  //             existingOptions.add(modifier.option!);
  //           }

  //           // Cập nhật metadata với list options mới
  //           groupedModifiers[modifierId] = existingModifier.copyWith(
  //             options: existingOptions,
  //           );
  //           // }
  //         }
  //       }
  //     }
  //   }

  //   return groupedModifiers.values.toList();
  // }

// Function cho Flutter models của bạn
  List<ModifierOrder> groupModifierOrders(List<ModifierOrder> modifiers) {
    final Map<String, ModifierOrder> groupedModifiers = {};

    for (final modifier in modifiers) {
      if (modifier != null && modifier.modifier != null) {
        final modifierId = modifier.modifier!.id ?? '';

        if (!groupedModifiers.containsKey(modifierId)) {
          groupedModifiers[modifierId] = ModifierOrder(
            id: modifierId,
            modifier: modifier.modifier,
            options: [],
          );
        }
        final existingModifier = groupedModifiers[modifierId]!;
        final existingOptions = List<ModifierOption>.from(existingModifier.options ?? []);

        if (modifier.option != null) {
          final optionWithPrice = ModifierOption(
            id: modifier.option!.id,
            name: modifier.option!.name,
            price: modifier.price,
            currencyCode: modifier.option!.currencyCode,
            isUnavailable: modifier.option!.isUnavailable,
            unAvailableUntil: modifier.option!.unAvailableUntil,
          );
          existingOptions.add(optionWithPrice);
        }
        groupedModifiers[modifierId] = existingModifier.copyWith(
          options: existingOptions,
        );
      }
    }

    return groupedModifiers.values.toList();
  }

  int getTotalQuantity() {
    final items = _order?.items ?? [];
    return items.fold(0, (sum, item) => sum + (item.quantity ?? 0));
  }

  Widget _buildItemsTotal(BuildContext context) {
    final l10n = context.l10n;
    final itemsQuantity = getTotalQuantity();
    final isNewDelivery = _order?.isDeliveryExpress != null && _order?.isDeliveryExpress == true;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final currencyCode = state.currencyBusiness;
        final currencyBusiness = _order?.currencyCode ?? currencyCode ?? 'USD';
        final discountTotal = num.parse((_order?.discountTotal ?? 0).toString()) > 0 ? CurrencyHelper.convertMoney('${_order?.discountTotal ?? 0}', code: currencyBusiness) : null;
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isNewDelivery)
                Text(
                  l10n.lengthTotalItems(itemsQuantity.toString(), itemsQuantity > 1 ? 'S' : ''),
                  style: const TextStyle(
                    color: AppColors.appBlackColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              if (!isNewDelivery && (_order?.subtotal ?? 0) > 0)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.subtotal,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.subtotal ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              if (!isNewDelivery && (_order?.taxTotal ?? 0) > 0)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.tax,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.taxTotal ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name && (_order?.deliveryFee ?? 0) > 0)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.deliveryFee,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.deliveryFee ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              // if (isNewDelivery && _order?.tipAmount != null && _order?.tipAmount != 0)
              // Padding(
              //   padding: const EdgeInsets.only(top: 0.0, bottom: 8),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       Text(
              //         l10n.tip,
              //         style: const TextStyle(
              //           color: AppColors.appBlackColor,
              //           fontWeight: FontWeight.w600,
              //           fontSize: 16,
              //         ),
              //       ),
              //       Text(
              //         CurrencyHelper.convertMoney((_order?.tipAmount ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
              //         style: const TextStyle(
              //           color: AppColors.appBlackColor,
              //           fontWeight: FontWeight.w600,
              //           fontSize: 16,
              //         ),
              //       ),
              //     ],
              //   ),
              // ),
              if (_order?.discounts != null && _order!.discounts!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Column(
                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              l10n.discounts,
                              style: const TextStyle(
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          if (discountTotal != null && discountTotal != '') ...[
                            const SizedBox(width: 16),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                '-$discountTotal',
                                style: const TextStyle(
                                  color: AppColors.appResendColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        primary: false,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        itemBuilder: (context, index) {
                          final discount = _order?.discounts?[index];
                          return Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  ' • ',
                                  style: TextStyle(
                                    color: AppColors.appBlackColor.withValues(alpha: .56),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    discount?.rewardName ?? discount?.voucherName ?? '',
                                    style: TextStyle(
                                      color: AppColors.appBlackColor.withValues(alpha: .56),
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                        itemCount: _order?.discounts?.length ?? 0,
                      ),
                    ],
                  ),
                ),
              if (_order?.tipAmount != null && _order?.tipAmount != 0)
                Padding(
                  padding: const EdgeInsets.only(top: 0.0, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.tip,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.tipAmount ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              const Divider(
                height: 8,
                color: AppColors.appBorderColor,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      l10n.total,
                      style: const TextStyle(
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      CurrencyHelper.convertMoney((_order?.total ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                      style: const TextStyle(
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildButton(BuildContext context) {
    final isFontMobile = MediaQuery.of(context).size.width < 600 || MediaQuery.of(context).size.height < 600;
    final l10n = context.l10n;
    final isNewScheduled = _order?.scheduledAt != null && _order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name;
    final bool isDelivery = _order?.type?.toUpperCase() == OrderType.DELIVERY.name;
    final isNewDelivery = _order?.isDeliveryExpress != null && _order?.isDeliveryExpress == true;

    if (isNewDelivery && _order?.status?.toUpperCase() == OrderStatusDefine.READY.name) {
      return Padding(
        padding: const EdgeInsets.all(8),
        child: BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
          builder: (context, newState) {
            return ButtonLoading(
              height: isFontMobile ? 48 : 56,
              padding: EdgeInsets.zero,
              borderRadius: 16,
              iconDirection: TextDirection.rtl,
              textDirection: TextDirection.rtl,
              label: l10n.cancelDelivery,
              fontSize: isFontMobile ? 18 : 20,
              circularLoadingSize: 24,
              fontWeight: FontWeight.w600,
              labelColor: AppColors.lightPrimaryBackgroundColor,
              circularStrokeColor: AppColors.appColor,
              marginLoadingIcon: EdgeInsets.zero,
              labelColorDisable: AppColors.lightPrimaryBackgroundColor,
              buttonBackgroundColor: const Color(0xFFFF4E64),
              callback: () {
                context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
              },
              isLoading: newState.status == NewDeliveryStatus.Loading,
            );
          },
        ),
      );
    }

    if (isDelivery && _order?.status?.toUpperCase() == OrderStatusDefine.READY.name) {
      return const SizedBox.shrink();
    }

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        return BlocConsumer<OrderChangeStatusCubit, OrderChangeStatusState>(
          listener: (context, state) {
            switch (state.status) {
              case OrderChangeStatusStatus.Error:
                if (state.errorMsg != null && state.errorMsg != '') {
                  AppBased.toastError(context, title: state.errorMsg);
                  BlocProvider.of<OrderChangeStatusCubit>(context).onResetStatus();
                  context.read<OrdersActiveCubit>().getOrdersActive(loading: false);
                }
                return;
              case OrderChangeStatusStatus.Success:
                // final String status = _detectStatusOrder(_order?.status?.toUpperCase());
                // _order?.status = status;
                // context.read<OrdersActiveCubit>().onUpdateOrders(_order);
                return;
              default:
            }
          },
          builder: (context, state) {
            if (!stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3])) {
              return const SizedBox.shrink();
            }

            final isReadyOrder = _order?.status?.toUpperCase() == OrderStatusDefine.READY.name;
            final isPayAtStore = !hasPaid(_order?.payments ?? []);

            return AbsorbPointer(
              absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
              child: Container(
                margin: const EdgeInsets.only(bottom: 2),
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (!isNewScheduled) ...[
                      if (isNewDelivery) ...[
                        BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
                          builder: (context, newState) {
                            return ButtonLoading(
                              height: isFontMobile ? 48 : 56,
                              padding: EdgeInsets.zero,
                              borderRadius: 16,
                              iconDirection: TextDirection.rtl,
                              textDirection: TextDirection.rtl,
                              label: l10n.cancelDelivery,
                              fontSize: isFontMobile ? 18 : 20,
                              circularLoadingSize: 24,
                              fontWeight: FontWeight.w600,
                              labelColor: AppColors.lightPrimaryBackgroundColor,
                              circularStrokeColor: AppColors.appColor,
                              marginLoadingIcon: EdgeInsets.zero,
                              labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                              buttonBackgroundColor: const Color(0xFFFF4E64),
                              callback: () {
                                context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
                              },
                              isLoading: newState.status == NewDeliveryStatus.Loading,
                            );
                          },
                        ),
                      ] else if (isPayAtStore && isReadyOrder) ...[
                        OrderManagePaymentButtonWidget(
                          order: _order,
                          isListOrder: true,
                          height: isFontMobile ? 48 : 56,
                          fontSize: isFontMobile ? 18 : 20,
                          labelColor: AppColors.lightPrimaryBackgroundColor,
                          buttonBackgroundColor: AppColors.appBlackColor,
                          borderRadius: 16,
                        )
                      ] else
                        ButtonLoading(
                          height: isFontMobile ? 48 : 56,
                          padding: EdgeInsets.zero,
                          borderRadius: 16,
                          isAllowPress: state.status != OrderChangeStatusStatus.Loading,
                          isLoading: state.status == OrderChangeStatusStatus.Loading,
                          iconDirection: TextDirection.rtl,
                          textDirection: TextDirection.rtl,
                          label: _getButtonText(context),
                          widget: ((_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name || _order?.status?.toUpperCase() == OrderStatusDefine.SCHEDULED.name) && _order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                              ? Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      l10n.markInProgress,
                                      style: TextStyle(
                                        fontSize: isFontMobile ? 16 : 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.lightPrimaryBackgroundColor,
                                      ),
                                    ),
                                    Text(
                                      l10n.markInProgressDispatchDriver,
                                      textDirection: TextDirection.ltr,
                                      style: TextStyle(
                                        fontSize: isFontMobile ? 16 : 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.lightPrimaryBackgroundColor,
                                      ),
                                    )
                                  ],
                                )
                              : null,
                          fontSize: isFontMobile ? 18 : 20,
                          circularLoadingSize: 24,
                          fontWeight: FontWeight.w600,
                          labelColor: AppColors.lightPrimaryBackgroundColor,
                          circularStrokeColor: AppColors.appColor,
                          marginLoadingIcon: EdgeInsets.zero,
                          labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                          buttonBackgroundColor: AppColors.appBlackColor,
                          callback: () {
                            doUndoStatus(onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: status));
                          },
                        ),
                    ],
                    if (isNewScheduled) ...{
                      if (isNewDelivery) ...{
                        BlocBuilder<NewDeliveryCubit, NewDeliveryState>(
                          builder: (context, newState) {
                            if (isNewScheduled) {
                              return ButtonLoading(
                                height: isFontMobile ? 48 : 56,
                                padding: EdgeInsets.zero,
                                borderRadius: 16,
                                iconDirection: TextDirection.rtl,
                                textDirection: TextDirection.rtl,
                                label: l10n.markInProgress,
                                fontSize: isFontMobile ? 18 : 20,
                                circularLoadingSize: 24,
                                fontWeight: FontWeight.w600,
                                labelColor: AppColors.lightPrimaryBackgroundColor,
                                circularStrokeColor: AppColors.appColor,
                                marginLoadingIcon: EdgeInsets.zero,
                                labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                                buttonBackgroundColor: AppColors.appBlackColor,
                                callback: () {
                                  context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name);
                                },
                                isLoading: state.status == OrderChangeStatusStatus.Loading,
                              );
                            }
                            return ButtonLoading(
                              height: isFontMobile ? 48 : 56,
                              padding: EdgeInsets.zero,
                              borderRadius: 16,
                              iconDirection: TextDirection.rtl,
                              textDirection: TextDirection.rtl,
                              label: l10n.cancelDelivery,
                              fontSize: isFontMobile ? 18 : 20,
                              circularLoadingSize: 24,
                              fontWeight: FontWeight.w600,
                              labelColor: AppColors.lightPrimaryBackgroundColor,
                              circularStrokeColor: AppColors.appColor,
                              marginLoadingIcon: EdgeInsets.zero,
                              labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                              buttonBackgroundColor: const Color(0xFFFF4E64),
                              callback: () {
                                context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
                              },
                              isLoading: newState.status == NewDeliveryStatus.Loading,
                            );
                          },
                        ),
                      } else if (_order?.isOnTap != true) ...{
                        ButtonLoading(
                          height: isFontMobile ? 48 : 56,
                          padding: EdgeInsets.zero,
                          borderRadius: 16,
                          iconDirection: TextDirection.rtl,
                          textDirection: TextDirection.rtl,
                          label: l10n.confirm,
                          fontSize: isFontMobile ? 18 : 20,
                          circularLoadingSize: 24,
                          fontWeight: FontWeight.w600,
                          labelColor: AppColors.lightPrimaryBackgroundColor,
                          circularStrokeColor: AppColors.appColor,
                          marginLoadingIcon: EdgeInsets.zero,
                          labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                          buttonBackgroundColor: AppColors.appBlackColor,
                          callback: () {
                            _order?.isOnTap = true;
                            setState(() {});
                          },
                        ),
                      } else ...{
                        ButtonLoading(
                          borderRadius: 16,
                          shape: isNewScheduled ? const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))) : null,
                          height: (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name && _order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                              ? 56
                              : isFontMobile
                                  ? 48
                                  : 56,
                          padding: EdgeInsets.zero,
                          isAllowPress: state.status != OrderChangeStatusStatus.Loading,
                          isLoading: state.status == OrderChangeStatusStatus.Loading,
                          iconDirection: TextDirection.rtl,
                          textDirection: TextDirection.rtl,
                          label: _getButtonText(context),
                          widget: (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name && _order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 4),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        l10n.markInProgress,
                                        style: TextStyle(
                                          fontSize: isFontMobile ? 16 : 18,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.lightPrimaryBackgroundColor,
                                        ),
                                      ),
                                      Text(
                                        l10n.markInProgressDispatchDriver,
                                        textDirection: TextDirection.ltr,
                                        style: TextStyle(
                                          fontSize: isFontMobile ? 16 : 18,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.lightPrimaryBackgroundColor,
                                        ),
                                      )
                                    ],
                                  ),
                                )
                              : null,
                          fontSize: isFontMobile ? 18 : 20,
                          circularLoadingSize: 24,
                          fontWeight: FontWeight.w600,
                          labelColor: AppColors.lightPrimaryBackgroundColor,
                          circularStrokeColor: AppColors.appColor,
                          marginLoadingIcon: EdgeInsets.zero,
                          labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                          buttonBackgroundColor: AppColors.appBlackColor,
                          callback: () {
                            if (checkTimeSchedule(_order?.scheduledAt)) {
                              doUndoStatus(statusChange: OrderStatusDefine.PREPARING.name, onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name));
                            } else
                              setState(() {
                                _order?.isShowDialog = true;
                              });
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: .5),
                          child: ButtonLoading(
                            borderRadius: 16,
                            shape: isNewScheduled ? const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(bottom: Radius.circular(16))) : null,
                            height: isFontMobile ? 48 : 56,
                            padding: EdgeInsets.zero,
                            isAllowPress: state.status != OrderChangeStatusStatus.LoadingSheduled,
                            isLoading: state.status == OrderChangeStatusStatus.LoadingSheduled,
                            iconDirection: TextDirection.rtl,
                            textDirection: TextDirection.rtl,
                            label: l10n.remindMe,
                            fontSize: isFontMobile ? 18 : 20,
                            circularLoadingSize: 24,
                            fontWeight: FontWeight.w600,
                            labelColor: AppColors.lightPrimaryBackgroundColor,
                            circularStrokeColor: AppColors.appColor,
                            marginLoadingIcon: EdgeInsets.zero,
                            labelColorDisable: AppColors.lightPrimaryBackgroundColor,
                            buttonBackgroundColor: AppColors.appBlackColor,
                            callback: () {
                              doUndoStatus(onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.SCHEDULED.name));
                            },
                          ),
                        ),
                      }
                    }
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  bool isSpecialRequirements(Order? order) {
    final checkSpecialRequirements = order?.items?.firstWhereOrNull((item) => item.notes != null);
    if (checkSpecialRequirements != null) {
      return true;
    }
    return false;
  }

  String _detectStatusOrder(String? status) {
    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return OrderStatusDefine.SCHEDULED.name;
      }
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return OrderStatusDefine.READY.name;
    } else if (status == OrderStatusDefine.READY.name) {
      return OrderStatusDefine.COMPLETED.name;
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.CANCELLED.name) {
      return OrderStatusDefine.CANCELLED.name;
    } else
      return '';
  }

  Color _getStatusColor() {
    switch (_order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        if (_order?.scheduledAt != null) {
          return AppColors.orderScheduledBGColor;
        } else {
          return AppColors.orderNewBGColor;
        }
      case 'PREPARING':
        return AppColors.orderInProgressBGColor;
      case 'READY':
        return AppColors.orderReadyBGColor;
      case 'SCHEDULED':
        return AppColors.orderScheduledBGColor;
      case 'CANCELLED':
        return AppColors.orderCancelledBGColor;
      case 'COMPLETED':
        return AppColors.orderCompletedBGColor;
      case 'REFUNDED':
        return AppColors.orderRefundedBGColor;
      default:
        return AppColors.orderNewBGColor;
    }
  }

  String _getButtonText(BuildContext context) {
    final l10n = context.l10n;
    switch (_order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        return l10n.markInProgress;
      case 'PREPARING':
        return l10n.markReady;
      case 'READY':
        return l10n.markCompleted;
      case 'SCHEDULED':
        return l10n.markInProgress;
      case 'COMPLETED':
        return l10n.markCompleted;
      default:
        return '';
    }
  }

  void doUndoStatus({String? statusChange, required ValueChanged<String>? onCompleted}) {
    _doDebounceDelayButton(
        milliseconds: 2500,
        onChange: () {
          AppLog.e('doUndoStatus.....');
          context.read<OrderingActiveReloadCubit>().onChangedStatus(OrderingActiveReloadStatus.Loading);

          final l10n = context.l10n;
          String status = statusChange ?? _detectStatusOrder(_order?.status?.toUpperCase());
          _order?.status = status;
          // context.read<OrdersActiveCubit>().onUpdateStatusOrdersInActive(_order);
          setState(() {});
          final String msg = l10n.undoMsg(_getStatusText(context, status: _detectUndoStatusOrder(_order?.status?.toUpperCase())), _getStatusText(context, status: statusChange));
          showUndoSnackbar(
            context,
            msg,
            timeUndo: 2,
            onUndo: () {
              status = _detectUndoStatusOrder(_order?.status?.toUpperCase());
              _order?.status = status;
              context.read<OrdersActiveCubit>().onUpdateStatusOrdersInActive(_order);
            },
            onActionComplete: () {
              onCompleted?.call(status);
            },
          );
        });
  }

  String _getStatusText(BuildContext context, {String? status}) {
    final l10n = context.l10n;
    switch (status ?? _order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        if (_order?.scheduledAt != null) {
          return l10n.newScheduleOrder;
        }
        return l10n.newOrder;
      case 'PREPARING':
        return l10n.inProgressOrder;
      case 'READY':
        return l10n.readyOrder;
      case 'SCHEDULED':
        return l10n.scheduledOrder;
      case 'CANCELLED':
        return l10n.cancelledStatus;
      case 'COMPLETED':
        return l10n.completed;
      case 'REFUNDED':
        return l10n.refunded;
      default:
        return '';
    }
  }

  String _detectUndoStatusOrder(String? status) {
    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return OrderStatusDefine.SCHEDULED.name;
      }
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else if (status == OrderStatusDefine.READY.name) {
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else if (status == OrderStatusDefine.COMPLETED.name) {
      return OrderStatusDefine.READY.name;
    } else if (status == OrderStatusDefine.CANCELLED.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else
      return '';
  }

  void showUndoSnackbar(BuildContext context, String msg, {int timeUndo = 2, VoidCallback? onUndo, VoidCallback? onActionComplete}) {
    // ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).clearSnackBars();

    final l10n = context.l10n;
    bool isUndoPressed = false;
    bool isCompleted = false;
    OverlayEntry? overlayEntry;

    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      behavior: SnackBarBehavior.floating,
      dismissDirection: DismissDirection.none,
      // padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
      backgroundColor: AppColors.darkPrimaryBackgroundColor,

      content: Row(
        children: [
          Expanded(
            child: MarkdownBody(
              data: msg,
              styleSheet: MarkdownStyleSheet(
                p: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.lightPrimaryBackgroundColor,
                ),
              ),
              onTapLink: (text, href, title) {},
            ),
          ),
          TextButton(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.fromLTRB(42, 16, 42, 16),
              backgroundColor: AppColors.orderMarkBG,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            onPressed: () {
              try {
                onUndo?.call();
                overlayEntry?.remove();
                overlayEntry = null;
                isCompleted = true;
                isUndoPressed = true;
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              } catch (e) {}
            },
            child: Text(
              l10n.undo,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.lightPrimaryBackgroundColor,
              ),
            ),
          ),
        ],
      ),
      duration: Duration(seconds: timeUndo),
    ));
    // ScaffoldMessenger.of(context).showSnackBar(snackBar);

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 0,
        left: 0,
        right: 0,
        bottom: kBottomNavigationBarHeight + 48,
        child: IgnorePointer(
          ignoring: false,
          child: GestureDetector(
            onTap: () {},
            behavior: HitTestBehavior.opaque,
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ),
    );
    try {
      Overlay.of(context).insert(overlayEntry!);
      Timer(Duration(seconds: timeUndo), () {
        try {
          if (overlayEntry != null) {
            overlayEntry?.remove();
            overlayEntry = null;
            if (!isUndoPressed && !isCompleted) {
              isCompleted = true;
              onActionComplete?.call();
            }
          }
        } catch (e) {}
      });
    } catch (e) {}
  }
}
