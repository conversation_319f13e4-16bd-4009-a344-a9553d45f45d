// ignore_for_file: strict_raw_type, inference_failure_on_function_invocation, empty_catches, unused_element_parameter, sized_box_for_whitespace, inference_failure_on_instance_creation

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customer-segment/customer-segment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-time-pickup-tab/order-active-time-pickup-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-prep-time/order-change-prep-time.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-status/order-change-status.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-active-reload/ordering-active-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-local/printers-local.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-customer/tags-customer.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/issue-with-order/issue-with-order-page.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/kitchen-ticket-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/star-customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/star-kitchen-ticket-template.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/setting-menu-define.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderActiveDetailContent extends StatefulWidget {
  final Order? order;
  const OrderActiveDetailContent({Key? key, required this.order}) : super(key: key);

  @override
  State<OrderActiveDetailContent> createState() => _OrderActiveDetailContentState();
}

class _OrderActiveDetailContentState extends State<OrderActiveDetailContent> with WidgetsBindingObserver {
  Order? _order;
  Timer? _timer;
  Timer? _debounce;
  Timer? _debounceButton;
  Duration _timeLeft = Duration.zero;
  String? timeZone;
  int timeAddOrder = 0;
  bool canPop = false;

  @override
  void initState() {
    super.initState();
    _order = widget.order;
    timeZone = context.read<AuthBloc>().state.businessTimeZone;
    if (_order?.customer?.id != null) {
      context.read<TagsCustomerCubit>()
        ..onClearAllSelect()
        ..getCustomerTags(uid: _order?.customer?.id ?? '');
    }
    _startTimer();
  }

  void _startTimer() {
    try {
      _timer?.cancel();
      _updateTimeLeft();
      _timer = Timer.periodic(const Duration(minutes: 1), (_) {
        _updateTimeLeft();
      });
    } catch (e) {}
  }

  void _updateTimeLeft() {
    try {
      setState(() {
        _timeLeft = DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime).difference(
          DateTimeHelper.currentTime(timeZone),
        );
        if (_timeLeft.isNegative) {
          _timer?.cancel();
        }
      });
    } catch (e) {}
  }

  String _formatTime(AppLocalizations l10n) {
    final totalSeconds = _timeLeft.inSeconds;

    if (totalSeconds <= 0 || _isLessThanOneMinute()) {
      _timer?.cancel();
      return l10n.expired;
    }

    final totalMinutesRounded = (totalSeconds / 60).ceil();
    final hours = totalMinutesRounded ~/ 60;
    final minutes = totalMinutesRounded % 60;

    final hourText = hours > 0 ? '${hours}h ' : '';
    final minutesText = minutes > 0 ? '${minutes.toString().padLeft(2, '0')}m' : '';

    return hourText + minutesText;
  }

  bool _isLessThanOneMinute() {
    try {
      final now = DateTimeHelper.currentTime(timeZone);
      if (DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime).isBefore(now)) {
        return true;
      }
      final difference = DateTimeHelper.currentTime(timeZone, time: _order?.prepEndTime).difference(now);
      if (difference.inSeconds < 60) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  void _doDebounce({Function? onChange, int milliseconds = 1000}) {
    try {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(Duration(milliseconds: milliseconds), () {
        onChange?.call();
      });
    } catch (e) {}
  }

  void _doDebounceDelayButton({Function? onChange, int milliseconds = 1000}) {
    try {
      if (_debounceButton?.isActive ?? false) return;
      onChange?.call();
      _debounceButton = Timer(Duration(milliseconds: milliseconds), () {});
    } catch (e) {}
  }

  int getMinutesDiff(DateTime? dateTimeStr) {
    final DateTime now = DateTimeHelper.currentTime(timeZone);
    final diff = DateTimeHelper.currentTime(timeZone, time: dateTimeStr).difference(now);
    return diff.inMinutes;
  }

  void doUndoStatus({String? statusChange, String? statusOldChange, required ValueChanged<String>? onCompleted}) {
    _doDebounceDelayButton(
      milliseconds: 2000,
      onChange: () {
        try {
          context.read<OrderingActiveReloadCubit>().onChangedStatus(OrderingActiveReloadStatus.Loading);

          final l10n = context.l10n;
          String status = statusChange ?? _detectStatusOrder(_order?.status?.toUpperCase());
          _order?.status = status;
          _order?.isChangeStatusLocal = true;
          context.read<OrderDetailCubit>().onChangeOrder(_order);
          final String msg = l10n.undoMsg(
            statusOldChange != null
                ? _getStatusText(context, status: statusOldChange)
                : _getStatusText(
                    context,
                    status: _detectUndoStatusOrder(
                      _order?.status?.toUpperCase(),
                    ),
                  ),
            _getStatusText(context, status: statusChange),
          );
          showUndoSnackbar(
            context,
            msg,
            timeUndo: 2,
            onUndo: () {
              status = statusOldChange ?? _detectUndoStatusOrder(_order?.status?.toUpperCase());
              _order?.status = status;
              _order?.isChangeStatusLocal = true;
              context.read<OrderDetailCubit>().onChangeOrder(_order);
            },
            onActionComplete: () {
              onCompleted?.call(status);
            },
          );
        } catch (e) {}
      },
    );
  }

  String _orderTime() {
    return DateTimeHelper.timeFormat(context, _order?.createdAt, timeZone: timeZone);
  }

  bool hasPaid(List<Payment> payments) {
    final isPayInStore = context.read<AuthBloc>().state.business?.orderAllowPayAtStore ?? false;
    return payments.any((payment) => payment.status?.toUpperCase() == OrderPaymentAtStore.CAPTURED.name) || !isPayInStore;
  }

  @override
  void dispose() {
    try {
      _timer?.cancel();
      _debounce?.cancel();
      _debounceButton?.cancel();
    } catch (e) {}

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    final allowRefund = _order?.allowRefund ?? false;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    final isFontMobile = MediaQuery.of(context).size.width < 600 || MediaQuery.of(context).size.height < 600;

    /// field for print
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    final EpsonPrinterKitchenTicket epsonPrinterKitchen = EpsonPrinterKitchenTicket();
    final EpsonPrinterReceipt epsonPrinterReceipt = EpsonPrinterReceipt();
    final StarPrinterReceipt starPrinterReceipt = StarPrinterReceipt();
    final StarPrinterKitchen starPrinterKitchen = StarPrinterKitchen();
    final printersReceipt = context.read<PrintersLocalCubit>().state.printersReceiptEnable ?? [];
    final printersKitchenTicket = context.read<PrintersLocalCubit>().state.printersKitchenTicketEnable ?? [];

    return Scaffold(
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      appBar: AppBar(
        toolbarHeight: 0,
        backgroundColor: AppColors.appBlackColor,
      ),
      bottomNavigationBar: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, stateAuth) {
          if (!stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3])) {
            return const SizedBox.shrink();
          }

          final bool isDelivery = _order?.type?.toUpperCase() == OrderType.DELIVERY.name;

          final isHidenMarkCompletedDelivery = ((_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name || _order?.status?.toUpperCase() == OrderStatusDefine.PREPARING.name) && isDelivery) ? false : true;

          if (_order?.isDeliveryExpress == true) {
            final newDeliveryButton = BlocConsumer<NewDeliveryCubit, NewDeliveryState>(
              listener: (context, state) {
                if (state.status == NewDeliveryStatus.Success) {
                  AppBased.toastSuccess(context, title: l10n.successfully);
                } else if (state.status == NewDeliveryStatus.Canceled) {
                  context.read<OrderDetailCubit>().getOrderDetail(id: widget.order?.id);
                } else if (state.status == NewDeliveryStatus.Error) {
                  if (state.errorMsg != null && state.errorMsg != '') {
                    AppBased.toastError(context, title: state.errorMsg);
                    context.read<NewDeliveryCubit>().onRemoveErrMsg('');
                  }
                }
              },
              builder: (context, state) {
                return isTablet
                    ? Container(
                        padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              width: 1,
                              color: AppColors.appBorderColor,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name)
                              ButtonLoading(
                                height: 48,
                                callback: () {
                                  AppBased.showDialogYesNo(
                                    context,
                                    msgContent: l10n.cancelDeliveryMsg,
                                    title: l10n.confirmation,
                                    yesText: l10n.yes,
                                    noText: l10n.no,
                                    yesTap: () {
                                      context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
                                    },
                                  );
                                },
                                label: l10n.cancelDelivery,
                                fontSize: 16,
                                buttonBackgroundColor: const Color(0xFFFF4E64),
                                labelColor: Colors.white,
                                borderRadius: 12,
                                isAllowPress: true,
                                isLoading: state.status == NewDeliveryStatus.Loading,
                              ),
                            if (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name) ...[
                              const SizedBox(width: 8),
                              ButtonLoading(
                                height: 48,
                                callback: () {
                                  context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name);
                                },
                                label: l10n.markInProgress,
                                fontSize: 16,
                                buttonBackgroundColor: const Color(0xFF444444),
                                labelColor: Colors.white,
                                borderRadius: 12,
                                isAllowPress: true,
                                isLoading: context.read<OrderChangeStatusCubit>().state.status == OrderChangeStatusStatus.Loading,
                              ),
                            ],
                          ],
                        ),
                      )
                    : Container(
                        padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
                        decoration: const BoxDecoration(
                          border: Border(
                            top: BorderSide(
                              width: 1,
                              color: AppColors.appBorderColor,
                            ),
                          ),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name) ...[
                              ButtonLoading(
                                height: 48,
                                callback: () {
                                  context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name);
                                },
                                label: l10n.markInProgress,
                                fontSize: 16,
                                buttonBackgroundColor: const Color(0xFF444444),
                                labelColor: Colors.white,
                                borderRadius: 12,
                                isAllowPress: true,
                                // isLoading: state.status == NewDeliveryStatus.Loading,
                                isLoading: context.read<OrderChangeStatusCubit>().state.status == OrderChangeStatusStatus.Loading,
                              ),
                              const SizedBox(height: 8),
                            ],
                            if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name)
                              ButtonLoading(
                                height: 48,
                                callback: () {
                                  // context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
                                  AppBased.showDialogYesNo(
                                    context,
                                    msgContent: l10n.cancelDeliveryMsg,
                                    title: l10n.confirmation,
                                    yesText: l10n.yes,
                                    noText: l10n.no,
                                    yesTap: () {
                                      context.read<NewDeliveryCubit>().onCancelDelivery(oid: _order?.id ?? '');
                                    },
                                  );
                                },
                                label: l10n.cancelDelivery,
                                fontSize: 16,
                                buttonBackgroundColor: const Color(0xFFFF4E64),
                                labelColor: Colors.white,
                                borderRadius: 12,
                                isAllowPress: true,
                                isLoading: state.status == NewDeliveryStatus.Loading,
                              ),
                          ],
                        ),
                      );
              },
            );
            return newDeliveryButton;
          }

          if (isDelivery && _order?.status?.toUpperCase() == OrderStatusDefine.READY.name) {
            return const SizedBox.shrink();
          }

          final issueWithOrderButton = (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name || _order?.status?.toUpperCase() == OrderStatusDefine.PREPARING.name || _order?.status?.toUpperCase() == OrderStatusDefine.SCHEDULED.name)
              ? SizedBox(
                  height: isFontMobile ? 48 : 56,
                  child: OutlinedButton(
                    onPressed: () => OrderDialogs.showIssueWithOrder(context, _order ?? Order(), allowRefund: allowRefund),
                    style: ButtonStyle(
                      padding: WidgetStateProperty.all(EdgeInsets.symmetric(horizontal: isTablet ? 16 : 2)),
                      side: WidgetStateProperty.all(const BorderSide(width: 1, color: AppColors.iconColor)),
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    child: Text(
                      l10n.issueWithOrder,
                      style: TextStyle(
                        color: AppColors.appBlackColor,
                        fontSize: isFontMobile ? 18 : 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                )
              : null;

          final printerButton = SizedBox(
            height: isFontMobile ? 48 : 56,
            child: OutlinedButton(
              onPressed: () {
                showPrintReceiptDialog(
                  context,
                  l10n,
                  order: _order ?? Order(),
                  printersReceipt: printersReceipt,
                  printersKitchenTicket: printersKitchenTicket,
                  epsonPrinterKitchen: epsonPrinterKitchen,
                  epsonPrinterReceipt: epsonPrinterReceipt,
                  starPrinterKitchen: starPrinterKitchen,
                  starPrinterReceipt: starPrinterReceipt,
                  timeZone: timeZone,
                );
              },
              style: ButtonStyle(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(
                    horizontal: isTablet ? 16 : 2,
                  ),
                ),
                side: WidgetStateProperty.all(
                  const BorderSide(
                    width: 1,
                    color: AppColors.iconColor,
                  ),
                ),
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/svgs/print.svg',
                    width: 24,
                    height: 24,
                  ),
                  if (!isFontMobile) ...{
                    const SizedBox(width: 8),
                    Text(
                      l10n.print,
                      style: TextStyle(
                        color: AppColors.appBlackColor,
                        fontSize: isFontMobile ? 18 : 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  },
                ],
              ),
            ),
          );

          // Widget cho các nút trạng thái (isNewScheduled hoặc !isNewScheduled)
          final statusButtons = BlocBuilder<OrderChangeStatusCubit, OrderChangeStatusState>(
            builder: (context, state) {
              final isPayAtStore = !hasPaid(_order?.payments ?? []);

              final isNewScheduled = _order?.scheduledAt != null && _order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name;
              if (_getButtonText(context) != '' && _getButtonText(context) != null) {
                return Row(
                  mainAxisAlignment: isTablet ? MainAxisAlignment.end : MainAxisAlignment.spaceBetween,
                  children: [
                    if (!isNewScheduled) ...{
                      if (isTablet) ...[
                        if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name && _order?.status?.toUpperCase() != OrderStatusDefine.READY.name) ...[
                          if (isPayAtStore) ...[
                            OrderManagePaymentButtonWidget(order: _order)
                          ] else ...[
                            if (isHidenMarkCompletedDelivery) _buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state),
                          ],
                          SizedBox(width: isTablet ? 16 : 8),
                        ],
                        if (isPayAtStore && _order?.status?.toUpperCase() == OrderStatusDefine.READY.name) ...{
                          OrderManagePaymentButtonWidget(order: _order),
                        } else
                          _buildIsuenButton(l10n, isTablet, isFontMobile, state),
                      ] else
                        Expanded(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name && _order?.status?.toUpperCase() != OrderStatusDefine.READY.name) ...[
                                SizedBox(height: isTablet ? 16 : 8),
                                if (isPayAtStore) ...[
                                  OrderManagePaymentButtonWidget(order: _order)
                                ] else ...[
                                  if (isHidenMarkCompletedDelivery) _buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state),
                                ],
                                SizedBox(height: isTablet ? 16 : 8),
                              ],
                              if (isPayAtStore) ...{
                                // _buildActionPaymentButton(l10n, isTablet, isFontMobile, state),
                                OrderManagePaymentButtonWidget(order: _order)
                              } else
                                _buildIsuenButton(l10n, isTablet, isFontMobile, state),
                            ],
                          ),
                        ),
                    } else ...{
                      if (isTablet) ...[
                        if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name && _order?.status?.toUpperCase() != OrderStatusDefine.READY.name) ...[
                          Padding(
                            padding: EdgeInsets.only(left: isTablet ? 16 : 8),
                            child: isPayAtStore
                                // ? _buildActionPaymentButton(l10n, isTablet, isFontMobile, state)
                                ? OrderManagePaymentButtonWidget(order: _order)
                                : isHidenMarkCompletedDelivery
                                    ? _buildActionMarkCompletedButton(
                                        l10n,
                                        isTablet,
                                        isFontMobile,
                                        state,
                                      )
                                    : null,
                          ),
                        ],
                        Padding(
                          padding: EdgeInsets.only(left: isTablet ? 16 : 8),
                          child: _buildActionButton(l10n, isTablet, isFontMobile, state),
                        ),
                        SizedBox(width: isTablet ? 16 : 8),
                        _buildRemindMeButton(l10n, isTablet, isFontMobile, state)
                      ] else ...[
                        Expanded(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name && _order?.status?.toUpperCase() != OrderStatusDefine.READY.name) ...[
                                SizedBox(height: isTablet ? 16 : 8),
                                if (isPayAtStore) ...[
                                  // _buildActionPaymentButton(l10n, isTablet, isFontMobile, state),
                                  OrderManagePaymentButtonWidget(order: _order)
                                ] else ...[
                                  if (isHidenMarkCompletedDelivery) _buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state),
                                ],
                              ],
                              SizedBox(height: isTablet ? 16 : 8),
                              _buildActionButton(l10n, isTablet, isFontMobile, state),
                              SizedBox(height: isTablet ? 16 : 8),
                              _buildRemindMeButton(l10n, isTablet, isFontMobile, state),
                            ],
                          ),
                        ),
                      ]
                    }
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          );
          final checkTabletSmall = MediaQuery.of(context).size.width < 520;
          final isNewScheduled = _order?.scheduledAt != null && _order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name;
          if (_order?.status?.toUpperCase() == OrderStatusDefine.CANCELLED.name) {
            return const SizedBox.shrink();
          }

          return Container(
            padding: const EdgeInsets.fromLTRB(0, 8, 0, 12),
            margin: EdgeInsets.only(bottom: (Platform.isIOS || Platform.isMacOS) ? 6 : 0),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(width: 1, color: AppColors.appBorderColor)),
              color: AppColors.lightPrimaryBackgroundColor,
            ),
            child: isTablet
                ? SingleChildScrollView(
                    primary: true,
                    physics: const ClampingScrollPhysics(),
                    scrollDirection: checkTabletSmall && isNewScheduled ? Axis.horizontal : Axis.vertical,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0, right: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          if (issueWithOrderButton != null) ...[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: printerButton,
                                ),
                                issueWithOrderButton,
                              ],
                            ),
                          ],
                          const SizedBox(width: 8),
                          statusButtons,
                        ],
                      ),
                    ),
                  )
                : _buildButtonActionMobile(context, issueWithOrderButton: issueWithOrderButton, isTablet: isTablet, isFontMobile: isFontMobile, printerButton: printerButton),
          );
        },
      ),
      body: BlocConsumer<OrderChangeStatusCubit, OrderChangeStatusState>(
        listener: (context, state) async {
          switch (state.status) {
            case OrderChangeStatusStatus.Error:
              if (state.errorMsg != null && state.errorMsg != '') {
                AppBased.toastError(context, title: state.errorMsg);
                BlocProvider.of<OrderChangeStatusCubit>(context).onResetStatus();
                await OrderDetailCubitRegistry.refreshOrder(_order?.id ?? '');
              }
              return;
            case OrderChangeStatusStatus.Success:
              final order = state.order;
              if (order != null) {
                if (order.status != null && order.status?.toUpperCase() == OrderStatusDefine.COMPLETED.name) {
                  try {
                    Future.delayed(const Duration(milliseconds: 500), () {
                      try {
                        Navigator.pop(context);
                      } catch (e) {}
                    });
                  } catch (e) {}
                }
              }
              return;
            default:
          }
        },
        builder: (context, stateChangeStatus) {
          final bool isCompleted = (_order?.status?.toUpperCase() == OrderStatusDefine.COMPLETED.name) && stateChangeStatus.status != OrderChangeStatusStatus.Initial;

          return PopScope(
            canPop: (timeAddOrder == 0 && !isCompleted) ? true : false,
            onPopInvokedWithResult: (value, f) {
              return;
            },
            child: BlocBuilder<AuthBloc, AuthState>(
              builder: (context, stateAuth) {
                final currencyCode = stateAuth.currencyBusiness ?? 'USD';
                return Container(
                  color: AppColors.appBlackColor,
                  padding: EdgeInsets.only(top: statusBarHeight != 0 ? 0 : 24),
                  child: Column(
                    children: [
                      _buildHeader(context, stateAuth, _order),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          color: AppColors.anouncementBGColor,
                          child: _buildResponse(
                            l10n,
                            isTablet,
                            currencyCode: currencyCode,
                            stateAuth: stateAuth,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildPayAtStore(
    String title,
    double? borderRadius, {
    Color colorBG = AppColors.orderingProductTypeColor,
    Color colorTitle = AppColors.lightPrimaryBackgroundColor,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: colorBG,
          borderRadius: BorderRadius.circular(borderRadius ?? 0),
          border: Border.all(
            width: 1,
            color: colorBG,
          )),
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: TextStyle(
          color: colorTitle,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildButtonActionMobile(
    BuildContext context, {
    Widget? issueWithOrderButton,
    bool isTablet = false,
    bool isFontMobile = false,
    Widget? printerButton,
  }) {
    final l10n = context.l10n;

    final List<Widget> items = [];

    if (issueWithOrderButton != null) {
      items.add(issueWithOrderButton);
    }
    final bool isDelivery = _order?.type?.toUpperCase() == OrderType.DELIVERY.name;

    final isHidenMarkCompletedDelivery = (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name && isDelivery || _order?.status?.toUpperCase() == OrderStatusDefine.PREPARING.name) ? false : true;

    return BlocConsumer<OrderChangeStatusCubit, OrderChangeStatusState>(
      listener: (context, state) {
        switch (state.status) {
          case OrderChangeStatusStatus.Error:
            if (state.errorMsg != null && state.errorMsg != '') {
              AppBased.toastError(context, title: state.errorMsg);
              BlocProvider.of<OrderChangeStatusCubit>(context).onResetStatus();
              OrderDetailCubitRegistry.refreshOrder(_order?.id ?? '');
            }
            return;
          case OrderChangeStatusStatus.Success:
            return;
          default:
        }
      },
      builder: (context, state) {
        final isNewScheduled = _order?.scheduledAt != null && _order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name;

        final isPayAtStore = !hasPaid(_order?.payments ?? []);

        if (_getButtonText(context) != '' && _getButtonText(context) != null) {
          if (!isNewScheduled) {
            if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name) {
              if (isPayAtStore) {
                items.add(OrderManagePaymentButtonWidget(order: _order));
                // items.add(_buildActionPaymentButton(l10n, isTablet, isFontMobile, state));
              } else {
                if (isHidenMarkCompletedDelivery) items.add(_buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state));
              }
            }

            if (isPayAtStore) {
              items.add(OrderManagePaymentButtonWidget(order: _order));
              // items.add(_buildActionPaymentButton(l10n, isTablet, isFontMobile, state));
            } else {
              items.add(_buildIsuenButton(l10n, isTablet, isFontMobile, state));
            }

            // items.add(_buildIsuenButton(l10n, isTablet, isFontMobile, state));
          } else {
            if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name) {
              if (isPayAtStore) {
                items.add(OrderManagePaymentButtonWidget(order: _order));
                // items.add(_buildActionPaymentButton(l10n, isTablet, isFontMobile, state));
              } else {
                if (isHidenMarkCompletedDelivery) items.add(_buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state));
              }
            }

            items
              ..add(_buildActionButton(l10n, isTablet, isFontMobile, state))
              ..add(_buildRemindMeButton(l10n, isTablet, isFontMobile, state));
          }
        }
        return Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: printerButton ?? const SizedBox.shrink(),
                  ),
                  if (issueWithOrderButton != null) ...{
                    Expanded(
                      flex: 1,
                      child: issueWithOrderButton,
                    )
                  },
                  if (_order?.status?.toUpperCase() != OrderStatusDefine.COMPLETED.name && _order?.status?.toUpperCase() != OrderStatusDefine.CANCELLED.name && _order?.status?.toUpperCase() != OrderStatusDefine.READY.name) ...{
                    if (!isHidenMarkCompletedDelivery)
                      ...[]
                    else ...[
                      const SizedBox(width: 8),
                      Expanded(
                        flex: 2,
                        child: isPayAtStore ? OrderManagePaymentButtonWidget(order: _order) : _buildActionMarkCompletedButton(l10n, isTablet, isFontMobile, state),
                      )
                    ]
                  }
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (!isNewScheduled) ...{
                      Expanded(
                        child: isPayAtStore && _order?.status?.toUpperCase() == OrderStatusDefine.READY.name ? OrderManagePaymentButtonWidget(order: _order) : _buildIsuenButton(l10n, isTablet, isFontMobile, state),
                      )
                    } else ...{
                      Expanded(
                        child: _buildActionButton(l10n, isTablet, isFontMobile, state),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildRemindMeButton(l10n, isTablet, isFontMobile, state),
                      )
                    }
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIsuenButton(
    AppLocalizations l10n,
    bool isTablet,
    bool isFontMobile,
    OrderChangeStatusState state,
  ) {
    return ButtonLoading(
      height: _getButtonText(context) == ''
          ? 0
          : isFontMobile
              ? 48
              : 56,
      borderRadius: 12,
      padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 8),
      isAllowPress: state.status != OrderChangeStatusStatus.Loading,
      isLoading: state.status == OrderChangeStatusStatus.Loading,
      iconDirection: TextDirection.rtl,
      textDirection: TextDirection.rtl,
      label: _getButtonText(context),
      widget: (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name && _order?.type?.toUpperCase() == OrderType.DELIVERY.name)
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  l10n.markInProgress,
                  style: TextStyle(
                    fontSize: isFontMobile ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.lightPrimaryBackgroundColor,
                  ),
                ),
                Text(
                  l10n.markInProgressDispatchDriver,
                  textDirection: TextDirection.ltr,
                  style: TextStyle(
                    fontSize: isFontMobile ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.lightPrimaryBackgroundColor,
                  ),
                )
              ],
            )
          : null,
      fontSize: isFontMobile ? 18 : 20,
      circularLoadingSize: 24,
      fontWeight: FontWeight.w600,
      labelColor: AppColors.lightPrimaryBackgroundColor,
      circularStrokeColor: AppColors.appColor,
      marginLoadingIcon: EdgeInsets.zero,
      labelColorDisable: AppColors.appBlackColor,
      buttonBackgroundColor: AppColors.orderMarkBG,
      callback: () {
        doUndoStatus(onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: status));
      },
    );
  }

  Widget _buildActionButton(
    AppLocalizations l10n,
    bool isTablet,
    bool isFontMobile,
    OrderChangeStatusState state,
  ) {
    return ButtonLoading(
      height: isFontMobile ? 48 : 56,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.iconColor, width: 1),
      ),
      borderRadius: 12,
      padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 8),
      isAllowPress: state.status != OrderChangeStatusStatus.Loading,
      isLoading: state.status == OrderChangeStatusStatus.Loading,
      iconDirection: TextDirection.rtl,
      textDirection: TextDirection.rtl,
      label: _getButtonText(context),
      widget: (_order?.status?.toUpperCase() == OrderStatusDefine.CONFIRMED.name && _order?.type?.toUpperCase() == OrderType.DELIVERY.name)
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  l10n.markInProgress,
                  style: TextStyle(
                    fontSize: isFontMobile ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.lightPrimaryBackgroundColor,
                  ),
                ),
                Text(
                  l10n.markInProgressDispatchDriver,
                  textDirection: TextDirection.ltr,
                  style: TextStyle(
                    fontSize: isFontMobile ? 16 : 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.lightPrimaryBackgroundColor,
                  ),
                )
              ],
            )
          : null,
      fontSize: isFontMobile ? 18 : 20,
      circularLoadingSize: 24,
      fontWeight: FontWeight.w600,
      labelColor: AppColors.lightPrimaryBackgroundColor,
      circularStrokeColor: AppColors.appColor,
      marginLoadingIcon: EdgeInsets.zero,
      labelColorDisable: AppColors.lightPrimaryBackgroundColor,
      buttonBackgroundColor: AppColors.orderMarkBG,
      callback: () {
        if (checkTimeSchedule(_order?.scheduledAt)) {
          doUndoStatus(statusChange: OrderStatusDefine.PREPARING.name, onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name));
        } else {
          onShowCustomerTags(context, onChanged: (value) {
            if (value == true) {
              doUndoStatus(statusChange: OrderStatusDefine.PREPARING.name, onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.PREPARING.name));
            } else {}
          });
        }
      },
    );
  }

  Widget _buildActionMarkCompletedButton(
    AppLocalizations l10n,
    bool isTablet,
    bool isFontMobile,
    OrderChangeStatusState state,
  ) {
    return ButtonLoading(
      height: isFontMobile ? 48 : 56,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.iconColor, width: 1),
      ),
      borderRadius: 12,
      padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 2),
      isAllowPress: state.status != OrderChangeStatusStatus.LoadingCompleted,
      isLoading: state.status == OrderChangeStatusStatus.LoadingCompleted,
      iconDirection: TextDirection.rtl,
      textDirection: TextDirection.rtl,
      label: l10n.markCompleted,
      fontSize: isFontMobile ? 18 : 20,
      circularLoadingSize: 24,
      fontWeight: FontWeight.w600,
      labelColor: AppColors.appBlackColor,
      circularStrokeColor: AppColors.appColor,
      marginLoadingIcon: EdgeInsets.zero,
      labelColorDisable: AppColors.appBlackColor,
      buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
      widget: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            l10n.markCompleted,
            style: TextStyle(
              fontSize: isFontMobile ? 17 : 19,
              fontWeight: FontWeight.w600,
              color: AppColors.appBlackColor,
              height: 1.2,
            ),
          ),
          Text(
            l10n.skipNotifyingCustomers,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.appBlackColor,
              height: 1,
            ),
          )
        ],
      ),
      callback: () {
        doUndoStatus(
          statusOldChange: _order?.status,
          statusChange: OrderStatusDefine.COMPLETED.name,
          onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderCompletedStatus(id: _order?.id ?? '', statusOrder: status),
        );
      },
    );
  }

  Widget _buildRemindMeButton(AppLocalizations l10n, bool isTablet, bool isFontMobile, OrderChangeStatusState state) {
    return ButtonLoading(
      height: isFontMobile ? 48 : 56,
      borderRadius: 12,
      padding: EdgeInsets.symmetric(horizontal: isTablet ? 16 : 8),
      isAllowPress: state.status != OrderChangeStatusStatus.LoadingSheduled,
      isLoading: state.status == OrderChangeStatusStatus.LoadingSheduled,
      iconDirection: TextDirection.rtl,
      textDirection: TextDirection.rtl,
      label: l10n.remindMe,
      fontSize: isFontMobile ? 18 : 20,
      circularLoadingSize: 24,
      fontWeight: FontWeight.w600,
      labelColor: AppColors.lightPrimaryBackgroundColor,
      circularStrokeColor: AppColors.appColor,
      marginLoadingIcon: EdgeInsets.zero,
      labelColorDisable: AppColors.lightPrimaryBackgroundColor,
      buttonBackgroundColor: AppColors.orderScheduledBGColor,
      callback: () {
        doUndoStatus(onCompleted: (status) => context.read<OrderChangeStatusCubit>().doOrderChangeStatus(id: _order?.id ?? '', statusOrder: OrderStatusDefine.SCHEDULED.name));
      },
    );
  }

  Widget _buildResponse(
    AppLocalizations l10n,
    bool isTablet, {
    required String currencyCode,
    required AuthState stateAuth,
  }) {
    final isNewDelivery = widget.order?.isDeliveryExpress != null && widget.order?.isDeliveryExpress == true;

    if (isTablet) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              color: AppColors.lightPrimaryBackgroundColor,
              child: _buildItemsList(context, _order, currencyCode),
            ),
          ),
          SizedBox(
            width: 320,
            height: double.infinity,
            child: _buildRightInfo(context, stateAuth, isTablet, _order),
          ),
        ],
      );
    } else {
      // Giao diện trên mobile:
      return Container(
        color: AppColors.lightPrimaryBackgroundColor,
        child: RefreshIndicator(
          color: AppColors.appColor,
          onRefresh: () => OrderDetailCubitRegistry.refreshOrder(_order?.id ?? ''),
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(parent: ClampingScrollPhysics()),
            slivers: [
              SliverToBoxAdapter(
                child: _buildRightInfoContent(context, stateAuth, isTablet, _order),
              ),
              // Danh sách mặt hàng
              _buildItemsListSliver(context, _order, currencyCode),
              if (isNewDelivery) ...[
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.fromLTRB(32, 16, 32, 32),
                        alignment: Alignment.center,
                        child: SvgPicture.asset('assets/svgs/delivery-box.svg'),
                      ),
                      _buildDivider(height: 8),
                    ],
                  ),
                ),
              ],
              // Tổng tiền
              SliverToBoxAdapter(
                child: _buildItemsTotal(context),
              ),
              if (!isTablet) ...{
                SliverToBoxAdapter(
                  child: _buildDivider(),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                    margin: const EdgeInsets.only(bottom: 24),
                    child: AbsorbPointer(
                      absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
                      child: _buildUserOrder(context, _order),
                    ),
                  ),
                )
              },
            ],
          ),
        ),
      );
    }
  }

  String detectTimeSchedule(AppLocalizations l10n, DateTime? time) {
    final timeSchedule = DateTimeHelper.currentTime(timeZone, time: time);
    final currentTime = DateTimeHelper.currentTime(timeZone);
    final difference = timeSchedule.difference(currentTime);
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;
    return '${days > 0 ? '$days ' : ''}${days == 1 ? l10n.days(' ') : days > 1 ? l10n.days('s ') : ''}${hours > 0 ? ' $hours ' : ''}${hours == 1 ? l10n.hours(' ') : hours > 1 ? l10n.hours('s ') : ''}${minutes > 0 ? ' $minutes ' : ''}${minutes == 1 ? l10n.minutes('') : minutes > 1 ? l10n.minutes('s') : ''}'
        .trim();
  }

  bool checkTimeSchedule(DateTime? time) {
    final timeSchedule = DateTimeHelper.currentTime(timeZone, time: time);
    final currentTime = DateTimeHelper.currentTime(timeZone);
    final difference = timeSchedule.difference(currentTime);
    final minutes = difference.inMinutes;
    return minutes <= 15;
  }

  void onShowCustomerTags(
    BuildContext context, {
    ValueChanged? onChanged,
  }) {
    final l10n = context.l10n;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          backgroundColor: AppColors.lightPrimaryBackgroundColor,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 330),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset('assets/svgs/order-warning.svg'),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text(
                          l10n.confirmation,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                MarkdownBody(
                  data: l10n.confirmScheduleProgress(detectTimeSchedule(l10n, _order?.scheduledAt)),
                  styleSheet: MarkdownStyleSheet(
                    p: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: AppColors.homeShowQRBGColor,
                    ),
                  ),
                  onTapLink: (text, href, title) {},
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(
                      child: TextButton(
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.all(16),
                          backgroundColor: AppColors.lightPrimaryBackgroundColor,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                              side: const BorderSide(
                                width: 1,
                                color: AppColors.appBorderColor,
                              )),
                        ),
                        onPressed: () {
                          onChanged!.call(false);
                          Navigator.pop(context);
                        },
                        child: Text(
                          l10n.noRemindMe,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.appBlackColor,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.fromLTRB(42, 16, 42, 16),
                        backgroundColor: AppColors.orderMarkBG,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      onPressed: () {
                        onChanged!.call(true);
                        Navigator.pop(context);
                      },
                      child: Text(
                        l10n.yes,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.lightPrimaryBackgroundColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).then((onValue) {});
  }

  Widget _buildHeader(BuildContext context, AuthState stateAuth, Order? order) {
    final l10n = context.l10n;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(vertical: isTablet ? 12 : 8, horizontal: 16),
      decoration: BoxDecoration(
        color: _getStatusColor(order),
      ),
      child: isTablet
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _buildClose(),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildHeaderNameStatus(l10n, stateAuth, order, isTablet),
                      ),
                    ],
                  ),
                ),
                if ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name) ...{
                  AbsorbPointer(
                    absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
                    child: _buildTimePickup(l10n, stateAuth),
                  ),
                } else if (_order?.status == OrderStatusDefine.READY.name) ...{
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _detectTimeAutoTitle(context, l10n, order, stateAuth),
                        textAlign: TextAlign.start,
                        style: TextStyle(
                          color: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .8),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  )
                }
              ],
            )
          : BlocBuilder<OrderActiveTimePickupTabCubit, OrderActiveTimePickupState>(
              builder: (context, stateActiveTimePickup) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildClose(),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildHeaderNameStatus(l10n, stateAuth, order, isTablet),
                              ),
                            ],
                          ),
                        ),
                        if (((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name) && stateActiveTimePickup.tab != OrderActiveTimePickupStatus.TimePickup) ...{
                          InkWell(
                            onTap: () {
                              context.read<OrderActiveTimePickupTabCubit>().onChangedTab();
                            },
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  _detectTimeAutoTitle(context, l10n, order, stateAuth),
                                  style: TextStyle(
                                    color: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .8),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name)
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(right: 4.0),
                                        child: Text(
                                          _formatTime(l10n),
                                          style: const TextStyle(
                                            color: AppColors.lightPrimaryBackgroundColor,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        'assets/svgs/expand-up-down.svg',
                                      )
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        }
                      ],
                    ),
                    if ((_order?.status == OrderStatusDefine.CONFIRMED.name && _order?.scheduledAt == null) || _order?.status == OrderStatusDefine.PREPARING.name) ...{
                      if (stateActiveTimePickup.tab == OrderActiveTimePickupStatus.TimePickup) ...{
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(top: 8, bottom: 8),
                              height: 1,
                              color: AppColors.appBorderColor,
                            ),
                            AbsorbPointer(
                              absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
                              child: Padding(
                                padding: const EdgeInsets.only(top: 4.0),
                                child: _buildTimePickup(l10n, stateAuth),
                              ),
                            ),
                          ],
                        )
                      }
                    },
                  ],
                );
              },
            ),
    );
  }

  String _detectTimeAutoTitle(BuildContext context, AppLocalizations l10n, Order? order, AuthState stateAuth, {bool isCompleted = false}) {
    final status = order?.status?.toUpperCase();
    final orderAutoConfirm = stateAuth.business?.orderAutoConfirm ?? false;
    final orderAutoReady = stateAuth.business?.orderAutoReady ?? false;

    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return '';
      }
      return orderAutoConfirm ? l10n.autoConfirm : l10n.readyIn;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return orderAutoReady ? l10n.autoReady : l10n.readyIn;
    } else if (status == OrderStatusDefine.READY.name) {
      return '';
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return '';
    }
    return '';
  }

  bool isAutoCompleted(
    BuildContext context,
    AppLocalizations l10n,
    Order? order,
    AuthState stateAuth,
  ) {
    final status = order?.status?.toUpperCase();
    final orderAutoComplete = stateAuth.business?.orderAutoComplete ?? false;
    if (status == OrderStatusDefine.READY.name && orderAutoComplete && hasPaid(_order?.payments ?? [])) return true;
    return false;
  }

  int formatHHMMFromDateTime(DateTime formatTime) {
    return (formatTime.hour * 100) + formatTime.minute;
  }

  String formatHHMM(dynamic input, {bool is24H = true}) {
    String hhmm;
    if (input is int) {
      if (input < 0) throw const FormatException('Negative number not allowed');
      hhmm = input.toString().padLeft(4, '0');
    } else if (input is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(input)) {
        throw const FormatException('Input string must be 1 to 4 digits (HHMM)');
      }
      hhmm = input.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));

    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0–23), minute (0–59)');
    }

    if (is24H) {
      final String h = hour.toString().padLeft(2, '0');
      final String m = minute.toString().padLeft(2, '0');
      return '$h:$m';
    } else {
      final isPM = hour >= 12;
      final hour12 = hour % 12 == 0 ? 12 : hour % 12;
      final m = minute.toString().padLeft(2, '0');
      final suffix = isPM ? 'PM' : 'AM';
      return '$hour12:$m $suffix';
    }
  }

  String formatUtcToLocal(String utcTimeString) {
    try {
      DateTime utcDateTime;

      try {
        utcDateTime = DateTime.parse('${utcTimeString}Z');
      } catch (e) {
        try {
          if (utcTimeString.endsWith('Z') || utcTimeString.contains('+00:00')) {
            utcDateTime = DateTime.parse(utcTimeString);
          } else {
            utcDateTime = DateTime.parse(utcTimeString).toUtc();
          }
        } catch (e2) {
          final RegExp pattern = RegExp(r'(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?');
          final match = pattern.firstMatch(utcTimeString);

          if (match != null) {
            final year = int.parse(match.group(1)!);
            final month = int.parse(match.group(2)!);
            final day = int.parse(match.group(3)!);
            final hour = int.parse(match.group(4)!);
            final minute = int.parse(match.group(5)!);
            final second = int.parse(match.group(6)!);
            final millisecond = match.group(7) != null ? int.parse(match.group(7)!) : 0;

            utcDateTime = DateTime.utc(year, month, day, hour, minute, second, millisecond);
          } else {
            throw Exception('Không thể phân tích chuỗi thời gian: $utcTimeString');
          }
        }
      }

      // Chuyển sang múi giờ địa phương
      final localDateTime = utcDateTime.toLocal();

      return '${localDateTime.year}-${_padZero(localDateTime.month)}-${_padZero(localDateTime.day)} '
          '${_padZero(localDateTime.hour)}:${_padZero(localDateTime.minute)}:${_padZero(localDateTime.second)}';
    } catch (e) {
      return utcTimeString;
    }
  }

  String _padZero(int number) {
    return number.toString().padLeft(2, '0');
  }

  DateTime parseHHMMToToday(dynamic hhmmInput) {
    String hhmm;
    if (hhmmInput is int) {
      if (hhmmInput < 0) {
        throw const FormatException('Input must be a positive integer');
      }
      hhmm = hhmmInput.toString().padLeft(4, '0');
    } else if (hhmmInput is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(hhmmInput)) {
        throw const FormatException('String must be 1 to 4 digits in HHMM format');
      }
      hhmm = hhmmInput.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0-23), minute (0-59)');
    }

    final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59);
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  Widget _buildClose() {
    return BlocBuilder<OrderChangeStatusCubit, OrderChangeStatusState>(
      builder: (context, state) {
        return IconButton(
          splashRadius: 16,
          style: IconButton.styleFrom(
            backgroundColor: AppColors.lightPrimaryBackgroundColor,
            disabledBackgroundColor: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .3),
            shape: const CircleBorder(),
          ),
          icon: SvgPicture.asset(
            'assets/svgs/close-black.svg',
            colorFilter: ColorFilter.mode(
              timeAddOrder == 0 ? AppColors.appBlackColor : AppColors.appBlackColor.withValues(alpha: .3),
              BlendMode.srcIn,
            ),
          ),
          onPressed: timeAddOrder != 0
              ? null
              : () {
                  final bool isCompleted = (_order?.status?.toUpperCase() == OrderStatusDefine.COMPLETED.name) && state.status != OrderChangeStatusStatus.Initial;
                  if (timeAddOrder == 0 && !isCompleted) {
                    Navigator.pop(context);
                  } else {
                    if (!canPop) {
                      canPop = true;
                      timeAddOrder = 0;
                      Future.delayed(const Duration(seconds: 2), () {
                        setState(() {});
                      });
                    } else {
                      canPop = false;
                      Navigator.pop(context);
                    }
                  }
                },
        );
      },
    );
  }

  Widget _buildHeaderNameStatus(
    AppLocalizations l10n,
    AuthState stateAuth,
    Order? order,
    bool isTablet,
  ) {
    final CustomerOrder? customer = order?.customer;

    final isActive = customer?.isActive ?? true;
    final customerName = (order?.customerName != '' && order?.customerName != null)
        ? order?.customerName
        : (order?.customer?.displayName != '' && order?.customer?.displayName != null)
            ? order?.customer?.displayName
            : l10n.guest;
    final isFromPOS = order?.isFromPOS ?? false;
    return AbsorbPointer(
      absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  '${_getStatusText(context)} • #${order?.orderNumber}',
                  style: TextStyle(
                    color: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .9),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                  Padding(
                    padding: const EdgeInsets.only(left: 4.0),
                    child: SvgPicture.asset(
                      'assets/svgs/delivery-white.svg',
                      colorFilter: const ColorFilter.mode(
                        AppColors.lightPrimaryBackgroundColor,
                        BlendMode.srcIn,
                      ),
                    ),
                  )
              ],
            ),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  isFromPOS
                      ? (customerName ?? l10n.guest)
                      : isActive
                          ? (customerName ?? l10n.guest)
                          : l10n.guestDelete,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    color: AppColors.lightPrimaryBackgroundColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (isTablet)
                  Container(
                    margin: const EdgeInsets.only(left: 4),
                    child: _buildCustomerSegment(order?.customerId ?? ''),
                  )
              ],
            ),
            if (!isTablet) _buildCustomerSegment(order?.customerId ?? '')
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSegment(String uid) {
    return BlocBuilder<CustomerSegmentCubit, CustomerSegmentState>(
      builder: (context, state) {
        if (state.status == CustomerSegmentStatus.Success) {
          final typeByID = state.selectedCustomer(uid)?.type;
          if (typeByID != null) {
            return SegmentTargetOffersWidget(
              title: typeByID,
              uid: uid,
              titleColor: AppColors.lightPrimaryBackgroundColor,
              margin: const EdgeInsets.only(top: 2),
            );
          }
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildTimePickup(AppLocalizations l10n, AuthState stateAuth) {
    final checkPrepEndTime = _isLessThanOneMinute();

    return BlocConsumer<OrderChangePrepTimeCubit, OrderChangePrepTimeState>(
      listener: (context, state) {
        timeAddOrder = 0;
        switch (state.status) {
          case OrderChangePrepTimeStatus.Error:
            if (state.errorMsg != null && state.errorMsg != '') {
              AppBased.toastError(context, title: state.errorMsg);
              BlocProvider.of<OrderChangeStatusCubit>(context).onResetStatus();
              setState(() {});
            }
            return;
          case OrderChangePrepTimeStatus.AddTime:
            context.read<OrderChangePrepTimeCubit>().doOrderChangePrepTime(id: _order?.id, time: state.time ?? 0);
            return;
          case OrderChangePrepTimeStatus.Success:
            context.read<OrdersActiveCubit>().onUpdateOrders(_order);
            setState(() {});
            return;
          default:
        }
      },
      builder: (context, state) {
        return Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              splashRadius: 16,
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(checkPrepEndTime ? AppColors.lightPrimaryBackgroundColor.withValues(alpha: .2) : AppColors.lightPrimaryBackgroundColor),
                shape: WidgetStateProperty.all(const CircleBorder()),
              ),
              disabledColor: checkPrepEndTime ? AppColors.lightPrimaryBackgroundColor.withValues(alpha: .2) : AppColors.lightPrimaryBackgroundColor,
              icon: Container(
                width: 35,
                height: 35,
                alignment: Alignment.center,
                child: const Text(
                  '-5',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              isSelected: false,
              onPressed: checkPrepEndTime
                  ? null
                  : () {
                      timeAddOrder += -5;
                      final prepEndTime = _order?.prepEndTime;
                      _order?.prepEndTime = prepEndTime?.add(const Duration(minutes: -5));
                      int minute = getMinutesDiff(_order?.prepEndTime);
                      if (minute < 0) {
                        _order?.prepEndTime = DateTimeHelper.currentTime(timeZone);
                        minute = 0;
                      }
                      preEndTime(time: timeAddOrder);
                    },
            ),
            const SizedBox(width: 16),
            InkWell(
              onTap: () => context.read<OrderActiveTimePickupTabCubit>().onChangedTab(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    _detectTimeAutoTitle(context, l10n, _order, stateAuth),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.lightPrimaryBackgroundColor.withValues(alpha: .8),
                    ),
                  ),
                  Text(
                    _formatTime(l10n),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.lightPrimaryBackgroundColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            IconButton(
              splashRadius: 16,
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(AppColors.lightPrimaryBackgroundColor),
                shape: WidgetStateProperty.all(const CircleBorder()),
              ),
              icon: Container(
                width: 35,
                height: 35,
                alignment: Alignment.center,
                child: const Text(
                  '+5',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              onPressed: () {
                timeAddOrder += 5;
                final prepEndTime = _order?.prepEndTime;
                _order?.prepEndTime = prepEndTime?.add(const Duration(minutes: 5));
                final minute = getMinutesDiff(_order?.prepEndTime);
                if (minute < 0) {
                  _order?.prepEndTime = prepEndTime?.add(Duration(minutes: minute.abs() + 10));
                }
                preEndTime(time: timeAddOrder);

                // _doDebounce(
                //   milliseconds: 1000,
                //   onChange: () {
                //     context.read<OrderChangePrepTimeCubit>().doOrderChangePrepTime(id: _order?.id, time: timeAddOrder);
                //     timeAddOrder = 0;
                //   },
                // );
                // _startTimer();
              },
            ),
          ],
        );
      },
    );
  }

  void preEndTime({int? time}) {
    try {
      _doDebounce(
        milliseconds: 1000,
        onChange: () {
          context.read<OrderChangePrepTimeCubit>().onChangedTime(timeAddOrder);

          timeAddOrder = 0;
        },
      );
      _startTimer();
    } catch (e) {}
  }

  // Sử dụng cho tablet
  Widget _buildItemsList(BuildContext context, Order? order, String currencyCode) {
    final isNewDelivery = widget.order?.isDeliveryExpress != null && widget.order?.isDeliveryExpress == true;
    return RefreshIndicator(
      color: AppColors.appColor,
      onRefresh: () => OrderDetailCubitRegistry.refreshOrder(_order?.id ?? ''),
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(parent: ClampingScrollPhysics()),
        slivers: [
          if (isNewDelivery) ...[
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.fromLTRB(32, 16, 32, 32),
                    alignment: Alignment.center,
                    child: SvgPicture.asset('assets/svgs/delivery-box.svg'),
                  ),
                  _buildDivider(height: 8),
                ],
              ),
            ),
          ],
          _buildItemsListSliver(context, order, currencyCode),
          SliverFillRemaining(
            hasScrollBody: false,
            child: _buildItemsTotal(context),
          ),
        ],
      ),
    );
  }

  // Tách phần danh sách mặt hàng thành một Sliver để sử dụng trên mobile
  SliverList _buildItemsListSliver(
    BuildContext context,
    Order? order,
    String currencyCode,
  ) {
    final l10n = context.l10n;
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (BuildContext context, int index) {
          final item = order?.items?[index];
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 40,
                      child: Text(
                        '${item?.quantity}x',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlackColor,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: Text(
                                    item?.title ?? '',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.appBlackColor,
                                    ),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 6.0),
                                child: Text(
                                  item?.isVoucherFreeItem == true ? l10n.free : CurrencyHelper.convertMoney((item?.subtotal ?? 0).toString(), code: order?.currencyCode ?? currencyCode),
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.appBlackColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (item?.notes != null)
                            Container(
                              margin: EdgeInsets.only(bottom: item?.modifiers != null && item!.modifiers!.isNotEmpty ? 0 : 16),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppColors.specialRequirementsColor.withValues(alpha: .1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                item?.notes ?? '',
                                style: const TextStyle(
                                  color: AppColors.specialRequirementsColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          if (item?.modifiers != null && item!.modifiers!.isNotEmpty)
                            ListView.builder(
                              shrinkWrap: true,
                              itemCount: groupModifierOrders(item.modifiers ?? []).length,
                              physics: const NeverScrollableScrollPhysics(),
                              padding: EdgeInsets.zero,
                              itemBuilder: (context, index) {
                                final itemData = groupModifierOrders(item.modifiers ?? []).elementAt(index);
                                final isBorder = itemData.id != groupModifierOrders(item.modifiers ?? []).last.id;
                                final bool isItemLast = itemData.id == groupModifierOrders(item.modifiers ?? []).last.id;
                                return DecoratedBox(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color: isBorder ? AppColors.homeBorderColor : AppColors.appTransparentColor,
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  child: ListTile(
                                    contentPadding: EdgeInsets.fromLTRB(0, 8, 0, isItemLast ? 0 : 8),
                                    horizontalTitleGap: 0,
                                    minVerticalPadding: 0,
                                    minTileHeight: 0,
                                    subtitle: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${itemData.modifier?.name}',
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: AppColors.appBlackColor.withValues(alpha: .5),
                                          ),
                                        ),
                                        ListView.builder(
                                          shrinkWrap: true,
                                          itemCount: itemData.options?.length,
                                          physics: const NeverScrollableScrollPhysics(),
                                          padding: EdgeInsets.only(bottom: isItemLast ? 12 : 0),
                                          itemBuilder: (context, index) {
                                            final option = itemData.options?[index];
                                            final textItemPrice = (option?.price ?? 0) > 0.0 ? CurrencyHelper.convertMoney((option?.price ?? 0).toString(), code: _order?.currencyCode ?? currencyCode) : null;

                                            return Padding(
                                              padding: const EdgeInsets.only(top: 4.0),
                                              child: Text(
                                                '${option?.name}${textItemPrice != null ? ' ($textItemPrice)' : ''}',
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                style: const TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors.appBlackColor,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),

                          // ListView.builder(
                          //   shrinkWrap: true,
                          //   itemCount: groupModifierOrders().length,
                          //   physics: const NeverScrollableScrollPhysics(),
                          //   padding: EdgeInsets.zero,
                          //   itemBuilder: (context, index) {
                          //     final itemData = groupModifierOrders().elementAt(index);
                          //     final isBorder = itemData != groupModifierOrders().last;

                          //     final textItemPrice = (itemData.price ?? 0) != 0.0 && (itemData.price ?? 0) != 0 ? CurrencyHelper.convertMoney((itemData.price ?? 0).toString(), code: _order?.currencyCode ?? currencyCode) : null;

                          //     return DecoratedBox(
                          //       decoration: BoxDecoration(
                          //         border: Border(
                          //           bottom: BorderSide(
                          //             color: isBorder ? AppColors.homeBorderColor : AppColors.appTransparentColor,
                          //             width: 1,
                          //           ),
                          //         ),
                          //       ),
                          //       child: ListTile(
                          //         contentPadding: const EdgeInsets.fromLTRB(0, 12, 0, 12),
                          //         horizontalTitleGap: 0,
                          //         minVerticalPadding: 0,
                          //         minTileHeight: 0,
                          //         subtitle: Column(
                          //           mainAxisSize: MainAxisSize.min,
                          //           mainAxisAlignment: MainAxisAlignment.start,
                          //           crossAxisAlignment: CrossAxisAlignment.start,
                          //           children: [
                          //             Text(
                          //               '${itemData?.modifier?.name}',
                          //               maxLines: 2,
                          //               overflow: TextOverflow.ellipsis,
                          //               style: TextStyle(
                          //                 fontSize: 14,
                          //                 fontWeight: FontWeight.w600,
                          //                 color: AppColors.appBlackColor.withValues(alpha: .6),
                          //               ),
                          //             ),
                          //             Text(
                          //               '${itemData.option?.name}${textItemPrice != null ? ' ($textItemPrice)' : ''}',
                          //               maxLines: 2,
                          //               overflow: TextOverflow.ellipsis,
                          //               style: const TextStyle(
                          //                 fontSize: 14,
                          //                 fontWeight: FontWeight.w500,
                          //                 color: AppColors.appBlackColor,
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ),
                          //     );
                          //   },
                          // )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              _buildDivider(),
            ],
          );
        },
        childCount: order?.items?.length,
      ),
    );
  }

  Widget _buildDivider({double height = 8}) {
    return Container(
      height: height,
      color: AppColors.appBGAvatarColor,
    );
  }

  List<ModifierOrder> groupModifierOrders(List<ModifierOrder> modifiers) {
    final Map<String, ModifierOrder> groupedModifiers = {};

    for (final modifier in modifiers) {
      if (modifier != null && modifier.modifier != null) {
        final modifierId = modifier.modifier!.id ?? '';

        if (!groupedModifiers.containsKey(modifierId)) {
          groupedModifiers[modifierId] = ModifierOrder(
            id: modifierId,
            modifier: modifier.modifier,
            options: [],
          );
        }
        final existingModifier = groupedModifiers[modifierId]!;
        final existingOptions = List<ModifierOption>.from(existingModifier.options ?? []);

        if (modifier.option != null) {
          final optionWithPrice = ModifierOption(
            id: modifier.option!.id,
            name: modifier.option!.name,
            price: modifier.price,
            currencyCode: modifier.option!.currencyCode,
            isUnavailable: modifier.option!.isUnavailable,
            unAvailableUntil: modifier.option!.unAvailableUntil,
          );
          existingOptions.add(optionWithPrice);
        }
        groupedModifiers[modifierId] = existingModifier.copyWith(
          options: existingOptions,
        );
      }
    }

    return groupedModifiers.values.toList();
  }

  int getTotalQuantity() {
    final items = _order?.items ?? [];
    return items.fold(0, (sum, item) => sum + (item.quantity ?? 0));
  }

  Widget _buildItemsTotal(BuildContext context) {
    final l10n = context.l10n;

    final currencyBusiness = _order?.currencyCode ?? context.read<AuthBloc>().state.currencyBusiness ?? 'USD';
    final discountTotal = num.parse((_order?.discountTotal ?? 0).toString()) > 0 ? CurrencyHelper.convertMoney('${_order?.discountTotal ?? 0}', code: currencyBusiness) : null;
    final isNewDelivery = widget.order?.isDeliveryExpress != null && widget.order?.isDeliveryExpress == true;
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        final currencyCode = state.currencyBusiness;
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isNewDelivery) ...[
                Text(
                  l10n.lengthTotalItems(getTotalQuantity().toString(), getTotalQuantity() > 1 ? 'S' : ''),
                  style: const TextStyle(
                    color: AppColors.appBlackColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
              if (!isNewDelivery)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.subtotal,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.subtotal ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              if (!isNewDelivery && (_order?.taxTotal ?? 0) > 0) ...[
                const Divider(
                  height: 8,
                  color: AppColors.appBorderColor,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.tax,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.taxTotal ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name && (_order?.deliveryFee ?? 0) >= 0) ...[
                if (!isNewDelivery)
                  const Divider(
                    height: 8,
                    color: AppColors.appBorderColor,
                  ),
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.deliveryFee,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.deliveryFee ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              // if (isNewDelivery && _order?.tipAmount != null && _order?.tipAmount != 0)
              //   Padding(
              //     padding: const EdgeInsets.only(top: 4, bottom: 8),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         Text(
              //           l10n.tip,
              //           style: const TextStyle(
              //             color: AppColors.appBlackColor,
              //             fontWeight: FontWeight.w600,
              //             fontSize: 16,
              //           ),
              //         ),
              //         Text(
              //           CurrencyHelper.convertMoney((_order?.tipAmount ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
              //           style: const TextStyle(
              //             color: AppColors.appBlackColor,
              //             fontWeight: FontWeight.w600,
              //             fontSize: 16,
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              if (_order?.discounts != null && _order!.discounts!.isNotEmpty) ...{
                const Divider(
                  height: 8,
                  color: AppColors.appBorderColor,
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0, top: 8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              l10n.discounts,
                              style: const TextStyle(
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                          if (discountTotal != null && discountTotal != '') ...[
                            const SizedBox(width: 16),
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                '-$discountTotal',
                                style: const TextStyle(
                                  color: AppColors.appResendColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      for (final DiscountOrder discount in _order?.discounts ?? []) ...{
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ' • ',
                                style: TextStyle(
                                  color: AppColors.appBlackColor.withValues(alpha: .56),
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  discount.rewardName ?? discount.voucherName ?? '',
                                  style: TextStyle(
                                    color: AppColors.appBlackColor.withValues(alpha: .56),
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      }
                    ],
                  ),
                ),
              },
              if (_order?.tipAmount != null && _order?.tipAmount != 0) ...[
                const Divider(
                  height: 8,
                  color: AppColors.appBorderColor,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 4, bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        l10n.tip,
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        CurrencyHelper.convertMoney((_order?.tipAmount ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                        style: const TextStyle(
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const Divider(
                height: 8,
                color: AppColors.appBorderColor,
              ),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      l10n.total,
                      style: const TextStyle(
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 20,
                      ),
                    ),
                    Text(
                      CurrencyHelper.convertMoney((_order?.total ?? 0).toString(), code: _order?.currencyCode ?? currencyCode ?? 'USD'),
                      style: const TextStyle(
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String getNameDiscount(List<DiscountOrder> discounts) {
    if (discounts.isEmpty) return '';
    return discounts.map((discount) => discount.rewardName ?? '').join(', ');
  }

  Widget _buildContentHeader(
    AppLocalizations l10n,
    String iconName,
    String title, {
    Color colorTitle = AppColors.appBlackColor,
  }) {
    return Row(
      children: [
        SvgPicture.asset('assets/svgs/$iconName'),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              color: colorTitle,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  String _pickupAt(Order? order) {
    if (order?.status == OrderStatusDefine.SCHEDULED.name || (order?.status == OrderStatusDefine.CONFIRMED.name && order?.scheduledAt != null)) {
      // return DateTimeHelper.dateTimeFormatMDY(2222
      //   order?.scheduledAt ?? DateTime.now(),
      //   timeZone: timeZone,
      // );
      return DateTimeHelper.dateTimeLongFormat(
        _order?.scheduledAt,
        timeZone: timeZone,
        isWeekDay: false,
        langLocale: Localizations.localeOf(context).languageCode,
      );
    }
    return DateTimeHelper.timeFormat(context, order?.prepEndTime, timeZone: timeZone);
  }

  // Sử dụng cho tablet
  Widget _buildRightInfo(BuildContext context, AuthState stateAuth, bool isTable, Order? order) {
    return CustomScrollView(
      primary: true,
      physics: const AlwaysScrollableScrollPhysics(
        parent: ClampingScrollPhysics(),
      ),
      slivers: [
        SliverFillRemaining(
          hasScrollBody: false,
          child: _buildRightInfoContent(context, stateAuth, isTable, order),
        ),
      ],
    );
  }

  Widget _buildRightInfoContent(BuildContext context, AuthState stateAuth, bool isTablet, Order? order) {
    final l10n = context.l10n;
    final isPayAtStore = !hasPaid(_order?.payments ?? []);
    final orderAllowPayAtStore = context.read<AuthBloc>().state.business?.orderAllowPayAtStore ?? false;
    final isShowAddNewDelivery = stateAuth.business?.enableOrderDeliveryFeature == true && stateAuth.business?.orderAllowDelivery == true;
    final isNewDelivery = widget.order?.isDeliveryExpress ?? false;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isNewDelivery && orderAllowPayAtStore) ...[
          Container(
            color: AppColors.lightPrimaryBackgroundColor,
            padding: const EdgeInsets.only(top: 4, bottom: 4),
            child: _buildPayAtStore(
              isPayAtStore ? l10n.payAtStore.toUpperCase() : l10n.paid.toUpperCase(),
              0,
              colorBG: isPayAtStore ? AppColors.orderUnpaidBGColor : AppColors.orderPaidBGColor,
              colorTitle: isPayAtStore ? AppColors.orderUnpaidColor : AppColors.orderPaidColor,
            ),
          ),
        ],
        _buildDivider(height: isTablet ? 16 : 8),
        if (order?.type?.toUpperCase() == OrderType.DELIVERY.name) ...[
          BoxOrderDeliveryWidget(order: order, isShipper: true),
        ],
        Container(
          padding: isTablet ? const EdgeInsets.fromLTRB(16, 0, 16, 16) : EdgeInsets.zero,
          decoration: isTablet ? const BoxDecoration(color: AppColors.appBGAvatarColor) : null,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isShowAddNewDelivery && isTablet) _buildNewOrderDelivery(context, l10n, order),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: isTablet
                    ? BoxDecoration(
                        color: AppColors.lightPrimaryBackgroundColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          width: 1,
                          color: AppColors.appBorderColor,
                        ),
                      )
                    : null,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_order?.status == OrderStatusDefine.SCHEDULED.name || (_order?.status == OrderStatusDefine.CONFIRMED.name || _order?.status == OrderStatusDefine.PREPARING.name)) ...{
                      _buildContentHeader(l10n, 'clock.svg', l10n.pickupAt(_pickupAt(order))),
                      const SizedBox(height: 8),
                    },
                    if (!isNewDelivery) _buildContentHeader(l10n, 'order-cart.svg', l10n.itemsOrder(getTotalQuantity(), getTotalQuantity() > 1 ? 's' : '')),
                    if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name)
                      Container(
                        margin: EdgeInsets.only(top: _order?.status == OrderStatusDefine.READY.name || _order?.status == OrderStatusDefine.CANCELLED.name ? 0 : 12),
                        padding: EdgeInsets.only(top: _order?.status == OrderStatusDefine.READY.name || _order?.status == OrderStatusDefine.CANCELLED.name ? 0 : 12),
                        decoration: BoxDecoration(
                          border: _order?.status != OrderStatusDefine.READY.name && _order?.status != OrderStatusDefine.CANCELLED.name
                              ? const Border(
                                  top: BorderSide(
                                    width: 1,
                                    color: AppColors.appBorderColor,
                                  ),
                                )
                              : null,
                        ),
                        child: _buildContentHeader(l10n, 'delivery-black.svg', l10n.delivery),
                      ),
                  ],
                ),
              ),
              if (isTablet)
                AbsorbPointer(
                  absorbing: !stateAuth.checkPermissions(PermissionBusiness.orders_active_orders.name, [2, 3]),
                  child: _buildUserOrder(context, order),
                )
              else
                _buildDivider()
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNewOrderDelivery(BuildContext context, AppLocalizations l10n, Order? order) {
    if (_order?.type?.toUpperCase() == OrderType.DELIVERY.name) {
      return const SizedBox.shrink();
    }
    return InkWell(
      onTap: () {
        // AppLog.d('stateAuth == ${jsonEncode(stateAuth.business)}');
        // AppLog.e('order == ${jsonEncode(order)}');
        AppBased.go(context, AppRoutes.addNewDeliveryPage, args: ScreenArguments(data: _order));
      },
      child: Container(
        width: double.infinity,
        height: 48,
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 16),
        decoration: BoxDecoration(
          color: AppColors.lightPrimaryBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            width: 1,
            color: AppColors.appResendColor,
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/svgs/add.svg',
              colorFilter: const ColorFilter.mode(
                AppColors.appBlackColor,
                BlendMode.srcIn,
              ),
              width: 20,
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                l10n.newDelivery,
                style: const TextStyle(
                  color: AppColors.appBlackColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserOrder(BuildContext context, Order? order) {
    final l10n = context.l10n;
    final CustomerOrder? customer = order?.customer;
    final isActive = customer?.isActive ?? true;
    final customerName = (order?.customerName != '' && order?.customerName != null)
        ? order?.customerName
        : (order?.customer?.displayName != '' && order?.customer?.displayName != null)
            ? order?.customer?.displayName
            : l10n.guest;
    final isFromPOS = order?.isFromPOS ?? false;

    final Search user = Search(
      userId: customer?.id,
      phoneNumber: customer?.phone,
      displayName: customer?.displayName,
      avatar: customer?.avatar,
      businessId: order?.businessId,
      // segmentType: segmentType,
      user: User(
        id: customer?.id,
        displayName: customer?.displayName,
        email: order?.customer?.email,
        phone: customer?.phone,
        isActive: isActive,
      ),
    );

    final avatarLink = customer?.avatar?.publicId != null ? '${AppBased.appEnv.cdnUrl}${customer?.avatar?.publicId}' : customer?.avatar?.file;
    final bool isTablet = MediaQuery.of(context).size.width >= 600;
    final auth = context.read<AuthBloc>().state;
    final isShowAddNewDelivery = auth.business?.enableOrderDeliveryFeature == true && auth.business?.orderAllowDelivery == true;
    final isDeliveryExpress = widget.order?.isDeliveryExpress ?? false;
    final deliveryExpressCustomerName = widget.order?.customerName ?? widget.order?.deliveryAddress?.name ?? l10n.guest;
    final deliveryExpressPhone = widget.order?.phone ?? widget.order?.deliveryAddress?.phone ?? '';

    return BlocBuilder<CustomerSegmentCubit, CustomerSegmentState>(
      builder: (context, stateSegment) {
        final segmentType = stateSegment.selectedCustomer(customer?.id)?.type;
        user.segmentType = segmentType;
        return Container(
          margin: const EdgeInsets.only(top: 16),
          child: Column(
            children: [
              if (!isTablet && isShowAddNewDelivery) _buildNewOrderDelivery(context, l10n, order),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.lightPrimaryBackgroundColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    width: 1,
                    color: AppColors.appBorderColor,
                  ),
                ),
                padding: const EdgeInsets.only(bottom: 16, top: 16),
                child: Column(
                  children: [
                    InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: isDeliveryExpress ? null : () => isActive && customer?.id != '' && customer?.id != null ? _onShowInfo(context, user) : null,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16.0),
                            child: AvatarControlWidget(
                              width: 48,
                              height: 48,
                              // name: order?.customer?.displayName ?? order?.customerName ?? l10n.guest,
                              name: customerName ?? l10n.guest,
                              urlImage: avatarLink,
                              borderColor: AppColors.appBGAvatarColor,
                              borderRadius: 50,
                              backgroundColor: AppColors.appBGAvatarColor,
                            ),
                          ),
                          Expanded(
                            child: Container(
                              margin: const EdgeInsets.only(left: 12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (isDeliveryExpress)
                                    Text(
                                      deliveryExpressCustomerName,
                                      style: const TextStyle(
                                        color: AppColors.appBlackColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    )
                                  else
                                    RichText(
                                      textAlign: TextAlign.left,
                                      softWrap: true,
                                      text: TextSpan(
                                        text: isFromPOS
                                            ? (customerName ?? l10n.guest)
                                            : isActive
                                                ? (customerName ?? l10n.guest)
                                                : l10n.guestDelete,
                                        style: const TextStyle(
                                          color: AppColors.appBlackColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                        ),
                                        children: [
                                          if (isActive && customer?.id != null && customer?.id != '')
                                            WidgetSpan(
                                              child: Container(
                                                margin: const EdgeInsets.only(left: 2),
                                                width: 24,
                                                height: 24,
                                                child: IconButton(
                                                  padding: EdgeInsets.zero,
                                                  onPressed: () {
                                                    final data = {'user': user};
                                                    AppBased.go(
                                                      context,
                                                      AppRoutes.redeemPoint,
                                                      args: ScreenArguments(
                                                        data: data,
                                                        type: 'activities-page',
                                                      ),
                                                      onChanged: (data) {},
                                                    );
                                                  },
                                                  icon: SvgPicture.asset(
                                                    'assets/svgs/open-link.svg',
                                                    colorFilter: const ColorFilter.mode(
                                                      AppColors.appResendColor,
                                                      BlendMode.srcIn,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  if (isDeliveryExpress && deliveryExpressPhone != '') ...[
                                    RichText(
                                      textAlign: TextAlign.left,
                                      softWrap: true,
                                      text: TextSpan(
                                        text: AppValidations.formatPhoneNumber(deliveryExpressPhone),
                                        style: TextStyle(
                                          color: AppColors.appBlackColor.withValues(alpha: .56),
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        children: [
                                          WidgetSpan(
                                            child: Container(
                                              margin: const EdgeInsets.only(left: 2),
                                              width: 24,
                                              height: 24,
                                              alignment: Alignment.center,
                                              child: IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  makePhoneCall(order?.customer?.phone ?? order?.phone ?? '');
                                                },
                                                icon: SvgPicture.asset(
                                                  'assets/svgs/delivery-call.svg',
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ] else if (order?.customer?.phone != null || order?.phone != null)
                                    RichText(
                                      textAlign: TextAlign.left,
                                      softWrap: true,
                                      text: TextSpan(
                                        text: AppValidations.formatPhoneNumber(order?.customer?.phone ?? order?.phone ?? ''),
                                        style: TextStyle(
                                          color: AppColors.appBlackColor.withValues(alpha: .56),
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        children: [
                                          WidgetSpan(
                                            child: Container(
                                              margin: const EdgeInsets.only(left: 2),
                                              width: 24,
                                              height: 24,
                                              alignment: Alignment.center,
                                              child: IconButton(
                                                padding: EdgeInsets.zero,
                                                onPressed: () {
                                                  makePhoneCall(order?.customer?.phone ?? order?.phone ?? '');
                                                },
                                                icon: SvgPicture.asset(
                                                  'assets/svgs/delivery-call.svg',
                                                  // colorFilter: const ColorFilter.mode(
                                                  //   AppColors.appResendColor,
                                                  //   BlendMode.srcIn,
                                                  // ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  if (order?.email != null && order?.email != '')
                                    Text(
                                      order?.email ?? '',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.appBlackColor.withValues(alpha: .56),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 16, bottom: 16),
                      child: const Divider(
                        height: 1,
                        color: AppColors.appBorderColor,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                // width: 65,
                                margin: const EdgeInsets.only(right: 16),
                                child: Text(
                                  l10n.orderName,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.homeShowQRBGColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: Text(
                                  '#${_order?.orderNumber ?? ''}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.homeShowQRBGColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            child: Row(
                              children: [
                                Container(
                                  // width: 65,
                                  margin: const EdgeInsets.only(right: 16),
                                  child: Text(
                                    l10n.orderDate,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.homeShowQRBGColor,
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 3,
                                  child: Text(
                                    DateTimeHelper.dateTimeLongFormat(
                                      _order?.createdAt,
                                      timeZone: timeZone,
                                      isWeekDay: false,
                                      langLocale: Localizations.localeOf(context).languageCode,
                                    ),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.homeShowQRBGColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _onShowInfo(BuildContext context, Search? user) {
    showDialog<dynamic>(
      context: context,
      useRootNavigator: false,
      builder: (BuildContext context) {
        return Dialog(
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.sizeOf(context).height * .8,
                ),
                color: AppColors.lightPrimaryBackgroundColor,
                child: OrderActiveCustomerInfoWidget(user: user),
              ),
            ),
          ),
        );
      },
    ).then((onValue) {});
  }

  String _detectStatusOrder(String? status) {
    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return OrderStatusDefine.SCHEDULED.name;
      }
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return OrderStatusDefine.READY.name;
    } else if (status == OrderStatusDefine.READY.name) {
      return OrderStatusDefine.COMPLETED.name;
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.CANCELLED.name) {
      return OrderStatusDefine.CANCELLED.name;
    } else {
      return '';
    }
  }

  String _detectUndoStatusOrder(String? status) {
    if (status == OrderStatusDefine.CONFIRMED.name) {
      if (_order?.scheduledAt != null) {
        return OrderStatusDefine.SCHEDULED.name;
      }
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.PREPARING.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else if (status == OrderStatusDefine.READY.name) {
      return OrderStatusDefine.PREPARING.name;
    } else if (status == OrderStatusDefine.SCHEDULED.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else if (status == OrderStatusDefine.COMPLETED.name) {
      return OrderStatusDefine.READY.name;
    } else if (status == OrderStatusDefine.CANCELLED.name) {
      return OrderStatusDefine.CONFIRMED.name;
    } else {
      return '';
    }
  }

  String _getButtonText(BuildContext context) {
    final l10n = context.l10n;
    switch (_order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        return l10n.markInProgress;
      case 'PREPARING':
        return l10n.markReady;
      case 'READY':
        return l10n.markCompleted;
      case 'SCHEDULED':
        return l10n.markInProgress;
      default:
        return '';
    }
  }

  Color _getStatusColor(Order? order) {
    switch (_order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        if (_order?.scheduledAt != null) {
          return AppColors.orderScheduledBGColor;
        } else {
          return AppColors.orderNewBGColor;
        }
      case 'PREPARING':
        return AppColors.orderInProgressBGColor;
      case 'READY':
        return AppColors.orderReadyBGColor;
      case 'SCHEDULED':
        return AppColors.orderScheduledBGColor;
      case 'CANCELLED':
        return AppColors.orderCancelledBGColor;
      case 'COMPLETED':
        return AppColors.orderCompletedBGColor;
      case 'REFUNDED':
        return AppColors.orderRefundedBGColor;
      default:
        return AppColors.orderNewBGColor;
    }
  }

  String _getStatusText(BuildContext context, {String? status}) {
    final l10n = context.l10n;
    switch (status ?? _order?.status?.toUpperCase()) {
      case 'CONFIRMED':
        if (_order?.scheduledAt != null) {
          return l10n.newScheduleOrder;
        }
        return l10n.newOrder;
      case 'PREPARING':
        return l10n.inProgressOrder;
      case 'READY':
        return l10n.readyOrder;
      case 'SCHEDULED':
        return l10n.scheduledOrder;
      case 'CANCELLED':
        return l10n.cancelledStatus;
      case 'COMPLETED':
        return l10n.completed;
      case 'REFUNDED':
        return l10n.refunded;
      default:
        return '';
    }
  }

  void showUndoSnackbar(BuildContext context, String msg, {int timeUndo = 2, VoidCallback? onUndo, VoidCallback? onActionComplete}) {
    final l10n = context.l10n;
    bool isUndoPressed = false;
    bool isCompleted = false;
    OverlayEntry? overlayEntry;

    final snackBar = SnackBar(
      behavior: SnackBarBehavior.floating,
      dismissDirection: DismissDirection.none,
      content: Row(
        children: [
          Expanded(
            child: MarkdownBody(
              data: msg,
              styleSheet: MarkdownStyleSheet(
                p: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.lightPrimaryBackgroundColor,
                ),
              ),
              onTapLink: (text, href, title) {},
            ),
          ),
          TextButton(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.fromLTRB(42, 16, 42, 16),
              backgroundColor: AppColors.orderMarkBG,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            onPressed: () {
              try {
                onUndo?.call();
                overlayEntry?.remove();
                overlayEntry = null;
                isCompleted = true;
                isUndoPressed = true;
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              } catch (e) {}
            },
            child: Text(
              l10n.undo,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.lightPrimaryBackgroundColor,
              ),
            ),
          ),
        ],
      ),
      duration: Duration(seconds: timeUndo),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 0,
        left: 0,
        right: 0,
        bottom: kBottomNavigationBarHeight + 48,
        child: IgnorePointer(
          ignoring: false,
          child: GestureDetector(
            onTap: () {},
            behavior: HitTestBehavior.opaque,
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ),
    );
    try {
      Overlay.of(context).insert(overlayEntry!);
      Timer(Duration(seconds: timeUndo), () {
        try {
          if (overlayEntry != null) {
            overlayEntry?.remove();
            overlayEntry = null;
            if (!isUndoPressed && !isCompleted) {
              isCompleted = true;
              onActionComplete?.call();
            }
          }
        } catch (e) {}
      });
    } catch (e) {}
  }

  static Future<bool> makePhoneCall(String phoneNumber) async {
    try {
      final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        return true;
      } else {
        throw 'Could not launch $phoneUri';
      }
    } catch (e) {
      print('Error making phone call: $e');
      return false;
    }
  }

  void showPrintReceiptDialog(
    BuildContext context,
    AppLocalizations l10n, {
    required Order order,
    required List<Printer> printersReceipt,
    required List<Printer> printersKitchenTicket,
    required EpsonPrinterReceipt epsonPrinterReceipt,
    required EpsonPrinterKitchenTicket epsonPrinterKitchen,
    required StarPrinterReceipt starPrinterReceipt,
    required StarPrinterKitchen starPrinterKitchen,
    required String timeZone,
  }) {
    final l10n = context.l10n;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          // actionsPadding: EdgeInsets.zero,
          // iconPadding: EdgeInsets.zero,
          // insetPadding: EdgeInsets.zero,
          // buttonPadding: EdgeInsets.zero,
          titlePadding: EdgeInsets.zero,
          title: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: AppBar(
              elevation: 1,
              scrolledUnderElevation: 1,
              shadowColor: Colors.grey.withValues(alpha: .2),
              title: Text(
                l10n.options,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
              automaticallyImplyLeading: false,
              centerTitle: true,
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 6),
                  child: IconButton(
                    icon: const Icon(
                      Icons.close,
                      size: 26,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ],
            ),
          ),
          content: Container(
            width: double.maxFinite,
            constraints: const BoxConstraints(maxHeight: 500, maxWidth: 300),
            child: ListView(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              children: [
                Divider(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
                ListTile(
                  title: Text(
                    l10n.kitchenTicketOnly,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  onTap: () {
                    onSelectedPrint(
                      typePrint: 'kitchen',
                      l10n: l10n,
                      order: order,
                      timeZone: timeZone,
                      printersKitchen: printersKitchenTicket,
                      printersReceipt: printersReceipt,
                      epsonPrinterKitchen: epsonPrinterKitchen,
                      epsonPrinterReceipt: epsonPrinterReceipt,
                      starPrinterKitchen: starPrinterKitchen,
                      starPrinterReceipt: starPrinterReceipt,
                    );
                    Navigator.pop(context, 'Cancel');
                  },
                ),
                Divider(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
                ListTile(
                  title: Text(
                    l10n.receiptOnly,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  onTap: () {
                    onSelectedPrint(
                      typePrint: 'receipt',
                      l10n: l10n,
                      order: order,
                      timeZone: timeZone,
                      printersKitchen: printersKitchenTicket,
                      printersReceipt: printersReceipt,
                      epsonPrinterKitchen: epsonPrinterKitchen,
                      epsonPrinterReceipt: epsonPrinterReceipt,
                      starPrinterKitchen: starPrinterKitchen,
                      starPrinterReceipt: starPrinterReceipt,
                    );
                    Navigator.pop(context, 'Cancel');
                  },
                ),
                Divider(
                  height: 1,
                  color: Colors.grey.shade300,
                ),
                ListTile(
                  title: Text(
                    l10n.both,
                    style: const TextStyle(
                      color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  onTap: () {
                    onSelectedPrint(
                      typePrint: 'both',
                      l10n: l10n,
                      order: order,
                      timeZone: timeZone,
                      printersKitchen: printersKitchenTicket,
                      printersReceipt: printersReceipt,
                      epsonPrinterKitchen: epsonPrinterKitchen,
                      epsonPrinterReceipt: epsonPrinterReceipt,
                      starPrinterKitchen: starPrinterKitchen,
                      starPrinterReceipt: starPrinterReceipt,
                    );
                    Navigator.pop(context, 'In cả 2');
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> onSelectedPrint({
    required String typePrint,
    required AppLocalizations l10n,
    required Order order,
    required String timeZone,
    required List<Printer> printersKitchen,
    required List<Printer> printersReceipt,
    required EpsonPrinterKitchenTicket epsonPrinterKitchen,
    required EpsonPrinterReceipt epsonPrinterReceipt,
    required StarPrinterReceipt starPrinterReceipt,
    required StarPrinterKitchen starPrinterKitchen,
  }) async {
    switch (typePrint) {
      case 'both':
        await onPrintCustomerReceipt(
          l10n: l10n,
          order: order,
          printersReceipt: printersReceipt,
          epsonPrinterReceipt: epsonPrinterReceipt,
          starPrinterReceipt: starPrinterReceipt,
        );
        await Future.delayed(const Duration(milliseconds: 1000));
        await onPrintKitchenTicket(
          l10n: l10n,
          order: order,
          timeZone: timeZone,
          printersKitchen: printersKitchen,
          epsonPrinterKitchen: epsonPrinterKitchen,
          starPrinterKitchen: starPrinterKitchen,
        );
        break;
      case 'kitchen':
        await onPrintKitchenTicket(
          l10n: l10n,
          order: order,
          timeZone: timeZone,
          printersKitchen: printersKitchen,
          epsonPrinterKitchen: epsonPrinterKitchen,
          starPrinterKitchen: starPrinterKitchen,
        );
        break;
      case 'receipt':
        await onPrintCustomerReceipt(
          l10n: l10n,
          order: order,
          printersReceipt: printersReceipt,
          epsonPrinterReceipt: epsonPrinterReceipt,
          starPrinterReceipt: starPrinterReceipt,
        );
        break;
      default:
    }
  }

  Future<void> onPrintCustomerReceipt({
    required AppLocalizations l10n,
    required Order order,
    required List<Printer> printersReceipt,
    required EpsonPrinterReceipt epsonPrinterReceipt,
    required StarPrinterReceipt starPrinterReceipt,
  }) async {
    if (printersReceipt.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    } else {
      for (final printer in printersReceipt) {
        final ip = printer.address ?? '';
        // if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
        if (ip.isEmpty) continue;

        final receiptCopies = printer.receiptCopies ?? 1;
        final numberOfCopies = receiptCopies > 0 ? receiptCopies : 1;

        AppLog.d('RECEIPT Printing $numberOfCopies copies for printer ${printer.address} with mode ${printer.mode}');

        // In theo số lượng receiptCopies
        for (int copyIndex = 1; copyIndex <= numberOfCopies; copyIndex++) {
          try {
            if (printer.mode == 'ESC_POS') {
              await epsonPrinterReceipt.printCustomerReceiptAsText(
                context: context,
                ipAddress: printer.address ?? '',
                order: order,
                pageSize: printer.pageSize ?? '80mm',
              );
            } else if (printer.mode == 'STAR_LINE') {
              await starPrinterReceipt.printReceipt(
                context: context,
                l10n: l10n,
                ipAddress: printer.address ?? '',
                order: order,
              );
            } else {
              // Xử lý trường hợp mode không được hỗ trợ
              AppLog.e('RECEIPT Unsupported printer mode: ${printer.mode} for printer ${printer.address}');
              continue; // Bỏ qua máy in này và chuyển sang máy in tiếp theo
            }

            AppLog.d('RECEIPT Printed copy $copyIndex/$numberOfCopies for printer ${printer.address}');

            // Thêm delay nhỏ giữa các lần in để tránh quá tải máy in
            if (copyIndex < numberOfCopies) {
              await Future.delayed(const Duration(milliseconds: 500));
            }
          } catch (e) {
            AppLog.e('RECEIPT Error printing copy $copyIndex for printer ${printer.address}: $e');
            break; // Dừng in các bản sao còn lại nếu có lỗi
            // continue; // Tiếp tục in bản sao tiếp theo dù có lỗi
          }
        }
      }
    }
  }

  Future<void> onPrintKitchenTicket({
    required AppLocalizations l10n,
    required Order order,
    required String timeZone,
    required List<Printer> printersKitchen,
    required EpsonPrinterKitchenTicket epsonPrinterKitchen,
    required StarPrinterKitchen starPrinterKitchen,
  }) async {
    if (printersKitchen.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    } else {
      final authUser = context.read<AuthBloc>().state.user;
      for (final printer in printersKitchen) {
        final ip = printer.address ?? '';
        // if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
        if (ip.isEmpty) continue;

        // Kiểm tra kitchenCategories
        final kitchenCategories = printer.kitchenCategories ?? <String>[];

        Order orderToPrint;

        if (kitchenCategories.isEmpty) {
          // Nếu kitchenCategories rỗng thì in toàn bộ order gốc
          AppLog.d('TICKET Printer ${printer.address} has empty kitchenCategories, printing full order...');
          orderToPrint = order;
        } else {
          // Nếu kitchenCategories không rỗng thì tạo filtered order
          final filteredOrder = _createFilteredOrder(order, kitchenCategories);

          // Nếu không có item nào được phép in thì bỏ qua máy in này
          if (filteredOrder == null || _isOrderEmpty(filteredOrder)) {
            AppLog.d('TICKET No items to print for printer ${printer.address}');
            continue;
          }
          orderToPrint = filteredOrder;
        }

        AppLog.e('orderToPrint == ${jsonEncode(orderToPrint)}');

        try {
          if (printer.mode == 'ESC_POS') {
            await epsonPrinterKitchen.printKitchenTicketAsText(
              l10n: l10n,
              timezone: timeZone,
              ipAddress: printer.address ?? '',
              order: orderToPrint,
              printerName: printer.productName ?? printer.name ?? '',
              authUser: authUser ?? User(),
            );
          } else if (printer.mode == 'STAR_LINE') {
            await starPrinterKitchen.printKitchenTicket(
              context: context,
              l10n: l10n,
              ipAddress: printer.address ?? '',
              order: orderToPrint,
              printerName: printer.productName ?? printer.name ?? '',
              authUser: authUser ?? User(),
            );
          } else {
            AppLog.d('TICKET Unsupported printer mode: ${printer.mode} for printer ${printer.address}');
            continue; // trường hợp mode không được hỗ trợ - Bỏ qua máy in này và chuyển sang máy in tiếp theo
          }

          AppLog.d('TICKET Printed order for printer ${printer.address} with mode ${printer.mode}');
        } catch (e) {
          AppLog.e('TICKET Error printing kitchen ticket for printer ${printer.address}: $e');
          // Có thể continue để tiếp tục với máy in tiếp theo
          continue;
        }
      }
    }
  }

  Order? _createFilteredOrder(Order? originalOrder, List<String> allowedCategoryIds) {
    if (originalOrder == null) return null;

    final filteredOrder = originalOrder.copyWith();

    // Lọc các item dựa trên categories được phép
    final filteredItems = <ItemOrder>[];

    for (final item in originalOrder.items ?? <ItemOrder>[]) {
      final productCategories = item.product?.categories ?? <ProductOrderCetegories>[];

      // Kiểm tra xem item có category được phép in không
      bool hasAllowedCategory = false;
      for (final category in productCategories) {
        if (allowedCategoryIds.contains(category.id)) {
          hasAllowedCategory = true;
          break;
        }
      }

      // Nếu item có category được phép thì thêm vào danh sách
      if (hasAllowedCategory) {
        filteredItems.add(item);
      }
    }

    return filteredOrder.copyWith(items: filteredItems);
  }

  bool _isOrderEmpty(Order order) {
    return order.items == null || order.items!.isEmpty;
  }
}
