// ignore_for_file: use_decorated_box, empty_catches, unawaited_futures, inference_failure_on_collection_literal

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/target-offers-rfm-customer/target-offers-rfm-customer.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/target-offers/widgets/@target-offers-widgets.dart';

class TargetOfferCustomerPage extends StatelessWidget {
  const TargetOfferCustomerPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    TargetOffersSegments? targetOffersSegments;
    String id = '';
    if (args != null && args is ScreenArguments) {
      targetOffersSegments = args.data as TargetOffersSegments;
      id = targetOffersSegments.id ?? '';
    }

    return BlocProvider(
      create: (context) => TargetOffersRFMCustomerCubit()
        ..onChangedID(id)
        ..geCustomers(),
      child: TargetOffersContentWidget(targetOffersSegments: targetOffersSegments),
    );
  }
}

class TargetOffersContentWidget extends StatefulWidget {
  final TargetOffersSegments? targetOffersSegments;
  const TargetOffersContentWidget({super.key, this.targetOffersSegments});

  @override
  State<TargetOffersContentWidget> createState() => _TargetOffersContentWidgetState();
}

class _TargetOffersContentWidgetState extends State<TargetOffersContentWidget> {
  final _searchControl = TextEditingController();

  Timer? _debounce;

  @override
  void dispose() {
    super.dispose();
    try {
      _debounce?.cancel();
      _searchControl.dispose();
    } catch (e) {}
  }

  void _doDebounce({Function? onChange, int milliseconds = 1000}) {
    try {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(Duration(milliseconds: milliseconds), () {
        onChange?.call();
      });
    } catch (e) {}
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      appBar: _buildAppbar(context, _buildSegmentTitle(l10n, widget.targetOffersSegments?.type ?? ''), _searchControl),
      body: SafeArea(
          bottom: false,
          child: BlocSelector<TargetOffersRFMCustomerCubit, TargetOffersRFMCustomerState, bool>(
            selector: (state) {
              return state.isHasMore ?? false;
            },
            builder: (context, state) {
              return NotificationListener<ScrollNotification>(
                onNotification: (notification) {
                  if (notification is ScrollEndNotification) {
                    if (notification.metrics.pixels == notification.metrics.maxScrollExtent && state) {
                      context.read<TargetOffersRFMCustomerCubit>().geCustomers(isLoadMore: true, isLoading: false);
                      return true;
                    }
                  }
                  return true;
                },
                child: RefreshIndicator(
                  color: AppColors.appColor,
                  onRefresh: () => _onRefresh(context, _searchControl),
                  child: _buildBody(),
                ),
              );
            },
          )),
    );
  }

  Widget _buildBody() {
    return BlocConsumer<TargetOffersRFMCustomerCubit, TargetOffersRFMCustomerState>(
      listener: (context, state) {
        if (state.status == TargetOffersRFMCustomerStatus.Error) {
          if (state.errorMsg != null && state.errorMsg != '') {
            AppBased.toastError(context, title: state.errorMsg);
            BlocProvider.of<TargetOffersRFMCustomerCubit>(context).onResetStatus();
          }
        }
      },
      builder: (context, state) {
        AppLog.e('geCustomers XXXX: ${state.status}');

        return CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(
            parent: ClampingScrollPhysics(),
          ),
          slivers: [
            if (state.status == TargetOffersRFMCustomerStatus.Loading) ...{
              const CustomerLoading(),
            } else if (state.status == TargetOffersRFMCustomerStatus.Success) ...{
              if (state.customers?.data != null && state.customers?.data != [] && (state.customers?.data?.length ?? 0) > 0) ...{
                _buildList(context, state.customers?.data ?? []),
                if (state.isHasMore == true) ...{
                  SliverList(
                    delegate: SliverChildListDelegate(
                      [
                        const SizedBox(height: 16),
                        Center(
                          child: Container(
                            width: 32,
                            height: 32,
                            padding: const EdgeInsets.all(2.0),
                            margin: const EdgeInsets.only(top: 8, bottom: 24),
                            child: const CircularProgressIndicator(
                              color: AppColors.appColor,
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                }
              } else ...{
                TargetedOffersEmptyWidget(isSearchEmpty: state.searchText != null && state.searchText != ''),
              },
            } else if (state.status == TargetOffersRFMCustomerStatus.Error) ...{
              const TargetedOffersEmptyWidget(),
            },
            SliverList(
              delegate: SliverChildListDelegate(
                [
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildList(BuildContext context, List<CustomerOnSegment>? items) {
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        childCount: items?.length,
        (context, index) {
          final CustomerOnSegment? item = items?.elementAt(index);
          final bool isBoder = items?.length == index + 1;
          return _buildItem(context, timeZone, items, item, isBoder: isBoder);
        },
      ),
    );
  }

  Widget _buildItem(
    BuildContext context,
    String timeZone,
    List<CustomerOnSegment>? items,
    CustomerOnSegment? customer, {
    bool isBoder = false,
  }) {
    final l10n = context.l10n;
    final isActive = customer?.isActive ?? true;

    final displayName = isActive ? customer?.displayName ?? l10n.guest : l10n.guestDelete;
    final phone = customer?.phone ?? '';
    final avatarUrl = customer?.avatarUrl ?? '';
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            border: isBoder == false
                ? const Border(
                    bottom: BorderSide(width: 1, color: AppColors.appBorderColor),
                  )
                : null,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: AvatarControlWidget(
                    width: 48,
                    height: 48,
                    name: displayName,
                    urlImage: avatarUrl,
                    borderColor: Colors.grey.shade100,
                    borderRadius: 50,
                    backgroundColor: const Color(0xFFF7F7F7),
                  ),
                ),
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(left: 12),
                    alignment: Alignment.centerLeft,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      // mainAxisSize: MainAxisSize.min,
                      children: [
                        if (displayName != '')
                          Text(
                            displayName,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.appBlackColor,
                            ),
                          ),
                        if (AppValidations.formatPhoneNumber(phone) != '')
                          Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              AppValidations.formatPhoneNumber(phone),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.appBlackColor.withValues(alpha: .56),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                if (isActive && customer?.customerId != null && customer?.customerId != '')
                  InkWell(
                    onTap: () {
                      final userData = Search(
                        userId: customer?.customerId,
                        displayName: displayName,
                        phoneNumber: customer?.phone,
                        businessId: customer?.businessId,
                        user: User(
                          id: customer?.customerId,
                          displayName: displayName,
                          email: customer?.email,
                          phone: customer?.phone,
                        ),
                      );
                      final data = {'user': userData};
                      AppBased.go(
                        context,
                        AppRoutes.redeemPoint,
                        args: ScreenArguments(
                          data: data,
                          type: 'activities-page',
                        ),
                        onChanged: (data) {},
                      );
                    },
                    child: Container(
                      margin: const EdgeInsets.only(right: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            width: 1,
                            color: AppColors.appBlackColor.withValues(alpha: .3),
                          )),
                      child: Text(
                        l10n.detail,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlackColor,
                        ),
                      ),
                    ),
                  )
              ],
            ),
          ),
        );
      },
    );
  }

  AppBar _buildAppbar(
    BuildContext context,
    String title,
    TextEditingController _searchControl,
  ) {
    return AppBar(
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(55),
        child: BlocBuilder<TargetOffersRFMCustomerCubit, TargetOffersRFMCustomerState>(
          builder: (context, state) {
            return Container(padding: const EdgeInsets.fromLTRB(16, 0, 16, 8), child: _buildSearchBar(context, _searchControl));
          },
        ),
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, TextEditingController _searchControl) {
    final l10n = context.l10n;
    return BlocBuilder<TargetOffersRFMCustomerCubit, TargetOffersRFMCustomerState>(
      builder: (context, state) {
        return TextFormField(
          cursorColor: AppColors.cursorColor,
          // focusNode: focusNode,
          enableSuggestions: true,
          autocorrect: true,
          controller: _searchControl,
          textInputAction: TextInputAction.next,
          // inputFormatters: [
          //   FilteringTextInputFormatter.deny(RegExp(r'[^\s\w]')),
          // ],
          decoration: InputDecoration(
            prefixIcon: const Icon(
              Icons.search,
              color: Colors.black,
              size: 24,
            ),
            contentPadding: EdgeInsets.zero,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                width: 1,
                color: AppColors.appBorderColor,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(
                width: 1,
                color: Colors.black.withValues(alpha: .7),
              ),
            ),
            hintText: l10n.search,
            hintStyle: const TextStyle(color: Color(0xFF909090)),
            suffixIcon: state.searchText != null && state.searchText != ''
                ? InkWell(
                    hoverColor: Colors.transparent,
                    splashColor: Colors.transparent,
                    onTap: () {
                      FocusScope.of(context).unfocus();
                      context.read<TargetOffersRFMCustomerCubit>()
                        ..onChangedPage(0)
                        ..onSearchChange('').then((onValue) {
                          context.read<TargetOffersRFMCustomerCubit>().geCustomers();
                        });
                      _searchControl.clear();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 24,
                      color: Color(0xFF909090),
                    ),
                  )
                : null,
          ),
          onChanged: (String value) {
            context.read<TargetOffersRFMCustomerCubit>()
              ..onChangedPage(0)
              ..onSearchChange(value).then((onValue) {
                _doDebounce(
                  milliseconds: 1000,
                  onChange: () {
                    context.read<TargetOffersRFMCustomerCubit>().geCustomers();
                  },
                );
              });
          },
        );
      },
    );
  }

  String _buildSegmentTitle(AppLocalizations l10n, String type) {
    switch (type.toUpperCase()) {
      case 'AT_RISK_CUSTOMERS':
        return l10n.atRiskCustomers;
      case 'CHAMPION_CUSTOMERS':
        return l10n.championCustomers;
      case 'HIBERNATING_CUSTOMERS':
        return l10n.hibernatingCustomers;
      case 'LOST_CUSTOMERS':
        return l10n.lostCustomers;
      case 'LOYAL_CUSTOMERS':
        return l10n.loyalCustomers;
      case 'NEW_CUSTOMERS':
        return l10n.newCustomers;
      case 'POTENTIAL_CUSTOMERS':
        return l10n.potentialLoyalist;
      case 'REACTIVATED_CUSTOMERS':
        return l10n.reactivatedCustomers;
      default:
        return type;
    }
  }

  Future<void> _onRefresh(BuildContext context, TextEditingController _searchControl) async {
    _searchControl.clear();
    context.read<TargetOffersRFMCustomerCubit>()
      ..onSearchChange('')
      ..geCustomers();
  }
}
