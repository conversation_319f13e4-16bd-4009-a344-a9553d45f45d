import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/target-offer-detail-customer/target-offer-detail-customer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/target-offer-detail/target-offer-detail.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/markdown-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/target-offers/widgets/@target-offers-widgets.dart';

class TargetOfferDetailPage extends StatefulWidget {
  const TargetOfferDetailPage({super.key});

  @override
  State<TargetOfferDetailPage> createState() => _TargetOfferDetailPageState();
}

class _TargetOfferDetailPageState extends State<TargetOfferDetailPage> {
  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    TargetedOffer? targetedOffer;
    String id = '';
    if (args != null && args is ScreenArguments) {
      targetedOffer = args.data as TargetedOffer;
      id = targetedOffer.id ?? '';
    }
    return BlocProvider(
      create: (context) => TargetOfferCubit()
        ..onChangedID(id)
        ..getCustomersSegment(),
      child: TargetOffersContentWidget(targetedOffer: targetedOffer),
    );
  }
}

class TargetOffersContentWidget extends StatelessWidget {
  final TargetedOffer? targetedOffer;
  const TargetOffersContentWidget({super.key, this.targetedOffer});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      appBar: _buildAppbar(context, l10n.targetedOfferDetailTitle),
      body: SafeArea(
          bottom: false,
          child: RefreshIndicator(
            color: AppColors.appColor,
            onRefresh: () => _onRefresh(context),
            child: _buildBody(context),
          )),
    );
  }

  Widget _buildBody(BuildContext context) {
    final timeZone = context.read<AuthBloc>().state.businessTimeZone ?? '';
    return BlocConsumer<TargetOfferCubit, TargetOfferState>(
      listener: (context, state) {
        if (state.status == TargetOfferStatus.Error) {
          if (state.errorMsg != null && state.errorMsg != '') {
            AppBased.toastError(context, title: state.errorMsg);
            BlocProvider.of<TargetOfferCubit>(context).onResetStatus();
          }
        }
      },
      builder: (context, state) {
        return CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(
            parent: ClampingScrollPhysics(),
          ),
          slivers: [
            if (state.status == TargetOfferStatus.Loading) ...{
              const TargetedOfferLoading(),
            } else if (state.status == TargetOfferStatus.Success) ...{
              if (state.targetedOffer != null) ...{
                SliverFillRemaining(
                    hasScrollBody: false,
                    child: _buildContent(
                      context,
                      state.targetedOffer,
                      timeZone,
                    ))
              } else ...{
                const SizedBox.shrink()
              },
            } else if (state.status == TargetOfferStatus.Error) ...{
              const SizedBox.shrink()
            }
          ],
        );
      },
    );
  }

  String formatPercentage(double value) {
    if (value.isNaN || value.isInfinite) {
      return '0%';
    }
    final String fixed = value.toStringAsFixed(2);
    if (fixed.endsWith('.00')) {
      return '${value.toInt()}%';
    } else {
      return '$fixed%';
    }
  }

  Widget _buildContent(BuildContext context, TargetedOffer? offer, String timeZone) {
    final l10n = context.l10n;
    final language = Localizations.localeOf(context).languageCode;

    final numberOfClaimed = offer?.numberOfClaimed ?? 0;
    final numberOfClaimedPercent = formatPercentage(numberOfClaimed / (offer?.numberOfTargeted ?? 0) * 100);

    final numberOfRedeemed = offer?.numberOfRedeemed ?? 0;
    final numberOfRedeemedPercent = formatPercentage(numberOfRedeemed / (offer?.numberOfClaimed ?? 0) * 100);

    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return DecoratedBox(
          decoration: const BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 4, 16, 0),
                child: Row(
                  children: [
                    Flexible(
                      child: Text(
                        offer?.title ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: AppColors.appBlackColor,
                        ),
                        // maxLines: 1,
                        // overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // if (offer?.isPublish != true) ...[
                    //   const SizedBox(width: 8),
                    //   Container(
                    //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    //     decoration: BoxDecoration(
                    //       color: AppColors.appBGAvatarColor,
                    //       borderRadius: BorderRadius.circular(8),
                    //     ),
                    //     child: Text(
                    //       l10n.draftOfferStatus,
                    //       style: const TextStyle(
                    //         fontSize: 12,
                    //         color: AppColors.appBlackColor,
                    //         fontWeight: FontWeight.w500,
                    //       ),
                    //     ),
                    //   ),
                    // ],
                  ],
                ),
              ),
              _buildDivider(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.offerPerformance.toUpperCase(),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.appBlackColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(16.0),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.appBorderColor,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildItemPerformance(
                                  l10n.claimedOffer,
                                  AppValidations.getTotalPrice(
                                    numberOfClaimed,
                                    language: language,
                                  ),
                                  isLink: true,
                                  onTap: () {
                                    _openPerformance(
                                        context,
                                        offer,
                                        l10n.claimedOffer,
                                        AppValidations.getTotalPrice(
                                          numberOfClaimed,
                                          language: language,
                                        ),
                                        'CLAIMED');
                                  },
                                ),
                              ),
                              Expanded(
                                child: _buildItemPerformance(
                                  l10n.redeemedOffer,
                                  AppValidations.getTotalPrice(
                                    numberOfRedeemed,
                                    language: language,
                                  ),
                                  isLink: true,
                                  onTap: () {
                                    _openPerformance(
                                        context,
                                        offer,
                                        l10n.redeemedOffer,
                                        AppValidations.getTotalPrice(
                                          numberOfRedeemed,
                                          language: language,
                                        ),
                                        'REDEEMED');
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Expanded(
                                child: _buildItemPerformance(l10n.claimRate, numberOfClaimedPercent),
                              ),
                              Expanded(
                                child: _buildItemPerformance(l10n.redemptionRate, numberOfRedeemedPercent),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              _buildDivider(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    _buildStatRow(
                      l10n.duration,
                      l10n.durationContent(
                        '${offer?.durationExpiresIn ?? 0} ${(offer?.durationExpiresIn ?? 0) > 1 ? l10n.days('s').toLowerCase() : l10n.day.toLowerCase()}',
                      ),
                      isBoderBottom: true,
                    ),
                    _buildStatRow(
                      l10n.maxPerUserOffer,
                      (offer?.maxPerUser ?? 0) == 0 ? l10n.unlimited : offer?.maxPerUser.toString() ?? '0',
                    ),
                  ],
                ),
              ),
              if ((offer?.description != null && offer?.description != '') || (offer?.media != null && offer!.media!.isNotEmpty)) _buildDivider(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (offer?.description != null && offer?.description != '') ...[
                      MarkdownBodyWidget(
                        content: offer?.description ?? '',
                        bottomPadding: 0,
                      ),
                    ],
                    if (offer?.media != null && offer!.media!.isNotEmpty) ...{
                      _buildPhotos(language, l10n, offer),
                    },
                  ],
                ),
              ),
              if (offer?.offersOnBusinessTag != null && offer!.offersOnBusinessTag!.isNotEmpty) ...{
                _buildDivider(),
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Text(
                          l10n.tagsTitle.toUpperCase(),
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.darkPrimaryBackgroundColor,
                          ),
                        ),
                      ),
                      _buildTags(language, l10n, offer),
                    ],
                  ),
                ),
              },
              const SizedBox(height: 32),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 16),
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.appBGAvatarColor,
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  Widget _buildStatRow(String label, String value, {bool isBoderBottom = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: isBoderBottom
            ? const Border(
                bottom: BorderSide(
                  color: AppColors.appBorderColor,
                  width: 1,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.appBlackColor.withValues(alpha: 0.56),
              ),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.appBlackColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTags(String language, AppLocalizations l10n, TargetedOffer? offer) {
    final tags = offer?.offersOnBusinessTag ?? [];
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: tags.map((e) => _buildTag(e.name ?? '')).toList(),
    );
  }

  Widget _buildTag(String tag) {
    return tag == ''
        ? const SizedBox.shrink()
        : Container(
            padding: const EdgeInsets.fromLTRB(12, 8, 12, 8),
            decoration: BoxDecoration(
              color: AppColors.appBGAvatarColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              tag,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlackColor,
              ),
            ),
          );
  }

  Widget _buildItemPerformance(String label, String value, {bool isLink = false, Function? onTap}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          textAlign: TextAlign.right,
          softWrap: true,
          text: TextSpan(
            text: label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.homeShowQRBGColor,
              fontWeight: FontWeight.w400,
            ),
            children: [
              if (isLink && value != '0')
                WidgetSpan(
                  child: Container(
                    margin: const EdgeInsets.only(left: 4),
                    width: 18,
                    height: 18,
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        onTap?.call();
                      },
                      icon: SvgPicture.asset(
                        'assets/svgs/open-link.svg',
                        colorFilter: const ColorFilter.mode(
                          Color(0xFF3448F0),
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            color: AppColors.appBlackColor,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }

  AppBar _buildAppbar(BuildContext context, String title) {
    return AppBar(
      titleSpacing: 0.0,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildPhotos(String language, AppLocalizations l10n, TargetedOffer? offer) {
    final media = offer?.media ?? [];
    return Column(
      children: media.map((photo) {
        final url = photo.media?.publicId != null ? '${AppBased.appEnv.cdnUrl}${photo.media?.publicId}' : photo.media?.file;
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          constraints: const BoxConstraints(
            minHeight: 120,
            maxWidth: 680,
          ),
          child: CachedNetworkImage(
            imageUrl: url ?? '',
            fit: BoxFit.fitWidth,
            placeholder: (context, url) => Container(
              color: AppColors.appImageBackgroundColor.withValues(alpha: .7),
              alignment: Alignment.center,
              child: const CircularProgressIndicator(
                color: AppColors.appColor,
                strokeWidth: 1,
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: AppColors.appImageBackgroundColor.withValues(alpha: .7),
              alignment: Alignment.center,
              child: const Icon(
                Icons.error_outline,
                size: 30,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPerformanceContent(
    BuildContext context,
    TargetedOffer? offer,
    String type,
  ) {
    final l10n = context.l10n;
    final id = offer?.id ?? '';
    context.read<TargetOfferCustomerCubit>()
      ..onChangedID(id)
      ..geTargetedOfferCustomer(id, type, isLoading: false);
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 16),
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: AppColors.appBlackColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.all(12),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    l10n.name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ),
                Text(
                  l10n.phone,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.appBlackColor,
                  ),
                ),
              ],
            ),
          ),
          BlocConsumer<TargetOfferCustomerCubit, TargetOfferCustomerState>(
            listener: (context, state) {},
            builder: (context, state) {
              switch (state.status) {
                case TargetOfferCustomerStatus.Loading:
                  return const CustomerLoading();

                case TargetOfferCustomerStatus.Success:
                  final users = state.users ?? [];
                  if (users.isNotEmpty) {
                    return Container(
                      margin: const EdgeInsets.only(top: 10),
                      child: Column(
                        children: users
                            .map(
                              (user) => _buildPerformanceItem(l10n, user, isBgColor: users.indexOf(user) % 2 != 0, onTap: () {
                                final userData = Search(
                                  userId: user.id,
                                  displayName: user.displayName,
                                  phoneNumber: user.phone,
                                  businessId: offer?.businessId,
                                  user: User(
                                    id: user.id,
                                    displayName: user.displayName,
                                    // email: order.customer?.email,
                                    phone: user.phone,
                                  ),
                                );
                                final data = {'user': userData};
                                AppBased.go(
                                  context,
                                  AppRoutes.redeemPoint,
                                  args: ScreenArguments(
                                    data: data,
                                    type: 'activities-page',
                                  ),
                                  onChanged: (data) {},
                                );
                              }),
                            )
                            .toList(),
                      ),
                    );
                  }
                  return const SizedBox.shrink();

                default:
                  return const SizedBox.shrink();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceItem(AppLocalizations l10n, User user, {Function? onTap, bool isBgColor = false}) {
    final isActive = user.isActive ?? true;

    return Container(
      decoration: BoxDecoration(
        color: isBgColor ? AppColors.appBlackColor.withValues(alpha: 0.05) : AppColors.appTransparentColor,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              isActive ? user.displayName ?? l10n.guest : l10n.guestDelete,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.appBlackColor,
              ),
            ),
          ),
          RichText(
            textAlign: TextAlign.right,
            softWrap: true,
            text: TextSpan(
              text: AppValidations.formatPhoneNumber(user.phone ?? ''),
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.appBlackColor,
                fontWeight: FontWeight.w500,
              ),
              children: [
                if (AppValidations.formatPhoneNumber(user.phone ?? '') != '' && user.displayName != null && isActive && user.id != null && user.id != '')
                  WidgetSpan(
                    child: Container(
                      margin: const EdgeInsets.only(left: 12),
                      width: 17,
                      height: 17,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        onPressed: () {
                          onTap?.call();
                        },
                        icon: SvgPicture.asset(
                          'assets/svgs/open-link.svg',
                          colorFilter: const ColorFilter.mode(
                            Color(0xFF3448F0),
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _openPerformance(
    BuildContext context,
    TargetedOffer? offer,
    String? title,
    String? titleValue,
    String? type,
  ) {
    AppBased.openShowModalCustomerButtomSheet(
      context,
      isLeading: false,
      title: title,
      titleValue: titleValue,
      widget: _buildPerformanceContent(
        context,
        offer,
        type ?? '',
      ),
    );
  }

  Future<void> _onRefresh(BuildContext context) async {
    await context.read<TargetOfferCubit>().getCustomersSegment();
  }
}
