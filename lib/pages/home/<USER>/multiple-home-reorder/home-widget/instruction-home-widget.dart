import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/instruction-multi-home/instruction-multi-home.cubit.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class HomeInstructionShowcaseWidget extends StatelessWidget {
  final GlobalKey globalKey;
  final Widget widget;
  final String? headerTitle;
  final String? subTitle;
  final Function? onTapTarget;
  final double width;
  final double height;
  final double borderRadius;
  final double targetPadding;
  final Positioned? positionedIconTooltip;
  final TooltipPosition? tooltipPosition;
  const HomeInstructionShowcaseWidget({super.key, required this.globalKey, required this.widget, required this.headerTitle, required this.subTitle, required this.onTapTarget, this.width = 360, this.height = 350, this.positionedIconTooltip, this.borderRadius = 8, this.targetPadding = 4, this.tooltipPosition});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Showcase.withWidget(
      tooltipPosition: tooltipPosition,
      key: globalKey,
      width: width,
      height: height,
      disableMovingAnimation: false,
      disableBarrierInteraction: true,
      targetShapeBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(borderRadius)),
      ),
      targetBorderRadius: BorderRadius.circular(borderRadius),
      targetPadding: EdgeInsets.all(targetPadding),
      onTargetClick: () {
        ShowCaseWidget.of(context).dismiss();
        onTapTarget?.call();
      },
      container: InkWell(
        onTap: () {
          ShowCaseWidget.of(context).dismiss();
          onTapTarget?.call();
        },
        child: Container(
          width: 350,
          margin: const EdgeInsets.only(bottom: 10),
          child: Stack(
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 10, top: 10),
                padding: const EdgeInsets.fromLTRB(12, 12, 0, 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Expanded(
                          child: Text(
                            headerTitle ?? '',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              color: AppColors.appBlackColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 12.0, top: 4),
                      child: Text(
                        subTitle ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.multipleHomeTooltipColor,
                        ),
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: InkWell(
                            onTap: () {
                              context.read<InstructionMultiHomeCubit>().doSkipInstructionHome();
                              ShowCaseWidget.of(context).dismiss();
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
                              child: Text(
                                l10n.skip,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.appTransparentColor,
                                  decoration: TextDecoration.underline,
                                  decorationColor: AppColors.multipleHomeMainTooltipSkipColor,
                                  decorationThickness: 1.5,
                                  shadows: [
                                    Shadow(offset: Offset(0, -2), color: AppColors.multipleHomeMainTooltipSkipColor),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
              // if (widget.positionedIconTooltip != null) ...{
              //   widget.positionedIconTooltip ?? const SizedBox.shrink(),
              // }
              // else
              // Positioned(
              //   bottom: 1,
              //   right: 10,
              //   child: SvgPicture.asset(
              //     'assets/svgs/tooltip.svg',
              //     width: 12,
              //     height: 12,
              //   ),
              // ),
            ],
          ),
        ),
      ),
      child: widget,
    );
  }
}
