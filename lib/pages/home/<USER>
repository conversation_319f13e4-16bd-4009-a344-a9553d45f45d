// ignore_for_file: empty_catches, inference_failure_on_collection_literal, strict_raw_type, inference_failure_on_function_invocation

import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/add-sticky/add-sticky.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-features/business-features.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile-edit.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-customer-receipt/customize-customer-receipt.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-kitchen-ticket/customize-kitchen-ticket.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/labels/labels.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-account/multiple-account.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/instruction-focused-home/instruction-focused-home.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/instruction-multi-home/instruction-multi-home.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multiple-home-list/multiple-home-list.cubit.dart';
import 'package:stickyqrbusiness/@core/store/onboarding/onboarding.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/print-reward/print.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-local/printers-local.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers/get-printers.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-tags/qr-tags-points.cubit.dart';
import 'package:stickyqrbusiness/@core/store/role-permission/role-permission.cubit.dart';
import 'package:stickyqrbusiness/@core/store/user-profile/user-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/vouchers-calendar/voucher-calendar.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/home-container-widget/widgets/@container-widget.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/@home-widgets.dart';
import 'package:stickyqrbusiness/pages/home/<USER>/multiple-home-reorder/home-widget/@home-widget.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver, SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin<HomePage> {
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  late final themePage = context.read<AppThemesCubit>().homePage;
  DateTime backPressTime = DateTime.now().add(const Duration(seconds: -10));

  GlobalKey menuButtonKey = GlobalKey();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
    AppBased.setAnalytics('HomePage', 'home-page');
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final String language = Localizations.localeOf(context).languageCode;
    final l10n = context.l10n;
    // final isEngagement = context.read<HomeDefaultWidgetCubit>().state.isEngagement;
    // if (isEngagement) {
    context.read<QrStatisticsCubit>().onBusinessStatistics();
    // }

    return BlocProvider(
      create: (context) => AddStickyCubit(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<BusinessProfileCubit, BusinessProfileState>(
            listener: (context, state) {
              if (state.status == Status.success) {
                try {
                  context.read<BusinessProfileCubit>().onResetStatus();
                  context.read<AuthBloc>().add(AuthStarted());
                } catch (e) {}
              }
            },
          ),
          BlocListener<InstructionMultiHomeCubit, InstructionMultiHomeState>(
            listener: (context, state) {
              switch (state.status) {
                case InstructionMultiHomeStatus.First:
                  _buildBottomSheet(
                    context,
                    l10n,
                    widget: const InstructionHomeMainWidget(),
                    onChange: (value) {
                      if (value != null && value == true) {
                        // context.read<CBTableCubit>().getCBTable(isLoading: false);
                      }
                    },
                  );
                  break;
                case InstructionMultiHomeStatus.Last:
                  context.read<OnboardingCubit>()
                    ..onUpdateExpandedFAB(false)
                    ..onShowHideOnboarding(false)
                    ..onGetOnboarding();

                  break;
                default:
              }
            },
          ),
        ],
        child: Scaffold(
          backgroundColor: AppColors.homeBGColor,
          body: BlocConsumer<AddStickyCubit, AddStickyState>(
            listener: (context, state) {
              switch (state.status) {
                case AddStickyStatus.ShowStickyError:
                  Future.delayed(const Duration(seconds: 3), () {
                    AppBased.showDialogYesNo(
                      context,
                      title: l10n.confirm,
                      noText: l10n.no,
                      yesText: l10n.yes,
                      msgContent: l10n.printError,
                      noTap: () {
                        context.read<AddStickyCubit>().onResetStickyError();
                      },
                      yesTap: () {
                        final stickys = state.dataStickyError ?? [];
                        final allStickys = QueueStickys(
                          stickys: stickys,
                          qty: stickys.length.toString(),
                          points: '0',
                        );
                        context.read<AddStickyCubit>().onAddQueueStickys(allStickys).then((value) {
                          context.read<PrintCubit>().printAllLabel(
                            context,
                            allStickys,
                            language: language,
                            onChange: (Sticky sticky) {
                              context.read<AddStickyCubit>().onAddStickysError(sticky);
                            },
                          );
                          context.read<AddStickyCubit>().onResetStickyError();
                        });
                      },
                    );
                  });
                  return;

                case AddStickyStatus.Error:
                  if (state.errorMsg != null && state.errorMsg != '') {
                    AppBased.toastError(context, title: state.errorMsg);
                    context.read<AddStickyCubit>().onResetStatus();
                  }
                  return;
                case AddStickyStatus.Success:
                  context.read<AddStickyCubit>().onChangedPoints('');
                  context.read<AddStickyCubit>().onChangedQuantity('0');
                  if (state.isPrint) {
                    AppBased.openShowModalBottomSheet(
                      context,
                      backgroundColor: AppColors.darkPrimaryBackgroundColor,
                      widget: ShowStickyWidget(
                        sticky: state.data?[0],
                      ),
                    );
                  } else {
                    if (state.allData!.isNotEmpty) {
                      if (state.allData?[0].isPrinting == false) {
                        AppLog.e('Printing.......');
                        context.read<PrintCubit>().printAllLabel(
                          context,
                          state.allData?[0],
                          language: language,
                          onChange: (Sticky sticky) {
                            AppLog.e('sticky: ${sticky.toJson()}');
                            context.read<AddStickyCubit>().onAddStickysError(sticky);
                          },
                        );
                      }
                    }
                  }
                  return;

                default:
              }
            },
            builder: (context, state) {
              bool isShowQueue = false;
              try {
                isShowQueue = state.allData != [] && state.allData != null && state.allData?.length == 1 && int.parse(state.allData?[0].qty ?? '1') <= 3;
              } catch (e) {}
              // AppLog.e('isShowQueue: //${state.isShowQueue}');
              return Stack(
                children: [
                  _buildBody(),
                  if (isShowQueue) ...{
                    const SizedBox.shrink(),
                  } else
                    bottomPrintings(context, state),
                ],
              );
            },
          ),
          floatingActionButton: BlocBuilder<AddStickyCubit, AddStickyState>(
            builder: (context, state) {
              final allData = state.allData ?? [];
              return allData.isEmpty
                  ? Container(
                      margin: EdgeInsets.only(
                        bottom: Platform.isAndroid ? 46 : 24,
                      ),
                      child: HomeFloatingActionButton(menuButtonKey: menuButtonKey))
                  : const SizedBox.shrink();
            },
          ),
        ),
      ),
    );
  }

  Widget bottomPrintings(BuildContext context, AddStickyState state) {
    final l10n = context.l10n;
    final queuePrint = state.allData ?? [];
    if (queuePrint.isEmpty) return const SizedBox.shrink();

    final wight = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height - (MediaQuery.of(context).padding.top + kToolbarHeight);
    double initialChildSize = .16;
    double minChildSize = .15;
    final double heightItem = queuePrint.length * 48;
    final double percenItem = heightItem / height;
    final double percenOneItem = 48 / height;
    if (height < wight) {
      initialChildSize = percenItem + .21;
      minChildSize = percenOneItem + .21;
    } else {
      initialChildSize = percenItem + .1;
      minChildSize = percenOneItem + .1;
    }
    if (initialChildSize >= 1) {
      initialChildSize = 0.85;
    }
    return DraggableScrollableSheet(
      initialChildSize: initialChildSize,
      minChildSize: minChildSize,
      maxChildSize: initialChildSize + .02,
      snap: true,
      // snapAnimationDuration: const Duration(seconds: 1),
      builder: (BuildContext context, ScrollController scrollController) {
        return SafeArea(
          top: false,
          bottom: false,
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 24),
            decoration: const BoxDecoration(
              color: AppColors.appPrintingBG,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(16.0),
                bottom: Radius.circular(16.0),
              ),
            ),
            child: ListView(
              controller: scrollController,
              physics: const AlwaysScrollableScrollPhysics(
                parent: ClampingScrollPhysics(),
              ),
              padding: EdgeInsets.zero,
              children: [
                DecoratedBox(
                  // margin: const EdgeInsets.only(bottom: 24),
                  decoration: const BoxDecoration(
                    color: AppColors.darkPrimaryBackgroundColor,
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16.0),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 48,
                        height: 6,
                        margin: const EdgeInsets.only(top: 8, bottom: 8),
                        decoration: BoxDecoration(
                          color: AppColors.appBorderColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Table(
                    defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                    children: [
                      for (int i = 0; i < queuePrint.length; i++) ...{
                        _buildPrintingItem(
                          l10n,
                          queuePrint[i],
                          isBoderBottom: i == queuePrint.length - 1,
                          onTap: () {
                            if (i == 0) {
                              try {
                                queuePrint[i].stopPrinter = true;
                              } catch (e) {}
                            } else {
                              context.read<AddStickyCubit>().onRemoveQueueStickys(index: i).then((List<QueueStickys> queueStickys) {});
                            }
                          },
                        ),
                      },
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  TableRow _buildPrintingItem(
    AppLocalizations l10n,
    QueueStickys data, {
    Function? onTap,
    bool isBoderBottom = false,
  }) {
    final String language = Localizations.localeOf(context).languageCode;

    return TableRow(
      children: [
        TableCell(
          child: Container(
            margin: const EdgeInsets.only(left: 8.0, right: 8),
            padding: const EdgeInsets.all(8),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: data.isPrinting == true ? AppColors.appColor : AppColors.darkPrimaryBackgroundColor,
              borderRadius: BorderRadius.circular(14),
            ),
            child: data.isLoading == true
                ? Container(
                    width: 16,
                    height: 16,
                    padding: const EdgeInsets.all(2.0),
                    // margin: marginLoadingIcon ?? const EdgeInsets.only(left: 8),
                    child: const CircularProgressIndicator(
                      color: AppColors.lightPrimaryBackgroundColor,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    data.isPrinting == true ? l10n.printing.toUpperCase() : l10n.queue.toUpperCase(),
                    style: const TextStyle(
                      color: AppColors.lightPrimaryBackgroundColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Text(
                    l10n.points.toUpperCase(),
                    style: const TextStyle(
                      color: AppColors.iconColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Text(
                  AppValidations.getTotalPrice(num.parse(data.points.toString()), language: language),
                  style: const TextStyle(
                    color: AppColors.lightPrimaryBackgroundColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: Text(
                    l10n.qtyPrintQueue.toUpperCase(),
                    style: const TextStyle(
                      color: AppColors.iconColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Text(
                  AppValidations.getTotalPrice(num.parse(data.qty.toString()), language: language),
                  style: const TextStyle(
                    color: AppColors.lightPrimaryBackgroundColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        TableCell(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: data.isLoading == true
                ? const SizedBox.shrink()
                : Container(
                    margin: EdgeInsets.only(right: data.isPrinting == true ? 14.0 : 0),
                    alignment: Alignment.centerRight,
                    child: OutlinedButton(
                      onPressed: () => onTap?.call(),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.zero,
                        side: BorderSide(
                          width: 1,
                          color: data.isPrinting == true ? AppColors.iconColor.withValues(alpha: .5) : AppColors.appTransparentColor,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                      ),
                      child: data.isPrinting == true
                          ? Text(
                              l10n.cancel,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: AppColors.lightPrimaryBackgroundColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/svgs/close.svg',
                              // width: 24,
                              // height: 24,
                              colorFilter: const ColorFilter.mode(
                                AppColors.lightPrimaryBackgroundColor,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return BlocBuilder<LabelsCubit, LabelsState>(
      builder: (context, state) {
        final l10n = context.l10n;
        return PopScope(
          canPop: false,
          onPopInvokedWithResult: (bool didPop, f) async {
            if (didPop) {
              return;
            }
            final bool shouldPop = await exitApp(l10n);
            if (shouldPop) {
              await SystemNavigator.pop();
            }
          },
          child: Responsive(
            mobile: HomeMobilePage(menuButtonKey: menuButtonKey),
            tablet: HomeMobilePage(menuButtonKey: menuButtonKey),
            desktop: HomeMobilePage(menuButtonKey: menuButtonKey),
          ),
          // child: BlocBuilder<AuthBloc, AuthState>(
          //   builder: (context, state) {
          //     final isNewHomePage = state.isNewHomePage ?? false;
          //     AppLog.e('isNewHomePage: $isNewHomePage');
          //     if (isNewHomePage) {
          //       return Responsive(
          //         mobile: HomeMobilePage(menuButtonKey: menuButtonKey),
          //         tablet: HomeMobilePage(menuButtonKey: menuButtonKey),
          //         desktop: HomeMobilePage(menuButtonKey: menuButtonKey),
          //       );
          //     } else {
          //       return Responsive(
          //         mobile: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
          //         tablet: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
          //         desktop: HomeMobileMultiPage(menuButtonKey: menuButtonKey),
          //         // tablet: HomeTabletPage(menuButtonKey: menuButtonKey),
          //         // desktop: HomeTabletPage(menuButtonKey: menuButtonKey),
          //       );
          //     }
          //   },
          // ),
        );
      },
    );
  }

  void _buildBottomSheet(
    BuildContext context,
    AppLocalizations l10n, {
    required Widget widget,
    required ValueChanged onChange,
  }) {
    AppBased.openShowModalBottomSheetTableService(
      context,
      isDismissible: false,
      enableDrag: false,
      onChanged: (value) => onChange.call(value),
      widget: GestureDetector(
        onTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(''),
              ],
            ),
            Flexible(
              child: SingleChildScrollView(child: widget),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 48, top: 10),
              child: ButtonLoading(
                height: 48,
                label: l10n.next,
                labelColor: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                buttonBackgroundColor: AppColors.appBlackColor,
                callback: () {
                  context.read<InstructionMultiHomeCubit>().onChangedNextStep(InstructionMultiHomeStep.CreateHomes);
                  Navigator.pop(context, true);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Row(
      children: [
        IconButton(
          onPressed: () {},
          icon: const Icon(
            Icons.close,
            size: 18,
            color: AppColors.appTransparentColor,
          ),
          splashRadius: 16,
        ),
        Expanded(
          child: Center(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        Transform.translate(
          offset: const Offset(5, 0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.lightPrimaryBackgroundColor,
              onPressed: () {
                try {
                  context.read<InstructionMultiHomeCubit>().doSkipInstructionHome();
                  Navigator.of(context).pop();
                } catch (e) {}
              },
              color: AppColors.lightPrimaryBackgroundColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/close.svg',
                colorFilter: const ColorFilter.mode(
                  AppColors.darkPrimaryBackgroundColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<bool> exitApp(AppLocalizations l10n) {
    try {
      final DateTime now = DateTime.now();
      if (now.difference(backPressTime) < const Duration(seconds: 2)) {
        return Future(() => true);
      } else {
        backPressTime = DateTime.now();
        AppBased.openSnackBar(
          context,
          l10n.againExitApp,
          onChange: (value) {},
        );
        return Future(() => false);
      }
    } catch (e) {
      return Future(() => false);
    }
  }

  void _onUpdateTemplateReceipt() {
    final l10n = context.l10n;
    final authState = context.read<AuthBloc>().state;
    final logoId = authState.business?.logo ?? '';
    context.read<CustomizeCustomerReceiptCubit>().getReceiptTemplates(l10n: l10n, logoId: logoId);
    context.read<CustomizeKitchenTicketCubit>().getReceiptTemplates();
  }

  void _buildCompleted() {
    try {
      try {
        context.read<BusinessFeaturesCubit>().getBusinessFeatures();
      } catch (e) {}
      if (!mounted) return;
      context.read<HomeDefaultWidgetCubit>().onChangeWidgetFirstLocal();
      context.read<MultipleHomeListCubit>().doGetMultipleHome().then((homes) {
        try {
          if (homes!.isNotEmpty) {
            context.read<HomeDefaultWidgetCubit>().onChangeFistHomesWidget(homes);
          } else {
            context.read<HomeDefaultWidgetCubit>().onChangeWidgetFirstLocal();
          }
        } catch (e) {}
      });
      context.read<BusinessProfileCubit>().getBusinessProfile().then(
        (business) {
          try {
            context.read<MultipleAccountCubit>().geMultipleAccount();

            context.read<LabelsCubit>().doGetLabels(type: LabelType.Rewards).then(
              (value) {
                try {
                  context.read<GetPrintersCubit>().getPrinters(page: 0).then(
                    (value) {
                      context.read<PrintersLocalCubit>().doGetPrinterLocal();
                      context.read<UserProfileCubit>().getUserProfile();
                      context.read<PrintCubit>().doCheckLabelPrinter().then((value) {
                        if (!value) {
                          context.read<PrintCubit>().doCheckLabelPrinter();
                        }
                      });
                      context.read<QrTagsPointsCubit>().getQrTagsPoints(page: 0);
                      _onUpdateTemplateReceipt();
                    },
                  );
                } catch (e) {}
              },
            );
          } catch (e) {}
          if (business?.acceptCustomerClaimsPoint == true) {
            //&& context.read<HomeDefaultWidgetCubit>().state.isPointClaimsAllHome
            try {
              context.read<PointClaimsCubit>().getPointClaims();
            } catch (e) {}
          }
          if (business?.enableCallButton == true) {
            // && context.read<HomeDefaultWidgetCubit>().state.isCallButtonAllHome
            try {
              context.read<CallButtonCubit>().getSections();
            } catch (e) {}
          }

          // if (context.read<HomeDefaultWidgetCubit>().state.isVoucherCalendarAllHome) {
          try {
            final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
            final String language = Localizations.localeOf(context).languageCode;
            context.read<VoucherCalendarCubit>()
              ..onChangedCalendarType(CalendarType.Month)
              ..onChangedTimeZone(timeZone)
              ..onChangedLanguage(language)
              ..onChangeFocusedDay()
              ..getVoucherCalendar();
          } catch (e) {}

          // }
        },
      );
      context.read<BusinessFeaturesCubit>().getBusinessFeatures();
      if (!context.read<AuthBloc>().state.businessOwner) {
        context.read<RolePermissionCubit>().getRolePermission();
      }
      context.read<LabelsCubit>().doGetLabels(type: LabelType.Offer);

      Future.delayed(const Duration(milliseconds: 600), () async {
        if (mounted && context.read<AuthBloc>().state.businessOwner) {
          // await context.read<InstructionMultiHomeCubit>().doCheckFirstHome();
          await context.read<InstructionFocusedHomeCubit>().doCheckFirstHome();

          if (context.read<AuthBloc>().state.isNewHomePage == false) {
            await context.read<InstructionMultiHomeCubit>().doCheckFirstHome();

            AppLog.e('XXXX 1: MULTIPLE Home');
            if (context.read<InstructionFocusedHomeCubit>().state.status == InstructionFocusedHomeStatus.First) {
              _showNewHome(context);
            }
          } else {
            AppLog.e('XXXX 2: New Home');
          }
        }
      });
    } catch (e) {}
  }

  void _showNewHome(BuildContext context) {
    final l10n = context.l10n;
    showDialog(
      barrierDismissible: false,
      useSafeArea: false,
      context: context,
      builder: (BuildContext context) {
        final bool isTablet = MediaQuery.of(context).size.width > 600;
        return AlertDialog(
          titlePadding: EdgeInsets.zero,
          contentPadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          backgroundColor: AppColors.lightPrimaryBackgroundColor,
          content: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColors.activitiesPendingPointsColor,
            ),
            constraints: const BoxConstraints(minWidth: 680, maxWidth: 680),
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                    width: MediaQuery.sizeOf(context).width,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                      color: AppColors.appBlackColor,
                      image: DecorationImage(
                        image: AssetImage('assets/theme-background/new-home-bg.png'),
                        // fit: BoxFit.repeat, // Lặp lại pattern
                        fit: BoxFit.fill,
                        // opacity: 0.15,
                      ),
                    ),
                    child: Container(
                      constraints: BoxConstraints(
                        maxWidth: isTablet ? 360 : MediaQuery.sizeOf(context).width,
                        maxHeight: 360,
                      ),
                      child: Image.asset(
                        'assets/theme-background/${isTablet ? 'new-home-tl' : 'new-home'}.png',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.vertical(bottom: Radius.circular(12)),
                      color: AppColors.lightPrimaryBackgroundColor,
                      border: Border(
                        top: BorderSide(width: 1, color: AppColors.appBorderColor),
                      ),
                    ),
                    padding: EdgeInsets.fromLTRB(16, 16, 16, isTablet ? 16 : 8),
                    child: Column(
                      children: [
                        Container(
                          alignment: Alignment.center,
                          child: Text(
                            l10n.focusedHomeScreen,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                              color: AppColors.appBlackColor,
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(top: 16, bottom: 24),
                          child: Text(
                            l10n.experienceFocusedHomeScreen,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.labelNewHomeColor,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.fromLTRB(8, 10, 8, 10),
                          alignment: Alignment.center,
                          decoration: const BoxDecoration(
                            color: AppColors.labelNewHomeBGColor,
                          ),
                          child: Text(
                            l10n.youCanSwitch,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.labelNewHomeColor,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 40),
                          child: _buildWigetButtonDialogNewHome(context, isTablet: isTablet),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWigetButtonDialogNewHome(BuildContext context, {bool isTablet = false}) {
    final l10n = context.l10n;

    final focusHome = BlocConsumer<BusinessProfileEditCubit, BusinessProfileEditState>(
      listener: (context, state) async {
        if (state.status == EditStatus.error && state.errMsg != '' && state.errMsg != null) {
          AppBased.toastError(context, title: state.errMsg);
          context.read<BusinessProfileEditCubit>().onResetStatus();
        } else if (state.status == EditStatus.success) {
          await context.read<InstructionFocusedHomeCubit>().doSkipInstructionHome();
          Navigator.of(context).pop();
        }
      },
      builder: (context, state) {
        return ButtonLoading(
          height: 48,
          iconDirection: TextDirection.rtl,
          textDirection: TextDirection.rtl,
          label: l10n.tryFocusedHome,
          labelColor: Colors.white,
          isLoading: state.status == EditStatus.loading,
          circularLoadingSize: 18,
          circularStrokeColor: AppColors.appColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
          buttonBackgroundColor: AppColors.appBlackColor,
          callback: () {
            final body = {
              'isNewHomePage': true,
            };
            context.read<BusinessProfileEditCubit>().onUpdateBusinessScreenNewHome(body);
          },
        );
      },
    );
    final skip = ButtonControlWidget(
      height: 48,
      buttonText: l10n.skip,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      buttonTextColor: Colors.black,
      buttonBackgroundColor: Colors.transparent,
      onPressed: () async {
        await context.read<InstructionFocusedHomeCubit>().doSkipInstructionHome();
        Navigator.of(context).pop();
      },
    );
    if (isTablet) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(child: skip),
          Expanded(child: focusHome),
        ],
      );
    } else {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          focusHome,
          const SizedBox(height: 16),
          skip,
        ],
      );
    }
  }
}
