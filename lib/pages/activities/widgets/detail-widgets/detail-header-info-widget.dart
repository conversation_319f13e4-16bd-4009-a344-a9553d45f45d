import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/activities/activities.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customer-segment/customer-segment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/redeem/redeem.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/segment-target-offers-widget.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';

class DetailHeaderInfoWidget extends StatelessWidget {
  final ActivityDetail? activity;
  final String? timeZone;
  const DetailHeaderInfoWidget({super.key, this.activity, this.timeZone});

  @override
  Widget build(BuildContext context) {
    final userId = activity?.userId ?? activity?.user?.id ?? '';

    return BlocProvider(
      create: (context) => RedeemCubit()..searchUserById(uid: userId),
      child: DetailHeaderInfoContentWidget(activity: activity, timeZone: timeZone),
    );
  }
}

class DetailHeaderInfoContentWidget extends StatelessWidget {
  final ActivityDetail? activity;
  final String? timeZone;
  const DetailHeaderInfoContentWidget({super.key, this.activity, this.timeZone});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final String language = Localizations.localeOf(context).languageCode;

    final bool isActive = activity?.user?.isActive ?? true;
    final isPending = activity?.type == ActivitiesType.BUSINESS_EXCEEDED_QR.name || activity?.type == ActivitiesType.BUSINESS_EXCEEDED_REFERRAL.name || activity?.type == ActivitiesType.BUSINESS_EXCEEDED_JOINING.name || activity?.type == ActivitiesType.BUSINESS_EXCEEDED_POINT_CLAIMS.name;
    final userId = activity?.userId ?? activity?.user?.id ?? '';

    final user = Search(
      userId: activity?.userId ?? activity?.user?.id,
      displayName: nameDisplay(l10n, activity),
      phoneNumber: activity?.user?.phone,
      businessId: activity?.businessId,
      user: User(
        id: activity?.userId ?? activity?.user?.id,
        displayName: nameDisplay(l10n, activity),
        email: activity?.user?.email,
        phone: activity?.user?.phone,
      ),
    );
    return Container(
      padding: const EdgeInsets.only(bottom: 16, top: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: InkWell(
                        onTap: () {
                          final data = {'user': user};
                          if (user.userId != null && user.userId != '') {
                            AppBased.go(
                              context,
                              AppRoutes.redeemPoint,
                              args: ScreenArguments(
                                data: data,
                                type: 'activities-page',
                              ),
                              onChanged: (data) {},
                            );
                          }
                        },
                        child: RichText(
                          textAlign: TextAlign.left,
                          text: TextSpan(
                            text: null,
                            style: const TextStyle(
                              fontSize: 16,
                              color: AppColors.appBlackColor,
                              fontWeight: FontWeight.w600,
                            ),
                            children: [
                              TextSpan(
                                text: isActive ? nameDisplay(l10n, activity) ?? l10n.guest : l10n.guestDelete,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: AppColors.appBlackColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              if (isActive && user.userId != null && user.userId != '')
                                WidgetSpan(
                                  child: Container(
                                    margin: const EdgeInsets.only(left: 4),
                                    width: 18,
                                    height: 18,
                                    child: SvgPicture.asset(
                                      'assets/svgs/open-link.svg',
                                      colorFilter: const ColorFilter.mode(
                                        AppColors.appResendColor,
                                        BlendMode.srcIn,
                                      ),
                                      width: 16,
                                      height: 16,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    if (!isPending && isActive)
                      Transform.translate(
                        offset: const Offset(8, 0),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(4),
                          onTap: () => {
                            if (user.userId != null && user.userId != '')
                              AppBased.go(
                                context,
                                AppRoutes.customerHistory,
                                args: ScreenArguments(
                                  data: {'user': user},
                                ),
                              )
                          },
                          child: Container(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Row(
                              children: [
                                Text(
                                  l10n.history,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.appResendColor,
                                  ),
                                ),
                                SvgPicture.asset(
                                  'assets/svgs/arrow-right.svg',
                                  width: 20,
                                  height: 20,
                                  colorFilter: const ColorFilter.mode(AppColors.appResendColor, BlendMode.srcIn),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                BlocBuilder<CustomerSegmentCubit, CustomerSegmentState>(
                  builder: (context, state) {
                    if (state.status == CustomerSegmentStatus.Success) {
                      final typeByID = state.selectedCustomer(userId)?.type;
                      if (typeByID != null) {
                        return SegmentTargetOffersWidget(
                          margin: const EdgeInsets.only(top: 4, bottom: 2),
                          title: typeByID,
                          uid: userId,
                          titleColor: AppColors.homeShowQRBGColor,
                          fontWeightTitle: FontWeight.w500,
                        );
                      }
                    }
                    return const SizedBox.shrink();
                  },
                ),
                if (isActive) ...[
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      AppValidations.formatPhoneNumber(phoneDisplay(activity) ?? ''),
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.codeScanNameColor,
                                      ),
                                    ),
                                    Container(
                                      margin: const EdgeInsets.only(
                                        left: 8,
                                      ),
                                      child: InkWell(
                                        onTap: () {
                                          Clipboard.setData(
                                            ClipboardData(
                                              text: AppValidations.detectPhone(
                                                    phoneDisplay(activity),
                                                  )?.nsn ??
                                                  '',
                                            ),
                                          ).then(
                                            (_) {
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    l10n.copied,
                                                    style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight: FontWeight.w400,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          );
                                        },
                                        child: SvgPicture.asset(
                                          'assets/svgs/copy.svg',
                                          width: 24,
                                          height: 24,
                                          colorFilter: const ColorFilter.mode(
                                            AppColors.iconColor,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                Container(
                                  width: 1,
                                  height: 24,
                                  color: AppColors.appBorderColor,
                                ),
                                BlocBuilder<RedeemCubit, RedeemState>(
                                  builder: (context, state) {
                                    // if (state.status == RedeemStatus.Success) {
                                    final userDate = state.searchUser;
                                    final num totalPoints = num.parse(userDate?.totalPoints ?? '0');
                                    final pointTitle = (totalPoints > 1 || totalPoints < -1) ? l10n.points : l10n.point;
                                    AppLog.e('totalPoints: $totalPoints');
                                    return Text(
                                      '${AppValidations.getTotalPrice(totalPoints, language: language)} $pointTitle',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.appBlackColor,
                                      ),
                                    );
                                    // }
                                    // return const SizedBox.shrink();
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }

  String? nameDisplay(AppLocalizations l10n, ActivityDetail? item) {
    try {
      if (item?.user != null && item?.user?.displayName != null && item?.user?.displayName != '') {
        return item?.user?.displayName;
      } else if (item?.metadata?['customerName'] != null) {
        return item?.metadata?['customerName'].toString() ?? l10n.guest;
      }
      return l10n.guest;
    } catch (e) {
      return l10n.guest;
    }
  }

  String? phoneDisplay(ActivityDetail? item) {
    try {
      if (item?.user != null && item?.user?.phone != null && item?.user?.phone != '') {
        return item?.user?.phone;
      } else if (item?.metadata?['customerPhone'] != null) {
        return item?.metadata?['customerPhone'].toString() ?? 'N/A';
      }
      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }
}
