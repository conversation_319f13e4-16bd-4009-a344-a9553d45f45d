// ignore_for_file: avoid_dynamic_calls, unnecessary_statements, strict_raw_type

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/activities/activities.cubit.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/activities/widgets/@activities-widgets.dart';

class ActivitiesItemWidget extends StatelessWidget {
  final Activiti? item;
  final bool isBoder;
  final ValueChanged<Activiti>? onChanged;
  const ActivitiesItemWidget({super.key, required this.item, this.onChanged, this.isBoder = false});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final bool isActive = item?.isActive ?? true;
    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    final String language = Localizations.localeOf(context).languageCode;

    num rewardPoints = num.parse(item?.points ?? '0');
    final isPenDing = item?.type == ActivitiesType.BUSINESS_EXCEEDED_QR.name || item?.type == ActivitiesType.BUSINESS_EXCEEDED_REFERRAL.name || item?.type == ActivitiesType.BUSINESS_EXCEEDED_JOINING.name || item?.type == ActivitiesType.BUSINESS_EXCEEDED_POINT_CLAIMS.name;
    if (isPenDing) {
      rewardPoints = num.parse(item?.pendingPoints ?? '0');
    }

    Widget widget = const SizedBox.shrink();
    if (item?.type == ActivitiesType.CALL_BUTTON_CHECKED_IN.name) {
      final points = item?.points ?? '0';
      final pointsText = num.parse(points) > 1 ? l10n.points : l10n.point;
      final pointsNumber = AppValidations.getTotalPrice(num.parse(points), language: language);
      widget = num.parse(points) > 0
          ? Text(
              '+$pointsNumber $pointsText',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                color: formatTypeColors(item?.type ?? ''),
              ),
            )
          : const SizedBox.shrink();
    } else if (item?.type == ActivitiesType.CUSTOMER_PAYMENT.name) {
      final cost = CurrencyHelper.convertMoney(
        item?.cost ?? '0',
        code: item?.costCurrency ?? '',
      );
      widget = Text(
        cost,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: formatTypeColors(item?.type ?? ''),
        ),
      );
    } else if (item?.type != ActivitiesType.OFFER_REDEEMED.name && item?.type != ActivitiesType.OFFER_CLAIMED.name && item?.type != ActivitiesType.CALL_BUTTON_CHECKED_IN.name) {
      widget = Text(
        // '${isPenDing ? '' : rewardPoints > 0 ? '+' : ''}${AppValidations.getTotalPrice(rewardPoints, language: language)} ${(rewardPoints > 1 || rewardPoints < -1) ? l10n.points : l10n.point}',
        '${isPenDing ? '' : rewardPoints > 0 && item?.type != ActivitiesType.VOID.name ? '+' : ''}${AppValidations.getTotalPrice(rewardPoints, language: language)} ${(rewardPoints > 1 || rewardPoints < -1) ? l10n.points : l10n.point}',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: formatTypeColors(item?.type ?? ''),
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap: () => AppBased.openShowModalBottomSheetAnn(
          context,
          enableDrag: true,
          isSetHeightRotateFactor: true,
          useSafeArea: true,
          widget: ActivitiesDetailWidget(
            activiti: item,
            onChanged: (Activiti? value) {
              if (value != null) {
                if (onChanged == null) {
                  item?.type = value.type;
                  item?.points = value.points;
                  item?.metadata == value.metadata;
                  context.read<ActivitiesCubit>().updateItemActivies(item!).then((value) {});
                } else {
                  onChanged?.call(value);
                }
              }
            },
          ),
          onChanged: (value) {
            if (value == true) {
              context.read<ActivitiesCubit>().getActivities(isLoading: false);
            }
          },
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
          ),
          decoration: BoxDecoration(
            border: isBoder == false
                ? const Border(
                    bottom: BorderSide(
                      width: 1,
                      color: AppColors.appBorderColor,
                    ),
                  )
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: item?.type == ActivitiesType.CALL_BUTTON_CHECKED_IN.name
                              ? Text(
                                  isActive ? nameDisplayCheckedIn(l10n, item) ?? '' : l10n.guestDelete,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.appBlackColor,
                                  ),
                                )
                              : Text(
                                  isActive ? nameDisplay(l10n, item) ?? '' : l10n.guestDelete,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.appBlackColor,
                                  ),
                                ),
                        ),
                        Row(
                          children: [
                            ActivitiesTypeWidget(
                              type: item?.type ?? '',
                              qrId: item?.qrId ?? '',
                              rewardsOnOrderId: item?.rewardsOnOrderId,
                            ),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(left: 4.0),
                                child: item?.type == ActivitiesType.CALL_BUTTON_CHECKED_IN.name
                                    ? Text(
                                        isActive ? AppValidations.formatPhoneNumber(phoneDisplayCheckedIn(item) ?? '') : '',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: AppColors.codeScanNameColor,
                                        ),
                                      )
                                    : Text(
                                        isActive ? AppValidations.formatPhoneNumber(phoneDisplay(item) ?? '') : '',
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          color: AppColors.codeScanNameColor,
                                        ),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          DateTimeHelper.timeFormat(context, item?.createdAt, timeZone: timeZone),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.codeScanNameColor,
                          ),
                        ),
                      ),
                      Container(child: widget),
                    ],
                  ),
                ],
              ),
              if (item?.offer != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    item?.offer?.title ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.appBlackColor,
                    ),
                  ),
                ),
              if (item?.type == ActivitiesType.OFFER_REDEEMED.name && item!.offersOnTransaction!.isNotEmpty)
                Container(
                  padding: const EdgeInsets.only(top: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      // for (int i = 0; i <= item!.offersOnTransaction!.length - 1; i++) ...{
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.only(right: 10),
                          alignment: Alignment.topLeft,
                          child: Text(
                            item?.offersOnTransaction?[0].offerTitle ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: AppColors.appBlackColor.withValues(alpha: .56),
                            ),
                          ),
                        ),
                      ),
                      // },
                      if (item!.offersOnTransaction!.length > 1) ...{
                        Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(right: 2),
                              child: SvgPicture.asset(
                                'assets/svgs/add.svg',
                                width: 16,
                                height: 16,
                                colorFilter: const ColorFilter.mode(
                                  AppColors.appResendColor,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                            Text(
                              '${item!.offersOnTransaction!.length - 1} ${l10n.more}',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: AppColors.appResendColor,
                              ),
                            )
                          ],
                        )
                      }
                    ],
                  ),
                ),
              if (item?.type == ActivitiesType.CALL_BUTTON_CHECKED_IN.name)
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.homeBorderColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        margin: const EdgeInsets.only(right: 8),
                        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
                        child: Text(
                          l10n.callButton.toUpperCase(),
                          style: const TextStyle(
                            fontSize: 11,
                            color: Colors.black,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      if (item?.metadata != null && item?.metadata?['tableName'] != null && item?.metadata?['tableName'] != '')
                        Text(
                          item?.metadata?['tableName'].toString() ?? '',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.codeScanNameColor,
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color formatTypeColors(String type) {
    if (type == ActivitiesType.CUSTOMER_PAYMENT.name) {
      return AppColors.darkPrimaryBackgroundColor;
    } else if (type == ActivitiesType.DEDUCTED_POINTS.name || type == ActivitiesType.REWARD_REDEEMED.name || type == ActivitiesType.VOID.name) {
      return AppColors.activitiesEarnedPointsColor;
    } else if (type == ActivitiesType.BUSINESS_EXCEEDED_QR.name || type == ActivitiesType.BUSINESS_EXCEEDED_REFERRAL.name || type == ActivitiesType.BUSINESS_EXCEEDED_JOINING.name || type == ActivitiesType.BUSINESS_EXCEEDED_POINT_CLAIMS.name) {
      return AppColors.activitiesPendingPointsColor.withValues(alpha: .4);
    } else {
      return AppColors.activitiesEarnedPointsColor;
    }
  }

  String? phoneDisplay(Activiti? item) {
    try {
      if (item?.phone != null && item?.phone != '') {
        return item?.phone;
      } else if (item?.metadata?['customerPhone'] != null) {
        return item?.metadata?['customerPhone'].toString() ?? 'N/A';
      }
      return 'N/A';
    } catch (e) {
      return 'N/A';
    }
  }

  String? nameDisplay(AppLocalizations l10n, Activiti? item) {
    try {
      if (item?.displayName != null && item?.displayName != '') {
        return item?.displayName;
      } else if (item?.metadata?['customerName'] != null) {
        return item?.metadata?['customerName'].toString() ?? l10n.guest;
      }
      return l10n.guest;
    } catch (e) {
      return l10n.guest;
    }
  }

  String? nameDisplayCheckedIn(AppLocalizations l10n, Activiti? item) {
    if (item?.metadata != null && item?.metadata?['customerDisplayName'] != null && item?.metadata?['customerDisplayName'].trim() != '') {
      return item?.metadata!['customerDisplayName'].toString();
    }
    return l10n.guest;
  }

  String? phoneDisplayCheckedIn(Activiti? item) {
    if (item?.metadata != null && item?.metadata?['customerPhone'] != null && item?.metadata?['customerPhone'] != '') {
      return item?.metadata?['customerPhone'].toString();
    }
    return 'N/A';
  }
}
