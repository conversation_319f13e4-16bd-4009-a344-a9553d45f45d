part of 'multiple-account.cubit.dart';

enum MultipleAccountStatus { Initial, Error, Loading, Success }

class MultipleAccountState {
  final String? errorMsg;
  final MultipleAccountStatus? status;
  final List<Business>? data;

  MultipleAccountState({
    this.status = MultipleAccountStatus.Initial,
    this.errorMsg,
    this.data,
  });

  MultipleAccountState copyWith({
    MultipleAccountStatus? status,
    String? errorMsg,
    List<Business>? data,
    int? currentPage,
  }) {
    return MultipleAccountState(
      status: status ?? this.status,
      errorMsg: errorMsg ?? this.errorMsg,
      data: data ?? this.data,
    );
  }

  @override
  String toString() {
    return 'MultipleAccountState  $status';
  }
}
