// ignore_for_file: avoid_catching_errors

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@const/config.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth.model.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multiple-home-list/multiple-home-list.cubit.dart';
import 'package:stickyqrbusiness/@share/apis.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'multiple-account.service.dart';
part 'multiple-account.state.dart';

class MultipleAccountCubit extends Cubit<MultipleAccountState> {
  MultipleAccountCubit() : super(MultipleAccountState());

  void onResetStatus() {
    emit(state.copyWith(status: MultipleAccountStatus.Initial, errorMsg: ''));
  }

  Future<List<Business>?> geMultipleAccount() async {
    try {
      emit(state.copyWith(status: MultipleAccountStatus.Loading));
      final rep = await MultipleAccountService().geMultipleAccount();

      if (rep != null) {
        emit(
          state.copyWith(status: MultipleAccountStatus.Success, data: rep),
        );
      } else {
        emit(state.copyWith(status: MultipleAccountStatus.Error, data: null));
      }

      return rep;
    } on AppErrorResponse catch (e) {
      AppLog.e(
        'AppErrorResponse: ${e.message}}',
        logType: AppLoggerType.FUNCTION,
        logValue: 'MultipleAccountCubit',
        classParent: 'MultipleAccountCubitCubit',
        obj: e,
      );
      emit(state.copyWith(status: MultipleAccountStatus.Error, errorMsg: e.message));
      return null;
    }
  }

  Future<Auth?> doSwitchAccount(String bid) async {
    try {
      emit(state.copyWith(status: MultipleAccountStatus.Loading));
      final rep = await MultipleAccountService().doSwitchAccount(bid);

      if (rep != null) {
        if (rep.accessToken != null && rep.accessToken != '') {
          try {
            await AppLocalStorage.deleteLocal(AppConfig.localAccessToken.toString());
            AppLocalStorage.saveAuthModel(rep);
            final List<MultipleHome>? homes = await MultipleHomeListService().getMultipleHome();
            if (homes != null && homes.isNotEmpty) {
              await sortList(homes);
              await AppLocalStorage.saveBusinessMultipleHomeWidget(homes);
            } else {
              final homeDefault = MultipleHome(
                isExistDB: false,
                settings: MultipleHomeSettings(
                  widgets: [
                    MultipleHomeWidget(
                      id: 'engagement',
                      index: 0,
                      type: 'module',
                    ),
                    MultipleHomeWidget(
                      id: 'customers',
                      index: 1,
                    )
                  ],
                  appearance: Appearance(theme: 'theme-7'),
                ),
              );
              await AppLocalStorage.saveBusinessMultipleHomeWidget([homeDefault]);
            }
          } catch (e) {}

          emit(state.copyWith(status: MultipleAccountStatus.Success));
        } else {
          emit(state.copyWith(status: MultipleAccountStatus.Error, errorMsg: 'Failed to switch account'));
        }
      } else {
        emit(state.copyWith(status: MultipleAccountStatus.Error));
      }
      return rep;
    } on AppErrorResponse catch (e) {
      AppLog.e(
        'Error: ${e.message}',
        logType: AppLoggerType.FUNCTION,
        logValue: 'login',
        classParent: 'LoginWithPasswordCubit',
        obj: e,
      );
      emit(state.copyWith(status: MultipleAccountStatus.Error, errorMsg: e.message));
      return null;
    }
  }

  Future<List<MultipleHome>> sortList(List<MultipleHome> list) async {
    list.sort((MultipleHome a, MultipleHome? b) {
      if (a.isDefault! && !b!.isDefault!) {
        return -1;
      } else if (!a.isDefault! && b!.isDefault!) {
        return 1;
      } else {
        return 0;
      }
    });
    return list;
  }
}
