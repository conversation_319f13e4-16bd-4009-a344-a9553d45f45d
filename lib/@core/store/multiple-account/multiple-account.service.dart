part of 'multiple-account.cubit.dart';

class MultipleAccountService {
  Future<List<Business>?> geMultipleAccount() async {
    try {
      final response = await AppHttp.request(
        AppAPI.multipleAccount,
        method: APIRequestMethod.GET,
        queryParams: {},
      );
      AppLog.e('Res: $response');
      AppBased.doAuditLogging(
        pageName: 'geMultipleAccount',
        pageUrl: 'profile-businesses',
        pageAction: 'doMultipleAccount',
        metadata: {},
      );
      final data = (response.data as List).map((e) => Business.fromJson(e as Map<String, dynamic>)).toList();
      return data;
    } on DioException catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }

  Future<Auth?> doSwitchAccount(String id) async {
    try {
      final response = await AppHttp.request(
        AppAPI.doSwitchAccount,
        method: APIRequestMethod.POST,
        body: {'businessId': id},
      );
      AppLog.e('Res: $response');
      AppBased.doAuditLogging(
        pageName: 'account',
        pageUrl: 'profile-businesses',
        pageAction: 'doSwitchAccount',
        metadata: {},
      );

      return Auth.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      AppError.dioResponseError(e);
    }
    return null;
  }
}
