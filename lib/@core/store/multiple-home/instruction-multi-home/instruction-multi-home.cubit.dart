// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';

part 'instruction-multi-home.state.dart';

class InstructionMultiHomeCubit extends Cubit<InstructionMultiHomeState> {
  InstructionMultiHomeCubit() : super(const InstructionMultiHomeState());

  void onChangedStep(InstructionMultiHomeStep value) {
    emit(
      state.copyWith(
        step: value,
        // status: InstructionMultiHomeStatus.FirstCustomize,
      ),
    );
  }

  void onChangedNextStep(InstructionMultiHomeStep value) {
    emit(
      state.copyWith(
        step: value,
        status: InstructionMultiHomeStatus.FirstCustomize,
      ),
    );
  }

  void onChangedEnd() {
    AppLocalStorage.saveFirsrMultiHome(true);
    emit(
      state.copyWith(status: InstructionMultiHomeStatus.Last),
    );
  }

  Future<bool> doCheckFirstHome() async {
    final isFirstHome = await AppLocalStorage.getFirsrMultiHome() ?? false;
    if (!isFirstHome) {
      emit(state.copyWith(status: InstructionMultiHomeStatus.First));
    } else {
      emit(state.copyWith(status: InstructionMultiHomeStatus.Last));
    }
    return isFirstHome;
  }

  Future<void> doSkipInstructionHome() async {
    await AppLocalStorage.saveFirsrMultiHome(true);
    emit(state.copyWith(status: InstructionMultiHomeStatus.Last));
  }
}
