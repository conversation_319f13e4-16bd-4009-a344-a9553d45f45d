part of 'instruction-focused-home.cubit.dart';

enum InstructionFocusedHomeStatus { First, FirstCustomize, Last }

class InstructionFocusedHomeState {
  final InstructionFocusedHomeStatus status;
  final String? errorMsg;

  const InstructionFocusedHomeState({
    this.status = InstructionFocusedHomeStatus.First,
    this.errorMsg,
  });

  InstructionFocusedHomeState copyWith({
    InstructionFocusedHomeStatus? status,
    String? errorMsg,
  }) {
    return InstructionFocusedHomeState(
      status: status ?? this.status,
      errorMsg: errorMsg ?? this.errorMsg,
    );
  }

  @override
  String toString() {
    return 'InstructionFocusedHomeState  $status';
  }
}
