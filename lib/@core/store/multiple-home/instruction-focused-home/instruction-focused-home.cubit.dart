// ignore_for_file: avoid_catching_errors, avoid_positional_boolean_parameters

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

part 'instruction-focused-home.state.dart';

class InstructionFocusedHomeCubit extends Cubit<InstructionFocusedHomeState> {
  InstructionFocusedHomeCubit() : super(const InstructionFocusedHomeState());

  Future<bool> doCheckFirstHome() async {
    final isFirstHome = await AppLocalStorage.getFirsrNewHome() ?? false;
    final isFirstMultipleHome = await AppLocalStorage.getFirsrMultiHome() ?? false;
    AppLog.e('isFirstHome: $isFirstHome');
    if (!isFirstHome && isFirstMultipleHome) {
      emit(state.copyWith(status: InstructionFocusedHomeStatus.First));
    } else {
      emit(state.copyWith(status: InstructionFocusedHomeStatus.Last));
    }
    return isFirstHome;
  }

  Future<void> doSkipInstructionHome({Duration? expiry}) async {
    // await AppLocalStorage.saveFirsrNewHome(true, expiry: const Duration(seconds: 20));
    await AppLocalStorage.saveFirsrNewHome(true, expiry: const Duration(days: 7));
    emit(state.copyWith(status: InstructionFocusedHomeStatus.Last));
  }
}
