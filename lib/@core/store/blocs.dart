import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@core/store/ai-rewards/ai-rewards.cubit.dart';
import 'package:stickyqrbusiness/@core/store/app-themes/appthemes_cubit.dart';
import 'package:stickyqrbusiness/@core/store/app/app_bloc.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/business-check-pro-plan/business-check-pro-plan.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-features/business-features.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile-autocomplete-map-address/address-selected.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile-autocomplete-map-address/business-profile-autocomplete-map-address.bloc.dart';
import 'package:stickyqrbusiness/@core/store/business-profile-map-address/business-profile-map-address.bloc.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile-edit.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-roles/business-roles.cubit.dart';
import 'package:stickyqrbusiness/@core/store/business-signup-autocomplete-map-address/business-signup-autocomplete-map-address.bloc.dart';
import 'package:stickyqrbusiness/@core/store/business-signup-autocomplete-map-address/signup-address-selected.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-ably/call-button-ably.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request-orders/call-button-count-down-request-orders.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-count-down-request/call-button-count-down-request.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-detail/call-button-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-help-me-choose/call-button-help-me-choose.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button-sound/call-button-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button-check-realtime/call-button-check-realtime.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/call-button.cubit.dart';
import 'package:stickyqrbusiness/@core/store/call-button/cb-select-table/cb-select-table.cubit.dart';
import 'package:stickyqrbusiness/@core/store/change-plan-features-info-content/change-plan-features-info-content.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support-bubble/chat-ai-support-bubble.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/chat-messages.cubit.dart';
import 'package:stickyqrbusiness/@core/store/chat-ai-support/chat-messages.service.dart';
import 'package:stickyqrbusiness/@core/store/chat-support-count-badge/chat-support-count-badge.cubit.dart';
import 'package:stickyqrbusiness/@core/store/check-business/check_business.cubit.dart';
import 'package:stickyqrbusiness/@core/store/check-enable-customer-pay/check-enable-customer-pay.cubit.dart';
import 'package:stickyqrbusiness/@core/store/custom-bubble/custom-bubble.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customer-order-payment-send-phone/customer-order-payment-send-phone.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customer-order-payment/customer-order-payment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customer-payment/customer-payment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customers/customers_cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-customer-receipt/customize-customer-receipt.cubit.dart';
import 'package:stickyqrbusiness/@core/store/customize-kitchen-ticket/customize-kitchen-ticket.cubit.dart';
import 'package:stickyqrbusiness/@core/store/delivery-quote/delivery-quote.cubit.dart';
import 'package:stickyqrbusiness/@core/store/help-me-choose/help-me-choose.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-default/home-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-screen-tabs/home-screen-tabs.cubit.dart';
import 'package:stickyqrbusiness/@core/store/home-theme/home-theme.cubit.dart';
import 'package:stickyqrbusiness/@core/store/labels/labels.cubit.dart';
import 'package:stickyqrbusiness/@core/store/login-not-password/login-not-password.cubit.dart';
import 'package:stickyqrbusiness/@core/store/media/media.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-account/multiple-account.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/instruction-focused-home/instruction-focused-home.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/instruction-multi-home/instruction-multi-home.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multi-home-page-view-current/multi-home-page-view-current.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multiple-home-list/multiple-home-list.cubit.dart';
import 'package:stickyqrbusiness/@core/store/multiple-home/multiple-home-set-default/multiple-home-set-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/network/network.cubit.dart';
import 'package:stickyqrbusiness/@core/store/new-delivery/new-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/notification-fcm/notification_cubit.dart';
import 'package:stickyqrbusiness/@core/store/notifications/notifications_cubit.dart';
import 'package:stickyqrbusiness/@core/store/offer-redeem/offer-redeem.cubit.dart';
import 'package:stickyqrbusiness/@core/store/offers-active/offers-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/offers-available/offers-available.cubit.dart';
import 'package:stickyqrbusiness/@core/store/onboarding/onboarding.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-change-tab/order-active-change-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-time-pickup-tab/order-active-time-pickup-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-auto-reload/order-detail-auto-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-for-print/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail-time-line/order-history-detail-time-line.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-history-detail/order-history-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-new-notification/order-new-notification.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-active-reload/ordering-active-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-delivery/ordering-integration-settings-delivery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-points-update/ordering-integration-settings-points-update.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-points/ordering-integration-settings-points.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-target-offers/ordering-integration-settings-target-offers.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-vouchers-update/ordering-integration-settings-vouchers-update.cubit.dart';
import 'package:stickyqrbusiness/@core/store/ordering-integration-settings-vouchers/ordering-integration-settings-vouchers.cubit.dart';
import 'package:stickyqrbusiness/@core/store/orders-active/orders-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/pending-approvals-total/pending-approvals-total.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims-sound/point-claims-sound.cubit.dart';
import 'package:stickyqrbusiness/@core/store/point-claims/point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/print-offer/print_offer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/print-reward/print.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-label-set-default/printers-label-set-default.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-local/printers-local.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers/get-printers.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers/update-printer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/process-raw-data/process-raw-data.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-statistics/qr_statistics.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-tags/add-edit-tag-points.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-tags/qr-tags-points.cubit.dart';
import 'package:stickyqrbusiness/@core/store/qr-tags/qr-tags-search.cubit.dart';
import 'package:stickyqrbusiness/@core/store/resend/resend.cubit.dart';
import 'package:stickyqrbusiness/@core/store/rewards-active/rewards-active.cubit.dart';
import 'package:stickyqrbusiness/@core/store/role-permission/role-permission.cubit.dart';
import 'package:stickyqrbusiness/@core/store/search-address/search-address.cubit.dart';
import 'package:stickyqrbusiness/@core/store/search-customer-by-id/search_customer_by_id.cubit.dart';
import 'package:stickyqrbusiness/@core/store/signage-counter-card-call-button-edit-content/signage-counter-card-call-button-edit-content.cubit.dart';
import 'package:stickyqrbusiness/@core/store/signage-counter-card-call-button/signage-counter-card.cubit.dart';
import 'package:stickyqrbusiness/@core/store/signage-rewards/signage-rewards.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-customer/add-edit-tag-customer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-customer/tags-customer-search.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-customer/tags-customer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-offer/add-edit-tag.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-offer/tags-offer-search.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-offer/tags-offer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-point-claims/add-edit-tag-point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-point-claims/tags-point-claims-search.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-point-claims/tags-point-claims.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-staff/add-edit-tag-staff.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-staff/tags-staff-search.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tags-staff/tags-staff.cubit.dart';
import 'package:stickyqrbusiness/@core/store/target-offer-detail-customer/target-offer-detail-customer.cubit.dart';
import 'package:stickyqrbusiness/@core/store/tutorial-target/tutorial-target.cubit.dart';
import 'package:stickyqrbusiness/@core/store/update-alert/update-alert.cubit.dart';
import 'package:stickyqrbusiness/@core/store/user-profile/user-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/voucher-customers-claimed-redeemed/voucher-customers-claimed-redeemed.cubit.dart';
import 'package:stickyqrbusiness/@core/store/vouchers-calendar/voucher-calendar.cubit.dart';

/// Ở đây chứa các blocs sẻ khai báo chính
/// Tổ chức dạng những
class AppBlocs {
  ///
  /// Khai báo blocs sử dụng cho global như fcm, auth
  ///
  static List<BlocProvider> globalBlocs() {
    return [
      BlocProvider<AppBloc>(create: (context) => AppBloc()),
      BlocProvider<AuthBloc>(create: (context) => AuthBloc()..add(AuthStarted())),
      BlocProvider<AppThemesCubit>(create: (context) => AppThemesCubit()),
      BlocProvider<BusinessProfileCubit>(create: (context) => BusinessProfileCubit()),
      BlocProvider<NotificationsCubit>(create: (context) => NotificationsCubit()),
      BlocProvider<PrintersLocalCubit>(create: (context) => PrintersLocalCubit()),
      BlocProvider<CheckBusinessCubit>(create: (context) => CheckBusinessCubit()),
      BlocProvider<LoginNotPasswordCubit>(create: (context) => LoginNotPasswordCubit()),
      BlocProvider<MediaCubit>(create: (context) => MediaCubit()),
      BlocProvider<PrintCubit>(create: (context) => PrintCubit()),
      BlocProvider<SearchCustomerByIDCubit>(create: (context) => SearchCustomerByIDCubit()),
      BlocProvider<ResendCubit>(create: (context) => ResendCubit()),
      BlocProvider<GetPrintersCubit>(create: (context) => GetPrintersCubit()),
      BlocProvider<UpdatePrintersCubit>(create: (context) => UpdatePrintersCubit()),
      BlocProvider<LabelsCubit>(lazy: false, create: (context) => LabelsCubit()),
      BlocProvider<UserProfileCubit>(create: (context) => UserProfileCubit()),
      BlocProvider<NotificationCubit>(create: (context) => NotificationCubit()),
      BlocProvider<QrStatisticsCubit>(create: (context) => QrStatisticsCubit()),
      BlocProvider<BusinessProfileAddressBloc>(create: (context) => BusinessProfileAddressBloc()),
      BlocProvider<UpdateAlertCubit>(create: (context) => UpdateAlertCubit()),
      BlocProvider<SignageRewardsCubit>(create: (context) => SignageRewardsCubit()),
      BlocProvider<PendingApprovalsTotalCubit>(create: (context) => PendingApprovalsTotalCubit()),
      BlocProvider<CustomersCubit>(create: (context) => CustomersCubit()),
      BlocProvider<BusinessProfileEditCubit>(create: (context) => BusinessProfileEditCubit()),
      BlocProvider<NetworkCubit>(lazy: false, create: (context) => NetworkCubit()..onChangedStatus(NetworkStatus.ONLINE)),
      BlocProvider<OnboardingCubit>(lazy: false, create: (context) => OnboardingCubit()),
      BlocProvider<AIRewardsCubit>(lazy: false, create: (context) => AIRewardsCubit()),
      BlocProvider<TutorialTargetCubit>(lazy: false, create: (context) => TutorialTargetCubit()),
      BlocProvider<BusinessProfileAutocompleteAddressBloc>(lazy: false, create: (context) => BusinessProfileAutocompleteAddressBloc()),
      BlocProvider<AddressSelectedCubit>(create: (context) => AddressSelectedCubit()),
      BlocProvider<OfferRedeemCubit>(create: (context) => OfferRedeemCubit()),
      BlocProvider<QrTagsPointsCubit>(create: (context) => QrTagsPointsCubit()),
      BlocProvider<QrTagsSearchCubit>(create: (context) => QrTagsSearchCubit()),
      BlocProvider<AddEditTagPointsCubit>(create: (context) => AddEditTagPointsCubit()),
      BlocProvider<OffersAvailableCubit>(create: (context) => OffersAvailableCubit()),
      BlocProvider<OffersActiveCubit>(create: (context) => OffersActiveCubit()),
      BlocProvider<PrintOfferCubit>(create: (context) => PrintOfferCubit()..doCheckLabelOfferPrinter()),
      BlocProvider<PrinterLabelSetDefaultCubit>(create: (context) => PrinterLabelSetDefaultCubit()),
      BlocProvider<TagsOfferCubit>(create: (context) => TagsOfferCubit()),
      BlocProvider<TagsOfferSearchCubit>(create: (context) => TagsOfferSearchCubit()),
      BlocProvider<AddEditTagOfferCubit>(create: (context) => AddEditTagOfferCubit()),
      BlocProvider<CustomerPaymentCubit>(create: (context) => CustomerPaymentCubit()),
      BlocProvider<CheckEnableCustomerPayCubit>(create: (context) => CheckEnableCustomerPayCubit()),
      BlocProvider<PointClaimsCubit>(create: (context) => PointClaimsCubit()),
      BlocProvider<HomeThemeCubit>(create: (context) => HomeThemeCubit()),
      BlocProvider<PointClaimsSoundCubit>(create: (context) => PointClaimsSoundCubit()),
      BlocProvider<AddEditTagPointClaimsCubit>(create: (context) => AddEditTagPointClaimsCubit()),
      BlocProvider<TagsPointClaimsSearchCubit>(create: (context) => TagsPointClaimsSearchCubit()),
      BlocProvider<TagsPointClaimsCubit>(create: (context) => TagsPointClaimsCubit()),
      BlocProvider<CallButtonCubit>(create: (context) => CallButtonCubit()),
      BlocProvider<CallButtonDetailCubit>(create: (context) => CallButtonDetailCubit()),
      BlocProvider<RewardsActiveCubit>(create: (context) => RewardsActiveCubit()),
      BlocProvider<CallButtonAblyCubit>(create: (context) => CallButtonAblyCubit()),
      BlocProvider<CallButtonSoundCubit>(create: (context) => CallButtonSoundCubit()),
      BlocProvider<CallButtonCountdownRequestCubit>(create: (context) => CallButtonCountdownRequestCubit()),
      BlocProvider<HomeDefaultWidgetCubit>(create: (context) => HomeDefaultWidgetCubit()),
      BlocProvider<RolePermissionCubit>(create: (context) => RolePermissionCubit()),
      BlocProvider<TagsStaffCubit>(create: (context) => TagsStaffCubit()),
      BlocProvider<AddEditTagStaffCubit>(create: (context) => AddEditTagStaffCubit()),
      BlocProvider<TagsStaffSearchCubit>(create: (context) => TagsStaffSearchCubit()),
      BlocProvider<TagsCustomerCubit>(create: (context) => TagsCustomerCubit()),
      BlocProvider<AddEditTagCustomerCubit>(create: (context) => AddEditTagCustomerCubit()),
      BlocProvider<TagsCustomerSearchCubit>(create: (context) => TagsCustomerSearchCubit()),
      BlocProvider<BusinessRolesCubit>(create: (context) => BusinessRolesCubit()),
      BlocProvider<VoucherCustomersClaimedRedeemedCubit>(create: (context) => VoucherCustomersClaimedRedeemedCubit()),
      BlocProvider<CallButtonCheckRealtimeCubit>(create: (context) => CallButtonCheckRealtimeCubit()),
      BlocProvider<CallButtonHelpMeChooseCubit>(create: (context) => CallButtonHelpMeChooseCubit()),
      BlocProvider<ProcessRawDataCubit>(create: (context) => ProcessRawDataCubit()),
      BlocProvider<MultipleHomeListCubit>(create: (context) => MultipleHomeListCubit()),
      BlocProvider<MultipleHomeSetDefaultCubit>(create: (context) => MultipleHomeSetDefaultCubit()),
      BlocProvider<VoucherCalendarCubit>(create: (context) => VoucherCalendarCubit()),
      BlocProvider<HelpMeChooseCubit>(create: (context) => HelpMeChooseCubit()),
      BlocProvider<InstructionMultiHomeCubit>(create: (context) => InstructionMultiHomeCubit()),
      BlocProvider<MultiHomePageViewCurrentCubit>(create: (context) => MultiHomePageViewCurrentCubit()),
      BlocProvider<BusinessCheckProPlanCubit>(create: (context) => BusinessCheckProPlanCubit()..getHidePro()),
      BlocProvider<BusinessFeaturesCubit>(create: (context) => BusinessFeaturesCubit()),
      BlocProvider<ChangePlanFeaturesInfoCubit>(create: (context) => ChangePlanFeaturesInfoCubit()),
      BlocProvider<OrdersActiveCubit>(create: (context) => OrdersActiveCubit()),
      BlocProvider<OrderNewNotificationCubit>(create: (context) => OrderNewNotificationCubit()),
      BlocProvider<OrderActiveTimePickupTabCubit>(create: (context) => OrderActiveTimePickupTabCubit()),
      BlocProvider<OrderActiveChangeTabCubit>(create: (context) => OrderActiveChangeTabCubit()),
      BlocProvider<OrderHistoryDetailCubit>(create: (context) => OrderHistoryDetailCubit()),
      BlocProvider<IntegrationSettingsPointsCubit>(create: (context) => IntegrationSettingsPointsCubit()),
      BlocProvider<IntegrationSettingsVouchersCubit>(create: (context) => IntegrationSettingsVouchersCubit()),
      BlocProvider<IntegrationSettingsVouchersUpdateCubit>(create: (context) => IntegrationSettingsVouchersUpdateCubit()),
      BlocProvider<IntegrationSettingsPointsUpdateCubit>(create: (context) => IntegrationSettingsPointsUpdateCubit()),
      BlocProvider<OrderHistoryDetailTimeLineCubit>(create: (context) => OrderHistoryDetailTimeLineCubit()),
      BlocProvider<BusinessSignupAutocompleteAddressBloc>(create: (context) => BusinessSignupAutocompleteAddressBloc()),
      BlocProvider<SignupAddressSelectedCubit>(create: (context) => SignupAddressSelectedCubit()),
      BlocProvider<BubbleStateCubit>(create: (context) => BubbleStateCubit()),
      BlocProvider<CallButtonCountdownRequestOrdersCubit>(create: (context) => CallButtonCountdownRequestOrdersCubit()),
      BlocProvider<OrderDetailAutoReloadCubit>(create: (context) => OrderDetailAutoReloadCubit()),
      BlocProvider<CustomerOrderPaymentCubit>(create: (context) => CustomerOrderPaymentCubit()),
      BlocProvider<OrderingActiveReloadCubit>(create: (context) => OrderingActiveReloadCubit()),
      BlocProvider<CustomerOrderPaymentSendPhoneCubit>(create: (context) => CustomerOrderPaymentSendPhoneCubit()),
      BlocProvider<CBSelectTableCubit>(create: (context) => CBSelectTableCubit()),
      BlocProvider<CallButtonSignageCounterCardCubit>(create: (context) => CallButtonSignageCounterCardCubit()),
      BlocProvider<CallButtonSignageCounterCardEditContentCubit>(create: (context) => CallButtonSignageCounterCardEditContentCubit()),
      BlocProvider<OrderIntegrationSettingsDeliveryCubit>(create: (context) => OrderIntegrationSettingsDeliveryCubit()),
      BlocProvider<TargetOfferCustomerCubit>(create: (context) => TargetOfferCustomerCubit()),
      BlocProvider<CustomizeKitchenTicketCubit>(create: (context) => CustomizeKitchenTicketCubit()),
      BlocProvider<CustomizeCustomerReceiptCubit>(create: (context) => CustomizeCustomerReceiptCubit()),
      BlocProvider<OrderIntegrationSettingsTargetOffersCubit>(create: (context) => OrderIntegrationSettingsTargetOffersCubit()),
      BlocProvider<OrderDetailForPrintCubit>(create: (context) => OrderDetailForPrintCubit()),
      BlocProvider<HomeSceenTabsCubit>(create: (context) => HomeSceenTabsCubit()),
      BlocProvider<CustomBubbleCubit>(create: (context) => CustomBubbleCubit()),
      BlocProvider<ChatMessagesCubit>(create: (context) => ChatMessagesCubit(chatMessagesService: ChatMessagesService())),
      BlocProvider<MultipleAccountCubit>(create: (context) => MultipleAccountCubit()),
      BlocProvider<ChatSupportCountBadgeCubit>(create: (context) => ChatSupportCountBadgeCubit()),
      BlocProvider<SearchAddressCubit>(create: (context) => SearchAddressCubit()),
      BlocProvider<DeliveryQuoteCubit>(create: (context) => DeliveryQuoteCubit()),
      BlocProvider<NewDeliveryCubit>(create: (context) => NewDeliveryCubit()),
      BlocProvider<InstructionFocusedHomeCubit>(create: (context) => InstructionFocusedHomeCubit()..doCheckFirstHome()),
    ];
  }
}
