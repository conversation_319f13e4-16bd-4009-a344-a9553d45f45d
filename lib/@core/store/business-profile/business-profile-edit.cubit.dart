// ignore_for_file: unused_catch_clause, avoid_catching_errors, avoid_positional_boolean_parameters, empty_catches, cascade_invocations

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:formz/formz.dart';
import 'package:stickyqrbusiness/@common/common.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/models/controls/controls.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.service.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/currency/currency.model.dart';
import 'package:stickyqrbusiness/@widgets/select-country/@select-country.dart';
import 'package:stickyqrbusiness/@widgets/select-timezone/time-zone.model.dart';

part 'business-profile-edit.state.dart';

class BusinessProfileEditCubit extends Cubit<BusinessProfileEditState> {
  BusinessProfileEditCubit()
      : super(
          const BusinessProfileEditState(
            busName: TextInputControl.pure(),
            busPhone: PhoneControl.pure(),
          ),
        );

  final _service = BusinessProfileService();

  void onChangedName(String value) {
    emit(state.copyWith(busName: TextInputControl.dirty(value)));
  }

  void onChangedPhoneNumber(String value) {
    emit(
      state.copyWith(
        busPhone: PhoneControl.dirty(value),
        status: EditStatus.edit,
      ),
    );
  }

  void onChangedCurrentCode(String value) {
    emit(
      state.copyWith(
        currentCode: value,
        status: EditStatus.edit,
      ),
    );
  }

  void onChangedCountry(String value) {
    emit(
      state.copyWith(
        currentCountry: value,
        status: EditStatus.edit,
      ),
    );
  }

  void onChangedFlag(String value) {
    emit(
      state.copyWith(
        flag: value,
        status: EditStatus.edit,
      ),
    );
  }

  void onChangedTimezone(TimezoneModel value) {
    emit(state.copyWith(timezone: value));
  }

  void onChangedCurrency(CurrencyModel value) {
    emit(state.copyWith(currency: value));
  }

  void onChangedCountryCode(Country value) {
    emit(state.copyWith(countryCode: value, status: EditStatus.initial));
  }

  void onChangedStreet(String value, {bool isUpdate = false}) {
    emit(state.copyWith(street: value, status: isUpdate ? EditStatus.initial : EditStatus.edit));
  }

  void onChangedCity(String value, {bool isUpdate = false}) {
    emit(state.copyWith(city: value, status: isUpdate ? EditStatus.initial : EditStatus.edit));
  }

  void onChangedState(String value, {bool isUpdate = false}) {
    emit(state.copyWith(state: value, status: isUpdate ? EditStatus.initial : EditStatus.edit));
  }

  void onChangedZipCode(String value, {bool isUpdate = false}) {
    emit(state.copyWith(zipCode: value, status: isUpdate ? EditStatus.initial : EditStatus.edit));
  }

  void onChangedLanguage(String value) {
    emit(state.copyWith(language: value));
  }

  void onChangedAvatar(Avatar value) {
    emit(state.copyWith(avatar: value));
  }

  void onChangedLat(String value) {
    emit(
      state.copyWith(
        lat: value,
        status: value == '' ? EditStatus.edit : EditStatus.initial,
      ),
    );
  }

  void onChangedLng(String value) {
    emit(
      state.copyWith(
        lng: value,
        status: value == '' ? EditStatus.edit : EditStatus.initial,
      ),
    );
  }

  void onChangedGooglePlaceId(String value) {
    emit(
      state.copyWith(
        googlePlaceId: value,
        status: value == '' ? EditStatus.edit : EditStatus.initial,
      ),
    );
  }

  void onChangePointClaims(bool value) {
    emit(
      state.copyWith(
        isAcceptCustomerClaimsPoint: value,
        status: EditStatus.success,
      ),
    );
  }

  void onChangeSoundNewEnablePointClaims(bool enableSoundNewPointClaim) {
    emit(state.copyWith(enableSoundNewPointClaim: enableSoundNewPointClaim));
  }

  void onChangeSoundNewRepeatPointClaims(int soundNewPointClaimsRepeat) {
    emit(
      state.copyWith(soundNewPointClaimsRepeat: soundNewPointClaimsRepeat),
    );
  }

  void onChangeSoundNewNamePointClaims(int soundNewPointClaimName) {
    emit(
      state.copyWith(soundNewPointClaimName: soundNewPointClaimName),
    );
  }

  Future<void> onChangeEnableCallButton(bool enableCallButton) async {
    emit(state.copyWith(enableCallButton: enableCallButton, status: EditStatus.initial));
  }

  void onChangeEnableCallButtonSound(bool enableCallButtonSound) {
    emit(state.copyWith(enableCallButtonSound: enableCallButtonSound, status: EditStatus.initial));
  }

  void onChangeCallButtonSoundRepeat(int callButtonSoundRepeat) {
    emit(
      state.copyWith(callButtonSoundRepeat: callButtonSoundRepeat, status: EditStatus.initial),
    );
  }

  void onChangeCallButtonSoundName(int callButtonSoundName) {
    emit(
      state.copyWith(callButtonSoundName: callButtonSoundName, status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksTitle(String value) {
    emit(
      state.copyWith(thanksTitle: TextInputControl.dirty(value), status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksMessage(String value) {
    emit(
      state.copyWith(thanksMessage: TextInputControl.dirty(value), status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksCTAButtonTitle(String value) {
    emit(
      state.copyWith(thanksCTAButtonTitle: TextInputControl.dirty(value), status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksCTAButtonLink(String value) {
    emit(
      state.copyWith(thanksCTAButtonLink: TextInputControl.dirty(value), status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksButton(bool value) {
    emit(
      state.copyWith(thanksButton: value, status: EditStatus.initial),
    );
  }

  void onChangeCallButtonBannerDownload(bool value) {
    emit(
      state.copyWith(thanksButton: value, status: EditStatus.initial),
    );
  }

  void onChangeCallButtonThanksButtonsMultiple(
    List<CallButtonThankYouButton> value,
  ) {
    if (value.isNotEmpty) {
      AppLog.e('onChangeCallButtonThanksButtonsMultiple: ${value[0].toJson()}');
    }
    emit(
      state.copyWith(callButtonThankYouButtons: value, status: EditStatus.initial),
    );
  }

  Future<void> onChangeStatusCallButtonThanksButtonsMultiple(EditStatus? status) async {
    AppLog.e('onChangeStatusCallButtonThanksButtonsMultiple: $status');
    emit(state.copyWith(
      status: status ?? EditStatus.initial,
      callButtonThankYouButtons: state.callButtonThankYouButtons,
    ));
  }

  void onChangeAddCallButtonThanksButtonsMultiple(CallButtonThankYouButton value, {bool isAdd = true}) {
    final data = state.callButtonThankYouButtons ?? [];
    if (isAdd) {
      data.add(value);
    } else {
      data.remove(value);
    }

    emit(
      state.copyWith(callButtonThankYouButtons: data, status: EditStatus.initial),
    );
  }

  Future<void> onChangeEnableCallButtonHMC(bool value) async {
    emit(state.copyWith(enableCallButtonHMC: value, status: EditStatus.initial));
  }

  Future<void> onChangeEnableCallButtonOrdering(bool value) async {
    emit(state.copyWith(enableCallButtonOrdering: value, status: EditStatus.initial));
  }

  void onResetStatus() {
    emit(
      state.copyWith(
        status: EditStatus.initial,
        errMsg: '',
        isAcceptCustomerClaimsPoint: null,
      ),
    );
  }

  Future<Business?> onUpdateAvatar(Media file) async {
    try {
      emit(state.copyWith(status: EditStatus.inprogress));

      final fileName = '${file.name?.replaceAll(' ', '-')}';
      final FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(file.path ?? '', filename: '$fileName.jpg'),
        'name': fileName,
      });

      final result = await _service.uploadFile(formData);

      final param = {
        'avatarId': result?.id ?? '',
      };
      final response = await _service.updateProfile(param);
      if (response != null) {
        emit(state.copyWith(status: EditStatus.upload));
        await AppLocalStorage.saveBusiness(response);
      } else {
        emit(state.copyWith(status: EditStatus.error));
      }
      return response;
    } on AppErrorResponse catch (e) {
      emit(
        state.copyWith(
          status: EditStatus.error,
          errMsg: e.message,
        ),
      );
    }
    return null;
  }

  Future<Business?> onUpdate() async {
    AppLog.e('state.isValidateCountry: ${state.isValidateCountry} -- ${state.isValidateForm} -- ${state.isValidatePhoneNumber}');

    if (state.isValidateForm && !state.isValidateCountry && state.isValidatePhoneNumber) {
      try {
        emit(state.copyWith(status: EditStatus.loading));
        final body = {
          'name': state.busName.value.trim(),
          // 'phone': state.busPhone.value.trim(),//          phone: '+${state.currentCode}${state.phoneNumber.value}',
          'phone': '+${state.currentCode}${state.busPhone.value}',
          'currency': state.currency?.code,
          'timeZone': state.timezone?.name,
          'street': state.street?.trim(),
          'city': state.city?.trim(),
          'state': state.state?.trim(),
          'country': state.countryCode?.isoCode,
          'zipCode': state.zipCode?.trim(),
          'language': state.language?.trim(),
          'lat': state.lat?.trim(),
          'lng': state.lng?.trim(),
          'googlePlaceId': state.googlePlaceId?.trim(),
        };

        final response = await _service.updateProfile(body);
        if (response != null) {
          // AppLog.d('update == $response');
          await AppLocalStorage.saveBusiness(response);
          emit(state.copyWith(status: EditStatus.success));
        } else {
          emit(state.copyWith(status: EditStatus.error));
        }
      } on AppErrorResponse catch (e) {
        emit(
          state.copyWith(
            status: EditStatus.error,
            errMsg: e.message,
          ),
        );
      }
    } else {
      emit(state.copyWith(status: EditStatus.error));
    }
    return null;
  }

  Future<Business?> onUpdateTimeZone(String timeZone) async {
    try {
      final body = {
        'timeZone': timeZone,
      };
      final response = await _service.updateProfile(body);
      if (response != null) {
        AppLog.d('update == $response');
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {}

    return null;
  }

  Future<Business?> onUpdateBusinessPointClaimsSettingSound() async {
    try {
      emit(state.copyWith(status: EditStatus.soudLoading));
      final body = {
        'acceptCustomerClaimsPoint': state.isAcceptCustomerClaimsPoint,
        'enableSoundNewPointClaim': state.enableSoundNewPointClaim,
        'soundNewPointClaimsRepeat': state.soundNewPointClaimsRepeat,
        'soundNewPointClaimName': state.soundNewPointClaimName,
      };
      AppLog.e('body == onUpdateBusinessPointClaimsSettingSound $body');
      final response = await _service.updateProfile(body);
      if (response != null) {
        AppLog.e('update == $response');
        emit(
          state.copyWith(
            status: EditStatus.soundSuccess,
          ),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessCallButtonsSettingSound() async {
    try {
      emit(state.copyWith(status: EditStatus.soudCallButtonLoading));
      final body = {
        'enableCallButtonSound': state.enableCallButtonSound,
        'callButtonSoundRepeat': state.callButtonSoundRepeat,
        'callButtonSoundName': state.callButtonSoundName,
      };
      AppLog.e('body == $body');
      final response = await _service.updateProfile(body);
      if (response != null) {
        AppLog.e('update == $response');
        emit(
          state.copyWith(status: EditStatus.soundCallButtonSuccess),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessCallButton() async {
    try {
      emit(state.copyWith(status: EditStatus.soudLoading));
      final body = {
        'enableCallButton': state.enableCallButton,
      };
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(
          state.copyWith(
            status: EditStatus.success,
          ),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessCallButtonThanks() async {
    // if (state.isValidateFormThanks) {
    try {
      final list = state.callButtonThankYouButtons?.where((item) => item.type == 'external').toList();
      final hasNullFields = list!.any((item) => item.title == null || item.title == '' || item.link == null || item.link == '');
      final hasNotLinkFields = list.any((item) => !AppValidations.isValidURL(item.link ?? ''));

      AppLog.e('hasNullFields: $hasNullFields - hasNotLinkFields: $hasNotLinkFields');
      if (hasNullFields || hasNotLinkFields || state.thanksButton == true && (!state.isValidateFormThanks || !AppValidations.isValidURL(state.thanksCTAButtonLink?.value ?? ''))) {
        emit(state.copyWith(status: EditStatus.error));
        return null;
      }

      emit(state.copyWith(status: EditStatus.soudCallButtonLoading));
      final CallButtonThankYouCustomize param = CallButtonThankYouCustomize();

      param.title = state.thanksTitle?.value;
      param.message = state.thanksMessage?.value;
      param.ctaButtonTitle = state.thanksCTAButtonTitle?.value;
      param.ctaButtonLink = state.thanksCTAButtonLink?.value;
      if (state.thanksButton != true) {
        param.ctaButtonTitle = null;
        param.ctaButtonLink = null;
      }

      final Map<String, dynamic> body = {'callButtonThankYouCustomize': param.toJson()};
      final callButtonThankYouButtons = state.callButtonThankYouButtons;
      final param2 = callButtonThankYouButtons?.map((item) => item.toJson()).toList();
      body['callButtonThankYouButtons'] = param2;
      AppLog.e('body == $body');

      final response = await _service.updateProfile(body);
      if (response != null) {
        AppLog.e('update == $response');
        emit(
          state.copyWith(
            status: EditStatus.soundCallButtonSuccess,
          ),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessCallButtonsHelpMeChoose(Map<String, dynamic> body) async {
    try {
      emit(state.copyWith(status: EditStatus.hmcLoading));
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(
          state.copyWith(status: EditStatus.hmcSuccess),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessScreenNewHome(Map<String, dynamic> body) async {
    try {
      emit(state.copyWith(status: EditStatus.loading));
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(state.copyWith(status: EditStatus.success));
        await AppLocalStorage.saveBusiness(response);
        return response;
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateBusinessCallButtonsOrdering(Map<String, dynamic> body) async {
    try {
      emit(state.copyWith(status: EditStatus.hmcLoading));
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(
          state.copyWith(status: EditStatus.hmcSuccess),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateCustomerCheckIn(Map<String, dynamic> body) async {
    try {
      emit(state.copyWith(status: EditStatus.customerCheckInLoading));
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(
          state.copyWith(status: EditStatus.customerCheckInSuccess),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }

  Future<Business?> onUpdateReviewGoogle(Map<String, dynamic> body) async {
    try {
      emit(state.copyWith(status: EditStatus.customerCheckInLoading));
      final response = await _service.updateProfile(body);
      if (response != null) {
        emit(
          state.copyWith(status: EditStatus.customerCheckInSuccess),
        );
        await AppLocalStorage.saveBusiness(response);
      }
    } on AppErrorResponse catch (e) {
      emit(state.copyWith(status: EditStatus.error, errMsg: e.message));
    }

    return null;
  }
}
