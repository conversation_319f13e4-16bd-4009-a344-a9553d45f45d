// ignore_for_file: inference_failure_on_instance_creation, strict_raw_type, inference_failure_on_function_invocation, empty_catches, avoid_dynamic_calls, use_colored_box, cascade_invocations

import 'dart:async';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flash/flash.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screen_lock/flutter_screen_lock.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/audit-logging/audit-logging.service.dart';
import 'package:stickyqrbusiness/@core/store/order-detail/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/app/app.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/customers/widgets/customer-ai-summary.dart';
import 'package:stickyqrbusiness/pages/orders-active/order-active-detail-page.dart';

///
/// General for All
///
class AppBased {
  factory AppBased.init(AppEnv evn) {
    final instance = AppBased._();
    appEnv = evn;
    return instance;
  }

  AppBased._() {
    _initLoading();
  }
  static late AppEnv appEnv;
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  // static final appLoader = Lottie.asset(AppLotties.loader);
  static const appLoader = CircularProgressIndicator();
  // static final appLoader = EasyLoadingIndicatorType.threeBounce;

  /// Get current auth state
  static AuthState? authState(BuildContext context) {
    try {
      return context.read<AuthBloc>().state;
    } catch (e) {
      return null;
    }
  }

  // LOADING

  void _initLoading() {
    AppLog.i(
      'InitLoading',
      logType: AppLoggerType.FUNCTION,
      classParent: 'AppBased',
    );
    EasyLoading.instance
      ..loadingStyle = EasyLoadingStyle.custom
      ..indicatorSize = 24.0
      ..radius = 12
      ..maskType = EasyLoadingMaskType.none
      ..contentPadding = const EdgeInsets.only(top: 20, bottom: 0, left: 20, right: 20)
      ..backgroundColor = Colors.black.withValues(alpha: 0.1)
      ..indicatorColor = Colors.red
      ..textColor = Colors.red
      ..userInteractions = false
      ..dismissOnTap = false;
  }

  /// Show/Hide loading
  static void loading({
    bool isShow = true,
    Widget? customIndicator,
    bool dismissOnTap = false,
    String? status,
    bool withMask = false,
  }) {
    if (withMask) {
      EasyLoading.instance
        ..maskType = EasyLoadingMaskType.custom
        ..indicatorType = EasyLoadingIndicatorType.cubeGrid
        ..backgroundColor = Colors.red.withValues(alpha: 0.6)
        ..maskColor = Colors.black.withValues(alpha: 0.1);
    }
    if (isShow) {
      // final loaderWidget = Container(
      //   color: Colors.transparent,
      //   width: 48,
      //   height: 48,
      //   child: appLoader,
      // );
      // EasyLoading.show(dismissOnTap: dismissOnTap, status: status.toString(), indicator: loaderWidget);
      EasyLoading.instance
        ..loadingStyle = EasyLoadingStyle.custom
        ..maskType = EasyLoadingMaskType.custom
        ..contentPadding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12)
        ..indicatorSize = 25
        ..radius = 8
        ..progressWidth = 1
        ..userInteractions = false
        ..dismissOnTap = false
        ..indicatorType = EasyLoadingIndicatorType.threeBounce
        ..indicatorColor = AppColors.appColor
        ..backgroundColor = Colors.white
        ..textColor = Colors.amber
        ..maskColor = Colors.black.withValues(alpha: 0.4);
      EasyLoading.show(
        dismissOnTap: dismissOnTap,
        // indicator: const AppLoadingCustom(),
        // indicator: loaderWidget,
        // maskType: EasyLoadingMaskType.clear
        // status: '123123',
      );
    } else {
      EasyLoading.dismiss();
    }
  }

  // ROUTING

  /// Remove all routes and push to Routes
  static void offToPage(BuildContext context, {String? customPage}) {
    Navigator.of(context).popUntil(ModalRoute.withName(customPage ?? AppRoutes.main));
  }

  static void goHome(BuildContext context, {String? customeHomePath}) {
    Navigator.of(context).popAndPushNamed(customeHomePath ?? AppRoutes.main);
  }

  static void goBackAllPage(BuildContext context, {String? customeHomePath}) {
    Navigator.of(context).pushAndRemoveUntil(MaterialPageRoute(builder: (context) => const StickyQRApp()), (Route<dynamic> route) => false);
  }

  /// Go (Push) to page route
  static void go(
    BuildContext context,
    String page, {
    bool isReplace = false,
    ScreenArguments? args,
    List<int>? roles,
    Function? onReback,
    ValueChanged? onChanged,
  }) {
    try {
      final pagename = AppRoutes.allRoutes[page].toString().split('Closure: (BuildContext) =>')[1].trim();
      AppLog.e(
        'Routing to : $page - $pagename',
        logValue: 'go',
        classParent: 'AppBased',
      );

      setAnalytics(pagename, page.split('/')[1]);
    } catch (e) {}
    if (authState(context)?.isAuthenticated == true) {
      // final bool routeExist = AppRoutes.routesNotGoLoggedIn.any((element) => element == page);
      // if (routeExist) {
      //   AppLog.e('Route to $page is not allow because you are already loggined in.', logValue: 'go', classParent: 'AppBased');
      //   return;
      // }
    }
    if (page == AppRoutes.main) {
      AppLog.e(
        'Route to $page is ROOT -> Calling offToPage(Root)',
        logValue: 'go',
        classParent: 'AppBased',
      );
      offToPage(context, customPage: page);
      return;
    }
    if (!isReplace) {
      Navigator.of(context).pushNamed(page, arguments: args).then(
        (value) {
          try {
            onReback?.call(value);
            onChanged?.call(value);
          } catch (e) {}
        },
      );
    } else {
      Navigator.of(context).pushReplacementNamed(page, arguments: args).then(
            (value) => {
              onReback?.call(value),
              onChanged?.call(value),
            },
          );
    }
  }

  static void goToOrderDetailPage(
    BuildContext context,
    String orderId, {
    Function? onChanged,
  }) {
    Navigator.of(context)
        .push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: OrderDetailCubitRegistry.getCubit(orderId),
          child: OrderActiveDetailPage(orderId: orderId),
        ),
      ),
    )
        .then((value) {
      onChanged?.call(value);
    });
  }

  // TOAST

  /// Toast Information
  // static void toastInfo(
  //   BuildContext context, {
  //   String? title,
  //   String? description,
  //   int duration = 2,
  //   FlashPosition position = FlashPosition.top,
  //   FlashBehavior style = FlashBehavior.floating,
  //   IconData? customIcon,
  //   bool withButton = false,
  //   Widget? buttonWidget,
  // }) {
  //   FlashBar flashBar;
  //   final iconInfo = Icon(customIcon ?? Icons.info, color: Colors.white);
  //   final titleWidget = Text(
  //     title ?? '',
  //     style: const TextStyle(
  //       fontSize: 18,
  //       fontWeight: FontWeight.bold,
  //       color: Colors.white,
  //     ),
  //   );

  //   Widget? priAction;
  //   if (withButton && buttonWidget != null) {
  //     priAction = buttonWidget;
  //   }
  //   if (description != null) {
  //     final descriptionWidget = Text(
  //       description,
  //       style: const TextStyle(fontSize: 16, color: Colors.white),
  //     );
  //     flashBar = FlashBar(
  //       icon: iconInfo,
  //       primaryAction: priAction,
  //       title: titleWidget,
  //       content: descriptionWidget,
  //     );
  //   } else {
  //     flashBar = FlashBar(
  //       icon: iconInfo,
  //       primaryAction: priAction,
  //       content: titleWidget,
  //     );
  //   }
  //   showFlash(
  //     context: context,
  //     duration: Duration(seconds: duration),
  //     builder: (context, controller) {
  //       return Flash(
  //         controller: controller,
  //         backgroundColor: Colors.blue,
  //         boxShadows: [BoxShadow(blurRadius: 4, color: Colors.black.withValues(alpha: 0.1))],
  //         borderRadius: BorderRadius.circular(8),
  //         position: position,
  //         behavior: style,
  //         onTap: () => controller.dismiss(),
  //         margin: const EdgeInsets.all(12),
  //         child: flashBar,
  //       );
  //     },
  //   );
  // }
  static void toastInfo(
    BuildContext context, {
    String? title,
    // String? description,
    int duration = 3,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
    Color? backgroundColor,
  }) {
    final iconInfo = Icon(customIcon ?? Icons.info, color: Colors.white);
    final titleWidget = Text(
      title ?? '',
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
    );
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              controller.dismiss();
            },
            child: FlashBar(
              controller: controller,
              position: position,
              behavior: style,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              icon: iconInfo,
              content: titleWidget,
              // content: description != null
              //     ? Text(
              //         description,
              //         style: const TextStyle(fontSize: 16, color: Colors.white),
              //       )
              // : const SizedBox.shrink(),
              // title: titleWidget,
              backgroundColor: backgroundColor ?? Colors.blue,
              surfaceTintColor: Colors.transparent,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  /// Toast Error
  static void toastError(
    BuildContext context, {
    String? title,
    // String? description,
    int duration = 3,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
  }) {
    final iconError = Icon(customIcon ?? Icons.report, color: Colors.white);
    final titleWidget = Text(
      title ?? '',
      style: const TextStyle(fontSize: 16, color: Colors.white),
    );
    // final descriptionWidget = description != null
    //     ? Text(
    //         description,
    //         style: const TextStyle(
    //           fontSize: 15,
    //           color: Colors.white,
    //         ),
    //       )
    //     : const SizedBox.shrink();
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              controller.dismiss();
            },
            child: FlashBar(
              controller: controller,
              position: position,
              behavior: style,
              margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              backgroundColor: const Color(0xFFFF4848),
              surfaceTintColor: Colors.transparent,
              icon: iconError,
              // title: titleWidget,
              // content: descriptionWidget,
              content: titleWidget,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  /// Toast Warning
  static void toastWarning(
    BuildContext context, {
    String? title,
    String? description,
    int duration = 3,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
    FontWeight? titleFontWeight,
    double? titleFontSize,
    String? customIconSvg,
    bool isTitleCustom = false,
    bool withIcon = false,
    Color? backgroundColor,
  }) {
    final iconWarning = Icon(
      customIcon ?? Icons.warning,
      color: Colors.black,
    );
    final titleWidget = isTitleCustom
        ? Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (customIconSvg != null)
                Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: SvgPicture.asset('assets/svgs/$customIconSvg.svg'),
                )
              else
                const SizedBox.shrink(),
              Text(
                title ?? '',
                style: TextStyle(
                  fontSize: titleFontSize ?? 18,
                  fontWeight: titleFontWeight ?? FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          )
        : Text(
            title ?? '',
            style: TextStyle(
              fontSize: titleFontSize ?? 18,
              fontWeight: titleFontWeight ?? FontWeight.bold,
              color: Colors.black,
            ),
          );
    final descriptionWidget = description != null
        ? Text(
            description,
            style: const TextStyle(fontSize: 16, color: Colors.black),
          )
        : const SizedBox.shrink();
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              controller.dismiss();
            },
            child: FlashBar(
              useSafeArea: true,
              controller: controller,
              position: position,
              behavior: style,
              backgroundColor: backgroundColor ?? Colors.yellow[700],
              surfaceTintColor: Colors.transparent,
              margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              icon: iconWarning,
              title: titleWidget,
              content: descriptionWidget,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  /// Toast Success
  static void toastSuccess(
    BuildContext context, {
    String? title,
    // String? description,
    int duration = 3,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
    double? titleSize,
    FontWeight? titleFontWeight,
  }) {
    final iconSuccess = Icon(customIcon ?? Icons.check_circle, color: Colors.white);
    final titleWidget = Text(
      title ?? '',
      textAlign: TextAlign.left,
      style: TextStyle(
        color: Colors.white,
        fontSize: titleSize ?? 18,
        fontWeight: titleFontWeight ?? FontWeight.bold,
      ),
    );
    // final descriptionWidget = description != null
    //     ? Text(
    //         description,
    //         style: const TextStyle(fontSize: 16, color: Colors.white),
    //       )
    //     : const SizedBox.shrink();
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              controller.dismiss();
            },
            child: FlashBar(
              useSafeArea: true,
              controller: controller,
              position: position,
              behavior: style,
              backgroundColor: Colors.black87,
              surfaceTintColor: Colors.transparent,
              margin: const EdgeInsets.all(20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              icon: iconSuccess,
              // title: titleWidget,
              content: titleWidget,
              // content: descriptionWidget,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  static void toastWarningHome(
    BuildContext context, {
    String? title,
    // String? description,
    int duration = 2,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
    double? titleSize,
    FontWeight? titleFontWeight,
  }) {
    // final iconSuccess = Icon(customIcon ?? Icons.check_circle, color: Colors.white);
    final titleWidget = Text(
      title ?? '',
      textAlign: TextAlign.left,
      style: TextStyle(
        color: Colors.white,
        fontSize: titleSize ?? 16,
        fontWeight: titleFontWeight ?? FontWeight.w400,
      ),
    );
    // final descriptionWidget = description != null
    //     ? Text(
    //         description,
    //         style: const TextStyle(fontSize: 16, color: Colors.white),
    //       )
    //     : const SizedBox.shrink();
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              controller.dismiss();
            },
            child: FlashBar(
              useSafeArea: true,
              controller: controller,
              position: position,
              behavior: style,
              backgroundColor: Colors.black87,
              surfaceTintColor: Colors.transparent,
              margin: const EdgeInsets.all(20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              // icon: iconSuccess,
              // title: titleWidget,
              content: titleWidget,
              // content: descriptionWidget,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  /// Toast Notifcation
  static void toastNotifcation(
    BuildContext context, {
    String? title,
    String? description,
    int duration = 3,
    FlashPosition position = FlashPosition.top,
    FlashBehavior style = FlashBehavior.floating,
    IconData? customIcon,
    bool withButton = false,
    Widget? buttonWidget,
    Widget? customLeading,
    Function? func,
    Widget? customWidget,
  }) {
    final iconNotifcation = Icon(
      customIcon ?? Icons.notifications_active,
      color: Colors.black,
    );
    final titleWidget = Text(
      title ?? '',
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: Colors.black,
      ),
    );
    final descriptionWidget = description != null
        ? Text(
            description,
            style: const TextStyle(fontSize: 16, color: Colors.black),
          )
        : const SizedBox.shrink();
    Widget? priAction;
    if (withButton && buttonWidget != null) {
      priAction = buttonWidget;
    }

    showFlash(
      context: context,
      duration: Duration(seconds: duration),
      builder: (context, controller) {
        return Flash(
          controller: controller,
          position: position,
          child: GestureDetector(
            onTap: () {
              func?.call();
              controller.dismiss();
            },
            child: FlashBar(
              useSafeArea: true,
              controller: controller,
              position: position,
              behavior: style,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.transparent,
              margin: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
              shadowColor: Colors.black.withValues(alpha: 0.55),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                // side: BorderSide(
                //   color: Colors.yellow,
                //   strokeAlign: BorderSide.strokeAlignInside,
                // ),
              ),
              padding: customWidget != null ? EdgeInsets.zero : null,
              // clipBehavior: Clip.antiAlias,
              // indicatorColor: customWidget == null ? Colors.blue.shade200 : null,
              icon: customWidget == null ? iconNotifcation : null,
              title: customWidget == null ? titleWidget : null,
              content: customWidget ?? descriptionWidget,
              primaryAction: priAction,
            ),
          ),
        );
      },
    );
  }

  // DIALOG
  static void dialog(
    BuildContext context, {
    required Widget children,
    String? dialogId,
    bool barrierDismissible = true,
    AlignmentGeometry modalAlign = Alignment.bottomCenter,
    EdgeInsets dialogPadding = const EdgeInsets.all(24),
    EdgeInsets dialogMargin = const EdgeInsets.all(20),
    BorderRadius? dialogBorderRadius,
    double? dialogHeight,
    double? dialogWidth,
  }) {
    showGeneralDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 150),
      transitionBuilder: (context, anim1, anim2, child) {
        return SlideTransition(
          position: Tween(begin: const Offset(0, 1), end: Offset.zero).animate(anim1),
          child: child,
        );
      },
      pageBuilder: (context, ani1, ani2) {
        return SafeArea(
          child: LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              final double dWidth = constraints.maxWidth > 375 ? 375 : constraints.maxWidth;
              double dMaxHeight;
              BoxConstraints constr = BoxConstraints(
                minHeight: 60,
                minWidth: dWidth,
              );
              if (dialogHeight != null) {
                dMaxHeight = constraints.maxHeight < dialogHeight ? constraints.maxHeight : dialogHeight;
                constr = BoxConstraints(
                  minHeight: dMaxHeight,
                  maxHeight: dMaxHeight,
                  maxWidth: dWidth,
                  minWidth: dWidth,
                );
              }
              return Dismissible(
                key: Key(dialogId ?? '${DateTime.now().microsecondsSinceEpoch}'),
                dragStartBehavior: DragStartBehavior.down,
                direction: DismissDirection.down,
                resizeDuration: const Duration(milliseconds: 50),
                onDismissed: (direction) => Navigator.of(context).pop(),
                child: Stack(
                  alignment: modalAlign,
                  children: [
                    ConstrainedBox(
                      constraints: constr,
                      child: IntrinsicHeight(
                        child: Container(
                          margin: dialogMargin,
                          padding: dialogPadding,
                          constraints: constr,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: dialogBorderRadius ?? BorderRadius.circular(20),
                          ),
                          child: ClipRRect(
                            borderRadius: dialogBorderRadius ?? BorderRadius.circular(20),
                            child: children,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  static void dialogYesNo(
    BuildContext context, {
    String? title,
    Widget? titleWidget,
    Widget? icon,
    String? message,
    Widget? messageWidget,
    String yesText = 'Yes',
    String? noText = 'No',
    String? dialogId,
    Widget? yesWidget,
    Widget? noWidget,
    Function? yesTap,
    Function? noTap,
    double? dialogHeight,
    AlignmentGeometry modalAlign = Alignment.center,
  }) {
    Widget? titleW = titleWidget;
    if (titleW == null && title != null) {
      titleW = Text(
        title,
        style: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
        ),
      );
    }

    Widget? messageW = messageWidget;
    if (messageW == null && message != null) {
      messageW = Text(
        message,
        softWrap: true,
        style: const TextStyle(fontSize: 15, color: Colors.black54),
      );
    }

    Widget? yesW = yesWidget;
    // ignore: unnecessary_null_comparison
    if (yesW == null && yesText != null) {
      yesW = ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: AppColors.appColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(color: AppColors.appColor),
          ),
        ),
        onPressed: () {
          Navigator.of(context).pop();
          yesTap?.call();
        },
        child: Text(yesText),
      );
    }

    Widget? noW = noWidget;
    if (noW == null && noText != null) {
      noW = OutlinedButton(
        style: OutlinedButton.styleFrom(
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: const BorderSide(color: AppColors.appColor),
          ),
        ),
        onPressed: () {
          Navigator.of(context).pop();
          noTap?.call();
        },
        child: Text(noText),
      );
    }
    dialog(
      context,
      dialogPadding: EdgeInsets.zero,
      dialogHeight: dialogHeight,
      modalAlign: modalAlign,
      children: ConstrainedBox(
        constraints: const BoxConstraints(
          minWidth: 150.0,
          maxWidth: 300.0,
          // minHeight: 180.0,
          // maxHeight: 180.0,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.only(left: 24, right: 24, top: 24, bottom: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (icon != null)
                    Container(
                      margin: const EdgeInsets.only(right: 12),
                      child: icon,
                    ),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (titleW != null) ...{
                          titleW,
                          const SizedBox(height: 8),
                        },
                        if (messageW != null) ...{
                          messageW,
                          const SizedBox(height: 8),
                        },
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.only(left: 24, right: 24, bottom: 16),
              // color: Color(0xFFF7F9FC),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (noW != null) ...{noW, const SizedBox(width: 16)},
                  if (yesW != null) yesW,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static void openShowModalBottomSheet(
    BuildContext context, {
    Widget? widget,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    bool resizeToAvoidBottomInset = true,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: enableDrag,
      context: context,
      useSafeArea: true,
      backgroundColor: backgroundColor ?? Colors.transparent,
      builder: (ctx) {
        return Scaffold(
          extendBody: false,
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
          body: FractionallySizedBox(
            heightFactor: 1,
            child: Container(
              height: 200,
              color: backgroundColor ?? Colors.transparent,
              padding: EdgeInsets.only(top: paddingTop),
              // margin: EdgeInsets.only(top: paddingTop),
              // decoration: const BoxDecoration(color: Colors.yellow),
              child: widget,
            ),
          ),
        );
      },
    );
  }

  static void openMapModalBottomSheet(
    BuildContext context, {
    Widget? widget,
    Color? backgroundColor,
    double paddingTopCurrent = 0,
    bool enableDrag = false,
    bool resizeToAvoidBottomInset = true,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: enableDrag,
      context: context,
      useSafeArea: true,
      backgroundColor: backgroundColor ?? Colors.transparent,
      builder: (ctx) {
        // final topPaddingCurrent = MediaQuery.of(context).padding.top;
        return Builder(
          builder: (BuildContext context) {
            final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
            return Container(
              padding: EdgeInsets.only(top: paddingTop),
              // child: widget,
              child: Container(
                padding: const EdgeInsets.only(top: 10),
                decoration: const BoxDecoration(
                  color: AppColors.lightPrimaryBackgroundColor,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 50,
                      height: 8,
                      decoration: BoxDecoration(
                        color: AppColors.homeBorderColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    Expanded(
                        child: Container(
                      child: widget,
                    )),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetAnn(
    BuildContext context, {
    Widget? widget,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    bool useSafeArea = true,
    bool isSetHeightRotateFactor = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      useSafeArea: useSafeArea,
      isScrollControlled: true,
      enableDrag: enableDrag,
      context: context,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (ctx) {
        return Builder(
          builder: (BuildContext context) {
            if (isSetHeightRotateFactor) {
              final height = MediaQuery.of(context).size.height;
              if (height <= 500) {
                heightFactor = .9;
              }
              // heightFactor = height <= 500 ? .9 : .4;
            }
            return FractionallySizedBox(
              heightFactor: heightFactor,
              child: Container(child: widget),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetOfferClaimCustomer(
    BuildContext context, {
    Widget? widget,
    Widget? widgetBottom,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    bool isSetHeightRotateFactor = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: enableDrag,
      context: context,
      useSafeArea: true,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (ctx) {
        return Builder(
          builder: (BuildContext context) {
            if (isSetHeightRotateFactor) {
              final height = MediaQuery.of(context).size.height;
              if (height <= 500) {
                heightFactor = .9;
              }
              // heightFactor = height <= 500 ? .9 : .4;
            }
            return FractionallySizedBox(
              heightFactor: heightFactor,
              child: Column(
                children: [
                  Expanded(child: Container(child: widget)),
                  Container(child: widgetBottom),
                ],
              ),
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetChangePlan(
    BuildContext context, {
    Widget? widget,
    Color? backgroundColor,
    bool enableDrag = false,
    bool isScrollControlled = true,
    bool isSetHeightRotateFactor = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    double height = MediaQuery.of(context).size.height;
    if (height <= 500) {
      isScrollControlled = true;
    }
    showModalBottomSheet(
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      context: context,
      useSafeArea: true,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16),
        ),
      ),
      builder: (ctx) {
        return StatefulBuilder(
          builder: (context, setState) {
            height = MediaQuery.of(context).size.height;
            setState(() {
              if (height <= 500) {
                isScrollControlled = true;
              } else {
                isScrollControlled = false;
              }
            });
            return Builder(
              builder: (context) {
                if (isScrollControlled) {
                  return FractionallySizedBox(
                    heightFactor: .8,
                    child: Container(child: widget),
                  );
                }

                return Container(child: widget);
              },
            );
          },
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void showBottomSheetBilling(
    BuildContext contextMain,
    String title, {
    Widget? widget,
    ValueChanged? onChanged,
    double? statusBarHeight,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: contextMain,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      builder: (BuildContext context) {
        final double statusBarHeightX = MediaQuery.of(contextMain).padding.top;
        return StatefulBuilder(
          builder: (context, state) {
            return ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height - statusBarHeightX,
              ),
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 50,
                      height: 8,
                      margin: const EdgeInsets.only(top: 8),
                      decoration: BoxDecoration(
                        color: AppColors.homeBorderColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    AppBar(
                      iconTheme: IconThemeData(
                        color: AppColors.appBlackColor.withValues(alpha: 0.6),
                      ),
                      backgroundColor: AppColors.appTransparentColor,
                      elevation: 0,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(16),
                        ),
                      ),
                      leadingWidth: 0,
                      actions: [
                        Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: SizedBox(
                            width: 40,
                            child: MaterialButton(
                              elevation: 0,
                              highlightElevation: 0,
                              hoverElevation: 0,
                              hoverColor: AppColors.lightPrimaryBackgroundColor,
                              onPressed: () => {
                                Navigator.of(context).pop(),
                              },
                              // color: AppColors.lightPrimaryBackgroundColor,
                              padding: EdgeInsets.zero,
                              shape: const CircleBorder(),
                              child: SvgPicture.asset(
                                'assets/svgs/close-black.svg',
                                // colorFilter: const ColorFilter.mode(
                                //   AppColors.appBlackColor,
                                //   BlendMode.srcIn,
                                // ),
                              ),
                            ),
                          ),
                        ),
                      ],
                      leading: const SizedBox.shrink(),
                      centerTitle: true,
                      title: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Container(
                        padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: widget,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).then((value) {
      onChanged?.call(value);
    });
  }

  static void showBottomSheetViewInvoice(
    BuildContext context,
    String title, {
    Widget? widget,
    bool enableDrag = false,
    double radiusValue = 16,
    ValueChanged? onChange,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: enableDrag,
      useSafeArea: true,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(radiusValue),
          topRight: Radius.circular(radiusValue),
        ),
      ),
      builder: (BuildContext context) {
        final marginTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return FractionallySizedBox(
          heightFactor: marginTop > 0 ? .94 : 1,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (enableDrag)
                  Container(
                    width: 50,
                    height: 8,
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: AppColors.homeBorderColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                AppBar(
                  iconTheme: IconThemeData(
                    color: AppColors.appBlackColor.withValues(alpha: 0.6),
                  ),
                  backgroundColor: AppColors.appTransparentColor,
                  elevation: 0,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SizedBox(
                        width: 40,
                        child: MaterialButton(
                          elevation: 0,
                          highlightElevation: 0,
                          hoverElevation: 0,
                          hoverColor: AppColors.lightPrimaryBackgroundColor,
                          onPressed: () => {
                            Navigator.of(context).pop(false),
                          },
                          // color: AppColors.lightPrimaryBackgroundColor,
                          padding: EdgeInsets.zero,
                          shape: const CircleBorder(),
                          child: SvgPicture.asset(
                            'assets/svgs/close-black.svg',
                            // colorFilter: const ColorFilter.mode(
                            //   AppColors.appBlackColor,
                            //   BlendMode.srcIn,
                            // ),
                          ),
                        ),
                      ),
                    ),
                  ],
                  leading: const SizedBox.shrink(),
                  centerTitle: true,
                  title: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      color: AppColors.appBlackColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Flexible(
                  child: Container(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: widget,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((onValue) {
      onChange?.call(onValue);
    });
  }

  static void showBottomSheetViewSignageCallButton(
    BuildContext context,
    String title, {
    Widget? widget,
    bool enableDrag = false,
    double radiusValue = 16,
    required Function? onCloseAll,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: enableDrag,
      useSafeArea: true,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(radiusValue),
          topRight: Radius.circular(radiusValue),
        ),
      ),
      builder: (BuildContext context) {
        final marginTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return FractionallySizedBox(
          heightFactor: marginTop > 0 ? .94 : 1,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                if (enableDrag)
                  Container(
                    width: 50,
                    height: 8,
                    margin: const EdgeInsets.only(top: 8),
                    decoration: BoxDecoration(
                      color: AppColors.homeBorderColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                AppBar(
                  iconTheme: IconThemeData(
                    color: AppColors.appBlackColor.withValues(alpha: 0.6),
                  ),
                  backgroundColor: AppColors.appTransparentColor,
                  elevation: 0,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: SizedBox(
                        width: 40,
                        child: MaterialButton(
                          elevation: 0,
                          highlightElevation: 0,
                          hoverElevation: 0,
                          hoverColor: AppColors.lightPrimaryBackgroundColor,
                          onPressed: () => {
                            Navigator.of(context).pop(true),
                            // onCloseAll?.call(true)
                          },
                          // color: AppColors.lightPrimaryBackgroundColor,
                          padding: EdgeInsets.zero,
                          shape: const CircleBorder(),
                          child: SvgPicture.asset(
                            'assets/svgs/close.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.appBlackColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                  leading: onCloseAll != null
                      ? Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: SizedBox(
                            width: 40,
                            child: MaterialButton(
                              elevation: 0,
                              highlightElevation: 0,
                              hoverElevation: 0,
                              hoverColor: AppColors.lightPrimaryBackgroundColor,
                              onPressed: () => {
                                Navigator.of(context).pop(),
                              },
                              // color: AppColors.lightPrimaryBackgroundColor,
                              padding: EdgeInsets.zero,
                              shape: const CircleBorder(),
                              child: SvgPicture.asset(
                                'assets/svgs/arrow-back-close.svg',
                                colorFilter: const ColorFilter.mode(
                                  AppColors.appBlackColor,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                  centerTitle: true,
                  title: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      color: AppColors.appBlackColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Flexible(
                  child: Container(
                    // padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: widget,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((value) {
      if (onCloseAll != null && value != null && value == true) {
        Navigator.of(context).pop(true);
      }
    });
  }

  static void openShowModalDraggableScrollableSheet(
    BuildContext context, {
    String? title = '',
    Widget? widget,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
    Function? onTap,
    bool isLeading = true,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(top: 24),
          child: DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: .95,
            snap: true,
            expand: false, // <- dismiss modal
            snapSizes: const [
              0.7,
              // 1,
            ],
            builder: (BuildContext context, ScrollController scrollController) {
              return ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Container(
                  color: AppColors.lightBoxBackgroundColor,
                  child: Stack(
                    children: [
                      DecoratedBox(
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: ListView(
                          controller: scrollController,
                          physics: const AlwaysScrollableScrollPhysics(
                            parent: ClampingScrollPhysics(),
                            // parent: BouncingScrollPhysics(),
                          ),
                          children: [
                            const SizedBox(height: kToolbarHeight),
                            Container(child: widget),
                          ],
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: double.infinity,
                            color: Colors.white,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 50,
                                  height: 8,
                                  margin: const EdgeInsets.only(top: 4),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: AppColors.homeBorderColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: kToolbarHeight - 8,
                            child: AppBar(
                              backgroundColor: Colors.white,
                              elevation: 0,
                              iconTheme: const IconThemeData(color: Colors.black),
                              // centerTitle: false,
                              // automaticallyImplyLeading: false,
                              // titleSpacing: 16,
                              // leadingWidth: 0,
                              title: Text(
                                title ?? '',
                                maxLines: 2,
                                style: const TextStyle(
                                  color: AppColors.darkPrimaryBackgroundColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              leading: isLeading
                                  ? IconButton(
                                      icon: SvgPicture.asset('assets/svgs/edit.svg'),
                                      onPressed: () {
                                        Navigator.of(context).pop();

                                        // _onEdit(item);
                                        onTap?.call();
                                      },
                                    )
                                  : const SizedBox.shrink(),
                              actions: [
                                IconButton(
                                  icon: SvgPicture.asset(
                                    'assets/svgs/close.svg',
                                  ),
                                  onPressed: () => Navigator.of(context).pop(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static void openShowModalTOUpcomingOfSegmentBottomSheet(
    BuildContext context, {
    String? title = '',
    Widget? widget,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    double initialChildSize = .7,
    ValueChanged? onChanged,
    Function? onTap,
    bool isLeading = true,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(top: 24),
          child: DraggableScrollableSheet(
            initialChildSize: initialChildSize,
            maxChildSize: .95,
            snap: true,
            expand: false, // <- dismiss modal
            snapSizes: [
              initialChildSize,
              .95,
            ],
            builder: (BuildContext context, ScrollController scrollController) {
              return ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Container(
                  color: AppColors.lightBoxBackgroundColor,
                  child: Stack(
                    children: [
                      DecoratedBox(
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: ListView(
                          controller: scrollController,
                          physics: const AlwaysScrollableScrollPhysics(
                            parent: ClampingScrollPhysics(),
                            // parent: BouncingScrollPhysics(),
                          ),
                          children: [
                            const SizedBox(height: kToolbarHeight),
                            Container(child: widget),
                          ],
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: double.infinity,
                            color: Colors.white,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 50,
                                  height: 8,
                                  margin: const EdgeInsets.only(top: 4),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: AppColors.homeBorderColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: kToolbarHeight - 8,
                            child: AppBar(
                              backgroundColor: Colors.white,
                              elevation: 0,
                              iconTheme: const IconThemeData(color: Colors.black),
                              // centerTitle: false,
                              // automaticallyImplyLeading: false,
                              // titleSpacing: 16,
                              // leadingWidth: 0,
                              title: Text(
                                title ?? '',
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                  color: AppColors.darkPrimaryBackgroundColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              centerTitle: true,
                              titleSpacing: 0,
                              leading: isLeading
                                  ? IconButton(
                                      icon: SvgPicture.asset('assets/svgs/edit.svg'),
                                      onPressed: () {
                                        Navigator.of(context).pop();

                                        // _onEdit(item);
                                        onTap?.call();
                                      },
                                    )
                                  : const SizedBox.shrink(),
                              actions: [
                                IconButton(
                                  icon: SvgPicture.asset(
                                    'assets/svgs/close.svg',
                                  ),
                                  onPressed: () => Navigator.of(context).pop(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static void openShowModalCustomerButtomSheet(
    BuildContext context, {
    String? title = '',
    String? titleValue = '',
    Widget? widget,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
    Function? onTap,
    bool isLeading = true,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.only(top: 24),
          child: DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: .95,
            snap: true,
            expand: false, // <- dismiss modal
            snapSizes: const [
              0.7,
              // 1,
            ],
            builder: (BuildContext context, ScrollController scrollController) {
              return ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Container(
                  color: AppColors.lightBoxBackgroundColor,
                  child: Stack(
                    children: [
                      DecoratedBox(
                        decoration: const BoxDecoration(
                          color: Colors.white,
                        ),
                        child: ListView(
                          controller: scrollController,
                          physics: const AlwaysScrollableScrollPhysics(
                            parent: ClampingScrollPhysics(),
                            // parent: BouncingScrollPhysics(),
                          ),
                          children: [
                            const SizedBox(height: kToolbarHeight),
                            Container(child: widget),
                          ],
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: double.infinity,
                            color: Colors.white,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  width: 50,
                                  height: 8,
                                  margin: const EdgeInsets.only(top: 4),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color: AppColors.homeBorderColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: kToolbarHeight - 8,
                            child: AppBar(
                              backgroundColor: Colors.white,
                              elevation: 0,
                              iconTheme: const IconThemeData(color: Colors.black),
                              centerTitle: false,
                              automaticallyImplyLeading: false,
                              titleSpacing: 16,
                              leadingWidth: 0,
                              title: Row(
                                children: [
                                  Text(
                                    title ?? '',
                                    style: const TextStyle(
                                      color: AppColors.darkPrimaryBackgroundColor,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    ' $titleValue',
                                    style: const TextStyle(
                                      color: AppColors.darkPrimaryBackgroundColor,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                              leading: isLeading
                                  ? IconButton(
                                      icon: SvgPicture.asset('assets/svgs/edit.svg'),
                                      onPressed: () {
                                        Navigator.of(context).pop();

                                        // _onEdit(item);
                                        onTap?.call();
                                      },
                                    )
                                  : const SizedBox.shrink(),
                              actions: [
                                IconButton(
                                  icon: SvgPicture.asset(
                                    'assets/svgs/close.svg',
                                  ),
                                  onPressed: () => Navigator.of(context).pop(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  static void openShowModalBottomSheetSetHight(
    BuildContext context, {
    required Widget widget,
    String? title,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            16.0,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      builder: (context) => SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            top: 10,
            right: 16,
            left: 16,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.close,
                      size: 18,
                      color: AppColors.appTransparentColor,
                    ),
                    splashRadius: 16,
                  ),
                  Expanded(
                    child: Center(
                      child: Text(
                        title ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      size: 24,
                      color: AppColors.appImageIconColor,
                    ),
                    splashRadius: 16,
                  ),
                ],
              ),
              Container(child: widget),
            ],
          ),
        ),
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetOfferTag(
    BuildContext context, {
    required Widget widget,
    String? title,
    ValueChanged? onChanged,
    double paddingTop = 10,
    double paddingRight = 16,
    double paddingLeft = 16,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            16.0,
          ),
        ),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        final marginTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(16),
            ),
          ),
          margin: EdgeInsets.only(top: marginTop),
          padding: EdgeInsets.only(
            top: paddingTop,
            right: paddingRight,
            left: paddingLeft,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(child: widget),
        );
      },
      // builder: (context) => SingleChildScrollView(
      //   child: Padding(
      //     padding: EdgeInsets.only(
      //       top: 10,
      //       right: 16,
      //       left: 16,
      //       bottom: MediaQuery.of(context).viewInsets.bottom,
      //     ),
      //     child: Column(
      //       crossAxisAlignment: CrossAxisAlignment.start,
      //       mainAxisSize: MainAxisSize.min,
      //       children: [
      //         Row(
      //           children: [
      //             IconButton(
      //               onPressed: () {},
      //               icon: const Icon(
      //                 Icons.close,
      //                 size: 18,
      //                 color: AppColors.appTransparentColor,
      //               ),
      //               splashRadius: 16,
      //             ),
      //             Expanded(
      //               child: Center(
      //                 child: Text(
      //                   title ?? '',
      //                   style: const TextStyle(
      //                     fontSize: 18,
      //                     fontWeight: FontWeight.w600,
      //                   ),
      //                 ),
      //               ),
      //             ),
      //             IconButton(
      //               onPressed: () => Navigator.pop(context),
      //               icon: const Icon(
      //                 Icons.close,
      //                 size: 24,
      //                 color: AppColors.appImageIconColor,
      //               ),
      //               splashRadius: 16,
      //             ),
      //           ],
      //         ),
      //         Container(child: widget),
      //       ],
      //     ),
      //   ),
      // ),
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetTableService(
    BuildContext context, {
    required Widget widget,
    String? title,
    ValueChanged? onChanged,
    double paddingTop = 10,
    double paddingRight = 16,
    double paddingLeft = 16,
    bool isDismissible = true,
    bool enableDrag = true,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            16.0,
          ),
        ),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      isScrollControlled: true,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      builder: (context) {
        final marginTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(16),
            ),
          ),
          margin: EdgeInsets.only(top: marginTop),
          padding: EdgeInsets.only(
            top: paddingTop,
            right: paddingRight,
            left: paddingLeft,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(child: widget),
        );
      },
      // builder: (context) => SingleChildScrollView(
      //   child: Padding(
      //     padding: EdgeInsets.only(
      //       top: 10,
      //       right: 16,
      //       left: 16,
      //       bottom: MediaQuery.of(context).viewInsets.bottom,
      //     ),
      //     child: Column(
      //       crossAxisAlignment: CrossAxisAlignment.start,
      //       mainAxisSize: MainAxisSize.min,
      //       children: [
      //         Row(
      //           children: [
      //             IconButton(
      //               onPressed: () {},
      //               icon: const Icon(
      //                 Icons.close,
      //                 size: 18,
      //                 color: AppColors.appTransparentColor,
      //               ),
      //               splashRadius: 16,
      //             ),
      //             Expanded(
      //               child: Center(
      //                 child: Text(
      //                   title ?? '',
      //                   style: const TextStyle(
      //                     fontSize: 18,
      //                     fontWeight: FontWeight.w600,
      //                   ),
      //                 ),
      //               ),
      //             ),
      //             IconButton(
      //               onPressed: () => Navigator.pop(context),
      //               icon: const Icon(
      //                 Icons.close,
      //                 size: 24,
      //                 color: AppColors.appImageIconColor,
      //               ),
      //               splashRadius: 16,
      //             ),
      //           ],
      //         ),
      //         Container(child: widget),
      //       ],
      //     ),
      //   ),
      // ),
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetVoucherEdit(
    BuildContext context, {
    required Widget widget,
    String? title,
    ValueChanged? onChanged,
    double paddingTop = 10,
    double paddingRight = 16,
    double paddingLeft = 16,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');

    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.0),
        ),
      ),
      backgroundColor: Colors.transparent,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        final marginTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(16),
            ),
          ),
          margin: EdgeInsets.only(top: marginTop),
          padding: EdgeInsets.only(
            top: paddingTop,
            right: paddingRight,
            left: paddingLeft,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(child: widget),
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetOffer(
    BuildContext context,
    String language, {
    required Widget widget,
    Widget? widgetButtom,
    String? title,
    ValueChanged? onChanged,
    final Search? user,
  }) {
    final l10n = context.l10n;
    final displayName = user?.user?.displayName ?? l10n.guest;
    final points = AppValidations.getTotalPrice(num.parse(user?.totalPoints ?? '0'), language: language);
    final pointsText = (num.parse(user?.totalPoints ?? '0') > 1 || num.parse(user?.totalPoints ?? '0') < -1) ? l10n.points : l10n.point;
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            16.0,
          ),
        ),
      ),
      backgroundColor: AppColors.appTransparentColor,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return Container(
          margin: EdgeInsets.only(top: paddingTop),
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: const BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Center(
                child: Container(
                  width: 50,
                  height: 8,
                  margin: const EdgeInsets.only(top: 4),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: AppColors.homeBorderColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.close,
                      size: 18,
                      color: AppColors.appTransparentColor,
                    ),
                    splashRadius: 16,
                  ),
                  Expanded(
                    child: Center(
                      child: Text(
                        title ?? '',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      size: 24,
                      color: AppColors.appImageIconColor,
                    ),
                    splashRadius: 16,
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(bottom: 10.0, left: 16, right: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: AppColors.appBlackColor,
                        fontSize: 16,
                      ),
                    ),
                    RichText(
                      text: TextSpan(
                        children: <InlineSpan>[
                          TextSpan(
                            text: points,
                            style: const TextStyle(
                              color: AppColors.appBlackColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          TextSpan(
                            text: ' $pointsText',
                            style: const TextStyle(
                              color: AppColors.appBlackColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Flexible(child: SingleChildScrollView(child: Container(child: widget))),
              Container(
                child: widgetButtom,
              ),
            ],
          ),
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetOfferPrinter(
    BuildContext context, {
    required Widget widget,
    Widget? widgetBottom,
    String? title,
    ValueChanged? onChanged,
    final Search? user,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            16.0,
          ),
        ),
      ),
      backgroundColor: AppColors.appTransparentColor,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return Container(
          margin: EdgeInsets.only(top: paddingTop),
          // padding: EdgeInsets.only(
          //     // top: 10,
          //     // right: 16,
          //     // left: 16,
          //     // bottom: MediaQuery.of(context).viewInsets.bottom,
          //     ),
          decoration: const BoxDecoration(
            color: AppColors.appTransparentColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
          ),
          child: FractionallySizedBox(
            heightFactor: 1,
            child: Column(
              children: [
                Flexible(child: widget),
              ],
            ),
          ),
        );
        // return Container(
        //   margin: EdgeInsets.only(top: paddingTop),
        //   padding: EdgeInsets.only(
        //     // top: 10,
        //     // right: 16,
        //     // left: 16,
        //     bottom: MediaQuery.of(context).viewInsets.bottom,
        //   ),
        //   decoration: const BoxDecoration(
        //     color: AppColors.lightPrimaryBackgroundColor,
        //     borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        //   ),
        //   child: Scaffold(
        //     body: Column(
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       mainAxisSize: MainAxisSize.min,
        //       children: [
        //         Center(
        //           child: Container(
        //             width: 50,
        //             height: 8,
        //             margin: const EdgeInsets.only(top: 4),
        //             alignment: Alignment.center,
        //             decoration: BoxDecoration(
        //               color: AppColors.homeBorderColor,
        //               borderRadius: BorderRadius.circular(10),
        //             ),
        //           ),
        //         ),
        //         Row(
        //           children: [
        //             IconButton(
        //               onPressed: () {},
        //               icon: const Icon(
        //                 Icons.close,
        //                 size: 18,
        //                 color: AppColors.appTransparentColor,
        //               ),
        //               splashRadius: 16,
        //             ),
        //             Expanded(
        //               child: Center(
        //                 child: Text(
        //                   title ?? '',
        //                   style: const TextStyle(
        //                     fontSize: 18,
        //                     fontWeight: FontWeight.w600,
        //                   ),
        //                 ),
        //               ),
        //             ),
        //             Container(
        //               child: widgetTags ?? const SizedBox.shrink(),
        //             ),
        //             IconButton(
        //               onPressed: () => Navigator.pop(context),
        //               icon: const Icon(
        //                 Icons.close,
        //                 size: 24,
        //                 color: AppColors.darkPrimaryBackgroundColor,
        //               ),
        //               splashRadius: 16,
        //             ),
        //           ],
        //         ),
        //         Container(child: widget),
        //         // Container(
        //         //   child: widgetBottom,
        //         // )
        //       ],
        //     ),
        //     // bottomNavigationBar: Container(
        //     //   child: widgetButtom,
        //     // ),
        //   ),
        // );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openShowModalBottomSheetChangePasswordAccount(
    BuildContext context, {
    required Widget widget,
    String? title,
    ValueChanged? onChanged,
  }) {
    setAnalytics(widget.runtimeType.toString(), '${widget.runtimeType.toString().toLowerCase()}-bottom-sheet');
    showModalBottomSheet(
      useSafeArea: true,
      // shape: const RoundedRectangleBorder(
      //   borderRadius: BorderRadius.vertical(
      //     top: Radius.circular(
      //       16.0,
      //     ),
      //   ),
      // ),
      backgroundColor: Colors.transparent,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;

        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.lightPrimaryBackgroundColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
            ),
            margin: EdgeInsets.only(top: paddingTop),
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SafeArea(
              bottom: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: Container(
                      width: 50,
                      height: 8,
                      margin: const EdgeInsets.only(top: 8),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.homeBorderColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {},
                        icon: const Icon(
                          Icons.close,
                          size: 18,
                          color: AppColors.appTransparentColor,
                        ),
                        splashRadius: 16,
                      ),
                      Expanded(
                        child: Center(
                          child: Text(
                            title ?? '',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.close,
                          size: 24,
                          color: AppColors.appImageIconColor,
                        ),
                        splashRadius: 16,
                      ),
                    ],
                  ),
                  Flexible(child: SingleChildScrollView(child: Container(child: widget))),
                ],
              ),
            ),
          ),
        );
      },
    ).then((value) => onChanged?.call(value));
  }

  static void openPROButtomSheetDraggableScrollable(
    BuildContext context, {
    Widget? widget,
    Widget? widgetButtom,
    Color? backgroundColor,
    double paddingTop = 24,
    bool enableDrag = false,
    double heightFactor = .8,
    ValueChanged? onChanged,
    Function? onTap,
    String? titleFeature,
  }) {
    showModalBottomSheet(
      useSafeArea: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final size = MediaQuery.of(context).size;
        AppLog.e('size: ${size.height}');
        // final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
        final double initialChildSize = size.height < 600
            ? .95
            : size.height < 900
                ? .7
                : size.height < 1000
                    ? .6
                    : .5;

        final double height = size.width < 460
            ? 180
            : size.width < 960
                ? 280
                : 300;
        final heightPosision = size.height < 600 ? height / 3.3 : height / 2;
        return Padding(
          padding: const EdgeInsets.only(top: 16),
          child: DraggableScrollableSheet(
            initialChildSize: initialChildSize,
            maxChildSize: .95,
            snap: false,
            expand: false,
            snapSizes: const [
              // 0.6,
              // 0.8,
            ],
            builder: (BuildContext context, ScrollController scrollController) {
              return Stack(
                children: [
                  Positioned.fill(
                    top: 32,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: AppColors.lightPrimaryBackgroundColor,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(32),
                          topRight: Radius.circular(32),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(bottom: 16),
                    decoration: const BoxDecoration(
                      color: AppColors.appTransparentColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(24),
                        topRight: Radius.circular(24),
                      ),
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: ListView(
                            controller: scrollController,
                            physics: const NeverScrollableScrollPhysics(),
                            // physics: const AlwaysScrollableScrollPhysics(
                            //   parent: ClampingScrollPhysics(),
                            // ),
                            children: [
                              // const SizedBox(height: kToolbarHeight),
                              Stack(
                                children: [
                                  Container(
                                    width: double.infinity,
                                    height: height,
                                    decoration: const BoxDecoration(
                                      // borderRadius: const BorderRadius.only(
                                      //   topLeft: Radius.circular(16),
                                      //   topRight: Radius.circular(16),
                                      // ),
                                      image: DecorationImage(
                                        image: AssetImage('assets/svgs/dialog-upgrade-bg.png'),
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    right: 16,
                                    top: 32,
                                    child: SizedBox(
                                      width: 40,
                                      child: MaterialButton(
                                        elevation: 0,
                                        highlightElevation: 0,
                                        hoverElevation: 0,
                                        hoverColor: AppColors.lightPrimaryBackgroundColor,
                                        onPressed: () => {
                                          Navigator.of(context).pop(true),
                                        },
                                        color: AppColors.lightPrimaryBackgroundColor,
                                        padding: EdgeInsets.zero,
                                        shape: const CircleBorder(),
                                        child: SvgPicture.asset(
                                          'assets/svgs/close.svg',
                                          colorFilter: const ColorFilter.mode(
                                            AppColors.appBlackColor,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Positioned.fill(
                                  //   top: heightPosision,
                                  //   child: Container(
                                  //     color: AppColors.appTransparentColor,
                                  //     child: widget,
                                  //   ),
                                  // )
                                ],
                              ),
                              // Transform.translate(
                              //   offset: Offset(0, heightPosision),
                              //   child: Container(
                              //     color: AppColors.appTransparentColor,
                              //     child: widget,
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                        // Container(
                        //   child: widgetButtom,
                        // )
                      ],
                    ),
                  ),
                  Positioned.fill(
                    top: heightPosision,
                    child: Container(
                      color: AppColors.appTransparentColor,
                      child: widget,
                    ),
                  ),
                  Positioned(
                    left: 0,
                    bottom: 12,
                    right: 0,
                    child: Container(
                      child: widgetButtom,
                    ),
                  )
                ],
              );
            },
          ),
        );
      },
    );
  }

  static void lockScreen(
    BuildContext context, {
    String? title,
    ValueChanged? onChanged,
  }) {
    final l10n = context.l10n;
    screenLock(
      context: context,
      correctString: '1111',
      // correctString: '123456',
      // useBlur: false,
      title: Text(
        '${l10n.pleaseEnterPasscode}(1111)',
        style: const TextStyle(
          fontSize: 18,
          color: AppColors.lightPrimaryBackgroundColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      canCancel: false,
      maxRetries: 4,
      config: const ScreenLockConfig(
        /// If you don't want it to be transparent, override the config
        backgroundColor: AppColors.darkPrimaryBackgroundColor,
        titleTextStyle: TextStyle(fontSize: 24),
      ),
      secretsConfig: const SecretsConfig(
        secretConfig: SecretConfig(
          // borderColor: Colors.amber,
          borderSize: 2.0,
          // builder: (context, config, enabled) => Container(
          //   decoration: BoxDecoration(
          //     shape: BoxShape.rectangle,
          //     color: enabled ? config.enabledColor : config.disabledColor,
          //     border: Border.all(
          //       width: config.borderSize,
          //       color: config.borderColor,
          //     ),
          //   ),
          //   padding: const EdgeInsets.all(10),
          //   width: config.size,
          //   height: config.size,
          // ),
        ),
      ),
      keyPadConfig: KeyPadConfig(
        buttonConfig: KeyPadButtonConfig(
          buttonStyle: OutlinedButton.styleFrom(
            textStyle: const TextStyle(
                // color: Colors.orange,
                // fontWeight: FontWeight.bold,
                ),
            shape: const RoundedRectangleBorder(),
            // backgroundColor: Colors.deepOrange,
          ),
        ),
        // displayStrings: [
        //   '0',
        //   '1',
        //   '2',
        //   '3',
        //   '4',
        //   '5',
        //   '6',
        //   '7',
        //   '8',
        //   '9',
        // ],
      ),
    );

//     screenLockCreate(
//       context: context,
// // correctString:'',
//       title: const Text('change title'),
//       confirmTitle: const Text('change confirm title'),
//       onConfirmed: (value) => Navigator.of(context).pop(),
//       config: const ScreenLockConfig(
//         backgroundColor: Colors.deepOrange,
//         titleTextStyle: TextStyle(fontSize: 24),
//       ),
//       secretsConfig: SecretsConfig(
//         spacing: 15, // or spacingRatio
//         padding: const EdgeInsets.all(40),
//         secretConfig: SecretConfig(
//           borderColor: Colors.amber,
//           borderSize: 2.0,
//           disabledColor: Colors.black,
//           enabledColor: Colors.amber,
//           size: 15,
//           builder: (context, config, enabled) => Container(
//             decoration: BoxDecoration(
//               shape: BoxShape.rectangle,
//               color: enabled ? config.enabledColor : config.disabledColor,
//               border: Border.all(
//                 width: config.borderSize,
//                 color: config.borderColor,
//               ),
//             ),
//             padding: const EdgeInsets.all(10),
//             width: config.size,
//             height: config.size,
//           ),
//         ),
//       ),
//       keyPadConfig: KeyPadConfig(
//         buttonConfig: KeyPadButtonConfig(
//           buttonStyle: OutlinedButton.styleFrom(
//             textStyle: const TextStyle(
//               color: Colors.orange,
//               fontWeight: FontWeight.bold,
//             ),
//             shape: const RoundedRectangleBorder(),
//             backgroundColor: Colors.deepOrange,
//           ),
//         ),
//         displayStrings: [
//           '0',
//           '1',
//           '2',
//           '3',
//           '4',
//           '5',
//           '6',
//           '7',
//           '8',
//           '9',
//         ],
//       ),
//       cancelButton: const Icon(Icons.close),
//       deleteButton: const Icon(Icons.delete),
//     );
  }

  static Future<void> openSnackBar(
    BuildContext context,
    String msg, {
    String? labelAction,
    Color? backgroundColor,
    Function? onTap,
    Duration duration = const Duration(seconds: 3),
    ValueChanged? onChange,
  }) async {
    // final snackBar = SnackBar(
    //   behavior: SnackBarBehavior.floating,
    //   // dismissDirection: DismissDirection.none,
    //   padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
    //   backgroundColor: backgroundColor ?? AppColors.darkPrimaryBackgroundColor,
    //   duration: duration,
    //   content: Text(
    //     msg,
    //     style: const TextStyle(
    //       fontWeight: FontWeight.w400,
    //       fontSize: 16,
    //     ),
    //   ),
    //   action: labelAction != null
    //       ? SnackBarAction(
    //           label: labelAction,
    //           textColor: AppColors.lightPrimaryBackgroundColor,
    //           onPressed: () => onTap?.call(),
    //         )
    //       : null,
    // );
    return ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(
          behavior: SnackBarBehavior.floating,
          // dismissDirection: DismissDirection.none,
          padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
          backgroundColor: backgroundColor ?? AppColors.darkPrimaryBackgroundColor,
          duration: duration,
          content: Text(
            msg,
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 16,
            ),
          ),
          action: labelAction != null
              ? SnackBarAction(
                  label: labelAction,
                  textColor: AppColors.lightPrimaryBackgroundColor,
                  onPressed: () => onTap?.call(),
                )
              : null,
        ))
        .closed
        .then(
          (value) => {},
          // (value) => ScaffoldMessenger.of(context).clearSnackBars(),
        );
  }

  // static Future<void> openErrorPrintSnackBar(
  //   BuildContext context,
  //   String msg, {
  //   String? labelAction,
  //   Color? backgroundColor,
  //   Function? onTap,
  //   Duration duration = const Duration(seconds: 3),
  //   ValueChanged? onChange,
  // }) async {
  //   final snackBar = SnackBar(
  //     behavior: SnackBarBehavior.floating,
  //     dismissDirection: DismissDirection.none,
  //     padding: const EdgeInsets.only(left: 16, top: 16, bottom: 16),
  //     backgroundColor: backgroundColor ?? AppColors.homeBGSnackBarErrorColor,
  //     duration: duration,
  //     content: StatefulBuilder(
  //       builder: (context, setStateBuild) {
  //         int timeDuration = duration.inSeconds;
  //         // Tạo timer để giảm countdown
  //         Timer.periodic(const Duration(seconds: 1), (timer) {
  //           if (timeDuration >= 0) {
  //             setStateBuild(() {
  //               timeDuration--;
  //             });
  //           } else {
  //             // Hủy timer khi countdown về 0
  //             timer.cancel();
  //             ScaffoldMessenger.of(context).hideCurrentSnackBar();
  //           }
  //         });
  //         return Text(
  //           'Alooooo: $timeDuration',
  //           style: const TextStyle(
  //             fontWeight: FontWeight.w400,
  //             fontSize: 16,
  //           ),
  //         );
  //       },
  //     ),
  //     action: labelAction != null
  //         ? SnackBarAction(
  //             label: labelAction,
  //             textColor: AppColors.lightPrimaryBackgroundColor,
  //             onPressed: () => onTap?.call(),
  //           )
  //         : null,
  //   );
  //   return ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then(
  //         (value) => ScaffoldMessenger.of(context).clearSnackBars(),
  //       );
  // }

  static Future<void> openSnackBarNetwork(
    BuildContext context, {
    required String? msgNotNetwork,
    String? msgNetwork,
    String? labelAction,
    Color? backgroundColor,
    Function? onTap,
    Duration duration = const Duration(seconds: 3),
    ValueChanged? onChange,
  }) async {
    try {
      final snackBar = SnackBar(
        dismissDirection: DismissDirection.none,
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
        // backgroundColor: backgroundColor ?? AppColors.darkPrimaryBackgroundColor,
        backgroundColor: AppColors.appErrorColor,
        duration: duration,
        content: Row(
          children: [
            const Padding(
              padding: EdgeInsets.only(right: 8.0),
              child: Icon(
                Icons.warning,
                color: Colors.white,
                size: 18,
              ),
            ),
            Flexible(
              child: Text(
                msgNotNetwork ?? '',
                style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                ),
              ),
            ),
            Container(
              width: 12,
              height: 12,
              margin: const EdgeInsets.only(left: 4, top: 2),
              decoration: const BoxDecoration(
                color: Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: const CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            ),
          ],
        ),
      );
      return ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then(
        (value) {
          ScaffoldMessenger.of(context).clearSnackBars();
          // ScaffoldMessenger.of(context).hideCurrentSnackBar();
          final snackBar = SnackBar(
            dismissDirection: DismissDirection.none,
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 12),
            backgroundColor: AppColors.appSuccess,
            duration: const Duration(seconds: 2),
            content: Row(
              children: [
                const Padding(
                  padding: EdgeInsets.only(right: 8.0),
                  child: Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                Text(
                  msgNetwork ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar).closed.then((value) => onChange?.call(true));
        },
      );
    } catch (e) {}
  }

  static void showDialogYesNo(
    BuildContext context, {
    String? title,
    String? msgContent,
    String yesText = 'Yes',
    String? noText = 'No',
    Function? yesTap,
    Function? noTap,
    ValueChanged? onChanged,
    bool isClose = false,
  }) {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => AlertDialog(
        titlePadding: const EdgeInsets.only(top: 16, left: 16),
        contentPadding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
        actionsPadding: const EdgeInsets.only(bottom: 8, right: 12),
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(12.0))),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title ?? '',
              style: const TextStyle(
                fontSize: 20,
                // fontWeight: FontWeight.w600,
              ),
            ),
            if (isClose)
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: InkWell(
                  radius: 99,
                  borderRadius: BorderRadius.circular(99),
                  onTap: () => Navigator.pop(context),
                  child: Padding(
                    padding: const EdgeInsets.all(6),
                    child: SvgPicture.asset(
                      'assets/svgs/close-black.svg',
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
              )
          ],
        ),
        content: Text(
          msgContent ?? '',
          style: const TextStyle(
            fontSize: 16,
            // fontWeight: FontWeight.w600,
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => {Navigator.pop(context), noTap?.call()},
            child: Text(
              noText ?? '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.darkPrimaryBackgroundColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () => {Navigator.pop(context, 'OK'), yesTap?.call()},
            child: Text(
              yesText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.appColor,
              ),
            ),
          ),
        ],
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void showDialogYesNoCheckBussiness(
    BuildContext context, {
    String? title,
    String? msgContent,
    String yesText = 'Yes',
    String? noText = 'No',
    Function? yesTap,
    Function? noTap,
    ValueChanged? onChanged,
  }) {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => AlertDialog(
        titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
        contentPadding: const EdgeInsets.fromLTRB(24, 12, 24, 24),
        actionsPadding: const EdgeInsets.only(left: 16, bottom: 8, right: 16),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(12.0),
          ),
        ),
        title: Text(
          title ?? '',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          msgContent ?? '',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
        ),
        scrollable: true,
        actions: <Widget>[
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () => {Navigator.pop(context), noTap?.call()},
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    noText ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.appLabelTextColor,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: TextButton(
                  onPressed: () => {Navigator.pop(context, 'OK'), yesTap?.call()},
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    backgroundColor: AppColors.appColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    yesText,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.lightPrimaryBackgroundColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void showDialogConfirmMarkComplete(
    BuildContext context, {
    String? title,
    String? msgContent,
    String onlyText = 'Only this voucher',
    String allText = 'All future vouchers',
    String? noText = 'Cancel',
    Function? onlyTap,
    Function? allTap,
    Function? noTap,
    ValueChanged? onChanged,
  }) {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => AlertDialog(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(12.0))),
        title: Text(
          title ?? '',
          style: const TextStyle(
            fontSize: 24,
            // fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          msgContent ?? '',
          style: const TextStyle(
            fontSize: 18,
            // fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          ButtonLoading(
            height: 48,
            label: onlyText,
            buttonBackgroundColor: AppColors.appBlueColor,
            fontWeight: FontWeight.w600,
            labelColor: AppColors.lightPrimaryBackgroundColor,
            callback: () => {Navigator.pop(context, 'only'), onlyTap?.call()},
          ),
          const SizedBox(
            height: 8,
          ),
          ButtonLoading(
            height: 48,
            label: allText,
            buttonBackgroundColor: AppColors.appBlueColor.withValues(alpha: .1),
            labelColor: AppColors.appBlueColor,
            fontWeight: FontWeight.w600,
            callback: () => {Navigator.pop(context, 'all'), allTap?.call()},
          ),
          const SizedBox(
            height: 8,
          ),
          ButtonLoading(
            height: 48,
            label: noText ?? '',
            buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
            fontWeight: FontWeight.w600,
            labelColor: AppColors.darkPrimaryBackgroundColor,
            side: BorderSide(color: AppColors.appBlackColor.withValues(alpha: .4), width: 1),
            callback: () => {Navigator.pop(context), noTap?.call()},
          ),
        ],
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void showDialogPrinError(
    BuildContext context, {
    String? title,
    Widget? widget,
    // String? msgContent,
    // String yesText = 'Yes',
    // String? noText = 'No',
    // Function? yesTap,
    // Function? noTap,
    ValueChanged? onChanged,
  }) {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => AlertDialog(
        backgroundColor: AppColors.darkPrimaryBackgroundColor,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(12.0))),
        title: Row(
          children: [
            Expanded(
              child: Text(
                title ?? '',
                style: const TextStyle(
                  fontSize: 24, color: AppColors.lightPrimaryBackgroundColor,
                  // fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SvgPicture.asset(
              'assets/svgs/close.svg',
              width: 24,
              height: 24,
              colorFilter: const ColorFilter.mode(
                AppColors.lightPrimaryBackgroundColor,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
        content: Flexible(
          child: Container(
            child: widget,
          ),
        ),
        // content: Text(
        //   msgContent ?? '',
        //   style: const TextStyle(
        //     fontSize: 18,
        //     // fontWeight: FontWeight.w600,
        //   ),
        // ),
        // actions: <Widget>[
        //   TextButton(
        //     onPressed: () => {Navigator.pop(context), noTap?.call()},
        //     child: Text(
        //       noText ?? '',
        //       style: const TextStyle(
        //         fontSize: 16,
        //         fontWeight: FontWeight.w500,
        //         color: AppColors.darkPrimaryBackgroundColor,
        //       ),
        //     ),
        //   ),
        //   TextButton(
        //     onPressed: () => {Navigator.pop(context, 'OK'), yesTap?.call()},
        //     child: Text(
        //       yesText,
        //       style: const TextStyle(
        //         fontSize: 16,
        //         fontWeight: FontWeight.w500,
        //         color: AppColors.appColor,
        //       ),
        //     ),
        //   ),
        // ],
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void showDialogUpgrade(
    BuildContext context, {
    Widget? widget,
    String yesText = 'Yes',
    String? noText = 'No',
    Function? yesTap,
    Function? noTap,
    ValueChanged? onChanged,
    bool isShowNo = true,
    bool isShowYes = true,
    bool isCloseShowYes = true,
  }) {
    showDialog<String>(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) => PopScope(
        canPop: false,
        onPopInvokedWithResult: (value, f) async {},
        child: AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(16.0))),
          // title: Text(
          //   title ?? '',
          //   style: const TextStyle(
          //     fontSize: 24,
          //     // fontWeight: FontWeight.w600,
          //   ),
          // ),
          content: widget,
          actionsPadding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
          actions: <Widget>[
            if (isShowNo)
              TextButton(
                onPressed: () => {Navigator.pop(context), noTap?.call()},
                child: Text(
                  noText ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.darkPrimaryBackgroundColor,
                  ),
                ),
              ),
            if (isShowYes)
              TextButton(
                onPressed: () {
                  if (isCloseShowYes) {
                    Navigator.pop(context, 'OK');
                  }
                  yesTap?.call();
                },
                child: Text(
                  yesText,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.appColor,
                  ),
                ),
              ),
          ],
        ),
      ),
    ).then((value) => onChanged?.call(value));
  }

  static void showDialogMenu(
    BuildContext context,
    Widget widget, {
    bool barrierDismissible = true,
    ValueChanged<bool>? onChange,
    // String? msgContent,
    // String yesText = 'Yes',
    // String? noText = 'No',
    // Function? yesTap,
    // Function? noTap,
  }) {
    // double width = MediaQuery.of(context).size.width;
    showDialog<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext context) => AlertDialog(
        insetPadding: const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(8),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(24.0),
          ),
        ),
        content: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 600,
            minWidth: 500,
          ),
          child: widget,
        ),
      ),
    ).then((value) {
      onChange?.call(value ?? false);
      return value;
    });
  }

  static void onShowCustomerTags(
    BuildContext context,
    Widget widget, {
    ValueChanged? onChanged,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.sizeOf(context).height * .6,
                ),
                color: Colors.white,
                child: widget,
              ),
            ),
          ),
        );
      },
    ).then((onValue) {
      onChanged!.call(false);
    });
  }

  static Future<void> openCustomerSummaryAI(BuildContext context, String uid) async {
    try {
      final paddingTop = MediaQueryData.fromView(WidgetsBinding.instance.platformDispatcher.views.single).padding.top;
      await showModalBottomSheet(
        useSafeArea: true,
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) => Container(
          margin: EdgeInsets.only(top: paddingTop),
          padding: MediaQuery.of(context).viewInsets,
          child: DraggableScrollableSheet(
            expand: false,
            initialChildSize: 0.7,
            maxChildSize: 1,
            builder: (BuildContext context, ScrollController scrollController) {
              return DecoratedBox(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: SafeArea(
                    top: false,
                    bottom: false,
                    child: CustomerAiSummaryWidget(
                      scrollController: scrollController,
                      uid: uid,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      );
    } catch (e) {}
  }

  static void setAnalytics(String screenNamePage, String screenClassPage) {
    try {
      final screenName = appEnv.id != AppEnvironment.production ? '[Dev]-[Manager]-$screenNamePage' : '[Manager]-$screenNamePage';
      final screenClass = appEnv.id != AppEnvironment.production ? '[Dev]-[Manager]-$screenClassPage' : '[Manager]-$screenClassPage';
      if (appEnv.id == AppEnvironment.production) {
        analytics.logScreenView(
          screenName: screenName,
          screenClass: screenClass,
        );
      } else {}
      doAuditLogging(
        pageName: screenName,
        pageUrl: screenClass,
        pageAction: 'Open Page',
      );
    } catch (e) {}
  }

  static void doAuditLogging({
    String? pageName,
    String? pageUrl,
    String? message,
    Map<String, dynamic>? metadata,
    String pageAction = 'Open Page',
  }) {
    try {
      AuditLoggingService().doAuditLogging(
        Audit(
          pageName: pageName,
          pageUrl: pageUrl,
          pageAction: pageAction,
          message: message,
          metadata: metadata,
        ),
      );
    } catch (e) {}
  }
}
