// ignore_for_file: avoid_positional_boolean_parameters, unawaited_futures

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stickyqrbusiness/@const/config.dart';
import 'package:stickyqrbusiness/@core/models/chat-message.model.dart';
import 'package:stickyqrbusiness/@core/models/models.dart';
import 'package:stickyqrbusiness/@core/models/receipt-template.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth.model.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';

class AppLocalStorage {
  /// GET

  /// Get Access Token
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConfig.localAccessToken.key);
  }

  /// Get Refresh Token
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConfig.localRefreshToken.key);
  }

  static Future<String?> getExpires() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConfig.localExpiresToken.key);
  }

  /// Get UserID
  static Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConfig.localUserId.key);
  }

  /// Get Local Data
  static dynamic getLocal(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.get(key);
  }

  /// SAVE

  /// Save Local
  static Future<void> saveLocal(StorageKeys storeKey, String val) async {
    final prefs = await SharedPreferences.getInstance();
    if (val != null) {
      await prefs.setString(storeKey.key, storeKey.isJsonObject ? jsonEncode(val) : val);
    }
  }

  /// Save Auth Model with Token/User
  static void saveAuthModel(Auth authModel) {
    debugPrint('accessToken: ${authModel.accessToken}');
    saveAccessToken(authModel.accessToken);
    saveRefreshToken(authModel.refreshToken);
    saveExpiresToken(authModel.expiresIn);
    saveBusiness(authModel.business);
    savePermission(authModel.permissions);
    saveRoleIdBusiness('');
    saveLocal(AppConfig.localUserId, '${authModel.userId}');
  }

  static void saveTokenExpires(Auth authModel) {
    saveAccessToken(authModel.accessToken);
    saveRefreshToken(authModel.refreshToken);
    saveExpiresToken(authModel.expiresIn);
  }

  /// Save Access Token only
  static void saveAccessToken(String? val) {
    saveLocal(AppConfig.localAccessToken, '$val');
  }

  /// Save Refresh Token only
  static void saveRefreshToken(String? val) {
    saveLocal(AppConfig.localRefreshToken, '$val');
  }

  static void saveExpiresToken(String? val) {
    saveLocal(AppConfig.localExpiresToken, '$val');
  }

  /// SAVE PRINTERS
  static Future<void> savePrinters(List<Printer> val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localPrintersData.key, jsonEncode(val));
  }

  static Future<void> saveBusiness(Business? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localBusinessData.key, jsonEncode(val));
  }

  static Future<void> saveFeatures(List<Feature>? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localFeatures.key, jsonEncode(val));
  }

  static Future<void> saveBusinessMultipleHomeWidget(List<MultipleHome>? val) async {
    AppLog.e('saveBusinessMultipleHomeWidget...');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localBusinessMultipleHomeData.key, jsonEncode(val));
  }

  static Future<void> saveUser(User? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localUserData.key, jsonEncode(val));
  }

  static Future<void> savePermission(List<Permission>? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localUserPermission.key, jsonEncode(val));
  }

  static void saveRoleIdBusiness(String val) {
    saveLocal(AppConfig.localRoleIdBusiness, val);
  }

  static Future<void> saveLanguage(Locale? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localLanguageData.key, val.toString());
    // await prefs.setString(AppConfig.localLanguageData.key, jsonEncode(val));
  }

  static Future<void> saveLabels(List<Label>? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localLabelsData.key, jsonEncode(val));
  }

  static Future<void> saveLabelsOffer(List<Label>? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localLabelsOfferData.key, jsonEncode(val));
  }

  static Future<bool?> saveFirsrMultiHome(bool? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConfig.localFirstMultiHomeScreen.key, val ?? true);
    return true;
  }

  static Future<bool?> saveFirsrNewHome(bool? val, {Duration? expiry}) async {
    final prefs = await SharedPreferences.getInstance();

    if (expiry != null) {
      final now = DateTime.now();
      final expiryDateTime = now.add(expiry);
      final expiryTime = expiryDateTime.millisecondsSinceEpoch;
      final Map<String, dynamic> dataToStore = {
        'value': val ?? true,
        'expiryTime': expiryTime,
      };
      await prefs.setString(AppConfig.localFirstNewHomeScreen.key, jsonEncode(dataToStore));
    } else {
      await prefs.setBool(AppConfig.localFirstNewHomeScreen.key, val ?? true);
    }

    return true;
  }

  static Future<bool?> saveHideProPlan(bool? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConfig.localHideProPlan.key, val ?? false);
    return true;
  }

  static Future<void> saveCustomerReceipt(ReceiptTemplate? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localCustomerReceipt.key, jsonEncode(val));
  }

  static Future<void> saveKitchenReceipt(ReceiptTemplate? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConfig.localKitchenReceipt.key, jsonEncode(val));
  }

  static Future<void> saveAISupport(List<ChatMessage>? val, Duration expiry) async {
    final prefs = await SharedPreferences.getInstance();

    final expiryTime = DateTime.now().add(expiry).millisecondsSinceEpoch;

    final Map<String, dynamic> dataToStore = {
      'value': val?.map((e) => e.toJson()).toList(),
      'expiryTime': expiryTime,
    };

    AppLog.e('Lưu dữ liệu với thời hạn: ${DateTime.fromMillisecondsSinceEpoch(expiryTime)}');
    await prefs.setString(AppConfig.localAISupport.key, jsonEncode(dataToStore));
  }

  static Future<List<Permission>> getPermission() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localUserPermission.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => Permission.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  static Future<List<Feature>> getMenuFeatures() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localFeatures.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => Feature.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  static Future<List<Label>> getLabelsOffer() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localLabelsOfferData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => Label.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  /// Save Started Guide
  static Future<bool?> saveStartedGuide(bool? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConfig.localStartedGuide.key, val ?? false);
    return null;
  }

  /// Save First Screen
  static Future<bool?> saveFirstScreen(bool? val) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConfig.localFirstScreen.key, val ?? true);
    return null;
  }

  // ignore: strict_raw_type
  static Future<List<Printer>> getPrinters() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localPrintersData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => Printer.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  static Future<List<Label>> getLabels() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localLabelsData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => Label.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  static Future<Business?> getBusiness() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localBusinessData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData);
        final Business data = Business.fromJson(decode as Map<String, dynamic>);
        return data;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<List<MultipleHome>> getBusinessMultipleHome() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localBusinessMultipleHomeData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData) as List;
        final data = decode.map((e) => MultipleHome.fromJson(e as Map<String, dynamic>)).toList();
        return data;
      } catch (e) {
        return [];
      }
    }
    return [];
  }

  static Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localUserData.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData);
        final User data = User.fromJson(decode as Map<String, dynamic>);
        return data;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localLanguageData.key);

    if (localData != null) {
      try {
        return localData;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<String?> getRoleIdBusiness() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConfig.localRoleIdBusiness.key);
  }

  /// Get Started Guide
  static Future<bool?> getStartedGuide() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getBool(AppConfig.localStartedGuide.key);
    if (localData != null) {
      try {
        return localData;
      } catch (e) {
        return false;
      }
    }
    return false;
  }

  /// Get First Screen
  static Future<bool?> getFirstScreen() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getBool(AppConfig.localFirstScreen.key);
    if (localData != null) {
      try {
        return localData;
      } catch (e) {
        return true;
      }
    }
    return true;
  }

  static Future<bool?> getFirsrMultiHome() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getBool(AppConfig.localFirstMultiHomeScreen.key);
    if (localData != null) {
      try {
        return localData;
      } catch (e) {
        return false;
      }
    }
    return null;
  }

  static Future<List<ChatMessage>> getAISupport() async {
    final prefs = await SharedPreferences.getInstance();

    if (!prefs.containsKey(AppConfig.localAISupport.key)) return [];

    try {
      final String? storedData = prefs.getString(AppConfig.localAISupport.key);
      if (storedData == null) return [];

      final data = json.decode(storedData);
      final expiryTime = data['expiryTime'] as int;

      final currentTime = DateTime.now().millisecondsSinceEpoch;

      if (currentTime > expiryTime) {
        await prefs.remove(AppConfig.localAISupport.key);
        return [];
      }
      final List<ChatMessage> value = ((data['value']) as List).map((e) => ChatMessage.fromJson(e as Map<String, dynamic>)).toList();
      return value;
    } catch (e) {
      AppLog.e('Lỗi khi đọc dữ liệu: $e');
      await prefs.remove(AppConfig.localAISupport.key);
      return [];
    }
  }

  static Future<bool?> getFirsrNewHome() async {
    final prefs = await SharedPreferences.getInstance();

    if (!prefs.containsKey(AppConfig.localFirstNewHomeScreen.key)) return null;

    try {
      final String? storedData = prefs.getString(AppConfig.localFirstNewHomeScreen.key);
      if (storedData == null) return null;

      final data = json.decode(storedData);
      if (data is Map<String, dynamic> && data.containsKey('expiryTime')) {
        final expiryTime = data['expiryTime'] as int;
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        if (currentTime > expiryTime) {
          // Đã hết hạn, xóa dữ liệu và trả về false
          await prefs.remove(AppConfig.localFirstNewHomeScreen.key);
          return false;
        }

        return data['value'] as bool? ?? false;
      }
      return null;
    } catch (e) {
      await prefs.remove(AppConfig.localFirstNewHomeScreen.key);
      return false;
    }
  }

  static Future<bool?> getHideProPlan() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getBool(AppConfig.localHideProPlan.key);
    if (localData != null) {
      try {
        return localData;
      } catch (e) {
        return false;
      }
    }
    return null;
  }

  static Future<ReceiptTemplate?> getCustomerReceipt() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localCustomerReceipt.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData);
        final ReceiptTemplate data = ReceiptTemplate.fromJson(decode as Map<String, dynamic>);
        return data;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static Future<ReceiptTemplate?> getKitchenReceipt() async {
    final prefs = await SharedPreferences.getInstance();
    final localData = prefs.getString(AppConfig.localKitchenReceipt.key);

    if (localData != null) {
      try {
        final decode = json.decode(localData);
        final ReceiptTemplate data = ReceiptTemplate.fromJson(decode as Map<String, dynamic>);
        return data;
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// DELETE
  static Future<void> deleteLocal(String id) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(id);
  }

  static Future<void> userLogout() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs
      ..remove(AppConfig.localAccessToken.key)
      ..remove(AppConfig.localRefreshToken.key)
      ..remove(AppConfig.localExpiresToken.key)
      // ..remove(AppConfig.localFCMTopicBID.key)
      // ..remove(AppConfig.localFCMTopicBIDUID.key)
      ..remove(AppConfig.localPrintersData.key)
      ..remove(AppConfig.localUserPermission.key)
      ..remove(AppConfig.localRoleIdBusiness.key)
      ..remove(AppConfig.localLabelsData.key)
      ..remove(AppConfig.localLabelsOfferData.key)
      ..remove(AppConfig.localBusinessData.key)
      ..remove(AppConfig.localBusinessMultipleHomeData.key)
      ..remove(AppConfig.localStartedGuide.key)
      // ..remove(AppConfig.localFirstMultiHomeScreen.key)
      // ..remove(AppConfig.localFirstNewHomeScreen.key)
      ..remove(AppConfig.localHideProPlan.key)
      ..remove(AppConfig.localFeatures.key)
      ..remove(AppConfig.localAISupport.key)
      ..remove(AppConfig.localHasShowBubble.key)
      ..remove(AppConfig.localFirstScreen.key)
      ..remove(AppConfig.localCustomerReceipt.key)
      ..remove(AppConfig.localKitchenReceipt.key);
    // prefs.clear();
  }
}
